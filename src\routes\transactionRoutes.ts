import { Router } from "express";
import { TransactionController } from "../controllers/transactionController";
import { ProgressTrackingController } from "../controllers/progressTrackingController";
import { validateBody, validateQuery } from "../middleware/validation";
import { authenticate, requireAccountOfficer } from "../middleware/auth";
import { validatePersonalInfoByLoanType } from "../middleware/dynamicValidation";
import { checkLoanEligibilityOnPersonalInfoUpdate } from "../middleware/loanEligibility";
import {
  personalInfoSchema,
  nextOfKinSchema,
  loanInfoSchema,
  disbursementSchema,
  paginationSchema,
  transactionFiltersSchema,
  loanTypeSelectionSchema,
  loanEligibilitySchema,
  markLoanCompletedSchema,
  markLoanRepaidSchema,
  transferTransactionOwnershipSchema,
  bulkTransferTransactionsSchema,
  transferableTransactionsQuerySchema,
} from "../utils/validation";

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/transactions/loan-types:
 *   get:
 *     summary: Get available loan types
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of available loan types
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/loan-types", TransactionController.getLoanTypes);

/**
 * @swagger
 * /api/v1/transactions:
 *   post:
 *     summary: Create a new transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: Transaction created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   get:
 *     summary: Get transactions with pagination and filters
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [DRAFT, SUBMITTED, UNDER_REVIEW, APPROVED, REJECTED, SENT_BACK]
 *         description: Filter by transaction status
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by transaction ID, name, or email
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering (YYYY-MM-DD)
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: List of transactions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/",
  requireAccountOfficer,
  TransactionController.createTransaction
);
router.get(
  "/",
  validateQuery(paginationSchema.merge(transactionFiltersSchema)),
  TransactionController.getTransactions
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}:
 *   get:
 *     summary: Get a specific transaction by ID
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Transaction details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   delete:
 *     summary: Delete a transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Transaction deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/:transactionId", TransactionController.getTransaction);
router.get(
  "/:transactionId/status",
  TransactionController.getTransactionStatus
);
router.get(
  "/:transactionId/validate",
  TransactionController.validateTransaction
);
router.delete(
  "/:transactionId",
  requireAccountOfficer,
  TransactionController.deleteTransaction
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/loan-type:
 *   patch:
 *     summary: Set loan type for a transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - loanType
 *             properties:
 *               loanType:
 *                 type: string
 *                 description: Type of loan
 *     responses:
 *       200:
 *         description: Loan type updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/loan-type",
  requireAccountOfficer,
  validateBody(loanTypeSelectionSchema),
  TransactionController.setLoanType
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/preview:
 *   get:
 *     summary: Get transaction preview
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Transaction preview data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/:transactionId/preview",
  TransactionController.getTransactionPreview
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/pdf:
 *   get:
 *     summary: Download transaction PDF
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: PDF file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/:transactionId/pdf", TransactionController.downloadTransactionPDF);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/csv:
 *   get:
 *     summary: Download transaction data as CSV
 *     description: |
 *       Export comprehensive transaction data in CSV format for analysis and reporting.
 *
 *       **Included Data:**
 *       - Transaction information (ID, status, dates, loan type)
 *       - Personal information (name, contact details, BVN, NIN)
 *       - Employment information (organization, IPPIS, employment date)
 *       - Loan details (amount, tenor, repayment mode, purpose)
 *       - Disbursement information (account details)
 *       - Document metadata (names, types, sizes, upload dates)
 *       - Approval history (stages, actions, approvers, comments)
 *       - System information (created by, timestamps)
 *
 *       **Access Control:**
 *       - Account Officers can only export their own transactions
 *       - Other roles can export transactions based on their permissions
 *
 *       **Use Cases:**
 *       - Data analysis and reporting
 *       - Audit trail documentation
 *       - Integration with external systems
 *       - Backup and archival purposes
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *         example: "687a4a82b2114af448562d15"
 *     responses:
 *       200:
 *         description: CSV file with transaction data
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *               description: CSV file containing comprehensive transaction data
 *         headers:
 *           Content-Disposition:
 *             description: Attachment with filename including timestamp
 *             schema:
 *               type: string
 *               example: 'attachment; filename="transaction-TXN-2024-001-20240118-143022.csv"'
 *           Content-Type:
 *             description: MIME type for CSV files
 *             schema:
 *               type: string
 *               example: 'text/csv'
 *           Content-Length:
 *             description: Size of the CSV file in bytes
 *             schema:
 *               type: integer
 *               example: 2048
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Transaction not found"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Access denied to this transaction
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error - Failed to generate CSV
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/:transactionId/csv", TransactionController.downloadTransactionCSV);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/download-all:
 *   get:
 *     summary: Download all transaction files as ZIP
 *     description: Downloads a ZIP archive containing all transaction documents and the transaction PDF
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: ZIP file containing all transaction documents and PDF
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment with filename
 *             schema:
 *               type: string
 *               example: 'attachment; filename="TXN17499886400668092.zip"'
 *           Content-Length:
 *             description: Size of the ZIP file in bytes
 *             schema:
 *               type: integer
 *       404:
 *         description: Transaction not found or no content available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error - Failed to create ZIP archive
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/:transactionId/download-all", TransactionController.downloadAll);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/personal-info:
 *   patch:
 *     summary: Update personal information for a transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PersonalInfoRequest'
 *     responses:
 *       200:
 *         description: Personal information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/personal-info",
  requireAccountOfficer,
  validatePersonalInfoByLoanType,
  TransactionController.updatePersonalInfo
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/next-of-kin:
 *   patch:
 *     summary: Update next of kin information for a transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - relationship
 *               - phoneNumber
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               relationship:
 *                 type: string
 *               phoneNumber:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               address:
 *                 type: string
 *     responses:
 *       200:
 *         description: Next of kin information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/next-of-kin",
  requireAccountOfficer,
  validateBody(nextOfKinSchema),
  TransactionController.updateNextOfKin
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/loan-info:
 *   patch:
 *     summary: Update loan information for a transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoanInfoRequest'
 *     responses:
 *       200:
 *         description: Loan information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/loan-info",
  requireAccountOfficer,
  validateBody(loanInfoSchema),
  TransactionController.updateLoanInfo
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/disbursement:
 *   patch:
 *     summary: Update disbursement information for a transaction
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accountName
 *               - accountNumber
 *               - bankName
 *             properties:
 *               accountName:
 *                 type: string
 *               accountNumber:
 *                 type: string
 *               bankName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Disbursement information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/disbursement",
  requireAccountOfficer,
  validateBody(disbursementSchema),
  TransactionController.updateDisbursement
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/submit:
 *   post:
 *     summary: Submit a transaction for approval
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Transaction submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Bad request - Transaction not ready for submission
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Account Officer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/:transactionId/submit",
  requireAccountOfficer,
  TransactionController.submitTransaction
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/approval-history:
 *   get:
 *     summary: Get detailed approval history for a specific transaction
 *     description: |
 *       Returns a chronological approval history for a specific transaction showing the complete approval workflow journey.
 *       Displays each stage, approver details, actions taken, comments, and timestamps in a formatted structure.
 *
 *       **Features:**
 *       - Complete approval workflow journey
 *       - Human-readable stage names
 *       - Approver details with names and roles
 *       - Chronological order (oldest to newest)
 *       - Formatted dates for display
 *       - Comments and action details
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Approval history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Approval history retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactionId:
 *                       type: string
 *                       description: The transaction ID
 *                       example: "TXN-12345"
 *                     approvalHistory:
 *                       type: array
 *                       description: Chronological list of approval actions
 *                       items:
 *                         type: object
 *                         properties:
 *                           stage:
 *                             type: string
 *                             enum: [ACCOUNT_OFFICER, SUPERVISOR, HEAD_CONSUMER_LENDING, HEAD_RISK_MANAGEMENT, MANAGING_DIRECTOR, ACCOUNTANT]
 *                             description: Approval stage enum value
 *                             example: "SUPERVISOR"
 *                           stageName:
 *                             type: string
 *                             description: Human-readable stage name
 *                             example: "Supervisor Review"
 *                           action:
 *                             type: string
 *                             enum: [APPROVED, REJECTED, SENT_BACK, DISBURSED, SUBMITTED]
 *                             description: Action taken by the approver
 *                             example: "APPROVED"
 *                           approver:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 description: Approver user ID
 *                                 example: "user-123"
 *                               name:
 *                                 type: string
 *                                 description: Full name of the approver
 *                                 example: "Jane Supervisor"
 *                               role:
 *                                 type: string
 *                                 enum: [SUPER_ADMIN, ACCOUNT_OFFICER, SUPERVISOR, HEAD_CONSUMER_LENDING, HEAD_RISK_MANAGEMENT, MANAGING_DIRECTOR, ACCOUNTANT]
 *                                 description: Role of the approver
 *                                 example: "SUPERVISOR"
 *                           comments:
 *                             type: string
 *                             nullable: true
 *                             description: Comments provided by the approver
 *                             example: "Application looks good, approved for next stage"
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                             description: ISO timestamp of the action
 *                             example: "2025-06-15T10:30:00Z"
 *                           formattedDate:
 *                             type: string
 *                             description: Human-readable date format
 *                             example: "6/15/2025"
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/:transactionId/approval-history",
  TransactionController.getApprovalHistory
);

/**
 * @swagger
 * /api/v1/transactions/check-loan-eligibility:
 *   post:
 *     summary: Check loan eligibility by BVN
 *     description: |
 *       Check if a user is eligible for a new loan based on their BVN.
 *       This endpoint validates the single active loan policy and returns
 *       detailed information about eligibility status.
 *
 *       **Business Rules:**
 *       - Only one active loan per BVN is allowed
 *       - Active loans include APPROVED and DISBURSED status
 *       - Users become eligible after their current loan tenor expires
 *       - COMPLETED loans don't affect eligibility
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bvn
 *             properties:
 *               bvn:
 *                 type: string
 *                 pattern: '^[0-9]{11}$'
 *                 description: Bank Verification Number (11 digits)
 *                 example: "***********"
 *     responses:
 *       200:
 *         description: Eligibility check completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "You are eligible for a new loan"
 *                 data:
 *                   type: object
 *                   properties:
 *                     isEligible:
 *                       type: boolean
 *                       example: false
 *                     reason:
 *                       type: string
 *                       example: "You have an active loan (TXN-2024-001) that expires on 12/31/2024. You can apply for a new loan after this date."
 *                     activeLoan:
 *                       type: object
 *                       properties:
 *                         transactionId:
 *                           type: string
 *                           example: "TXN-2024-001"
 *                         status:
 *                           type: string
 *                           example: "DISBURSED"
 *                         disbursedAt:
 *                           type: string
 *                           format: date-time
 *                         loanTenor:
 *                           type: integer
 *                           example: 12
 *                         expectedCompletionDate:
 *                           type: string
 *                           format: date-time
 *                         requestedAmount:
 *                           type: number
 *                           example: 500000
 *                     eligibilityDate:
 *                       type: string
 *                       format: date-time
 *                       description: Date when user becomes eligible for new loan
 *       400:
 *         description: Invalid BVN or validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/check-loan-eligibility",
  validateBody(loanEligibilitySchema),
  TransactionController.checkLoanEligibility
);

/**
 * @swagger
 * /api/v1/transactions/loan-history/{bvn}:
 *   get:
 *     summary: Get loan history by BVN
 *     description: |
 *       Retrieve complete loan history for a user based on their BVN.
 *       Shows all loans (except DRAFT status) with their current status,
 *       amounts, tenors, and completion information.
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: bvn
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[0-9]{11}$'
 *         description: Bank Verification Number (11 digits)
 *         example: "***********"
 *     responses:
 *       200:
 *         description: Loan history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Loan history retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       transactionId:
 *                         type: string
 *                       status:
 *                         type: string
 *                       requestedAmount:
 *                         type: number
 *                       loanTenor:
 *                         type: integer
 *                       disbursedAt:
 *                         type: string
 *                         format: date-time
 *                       completedAt:
 *                         type: string
 *                         format: date-time
 *                       expectedCompletionDate:
 *                         type: string
 *                         format: date-time
 *                       isActive:
 *                         type: boolean
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *       400:
 *         description: Invalid BVN format
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/loan-history/:bvn", TransactionController.getLoanHistory);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/mark-completed:
 *   patch:
 *     summary: Mark a loan as completed
 *     description: |
 *       Manually mark a loan as completed. This is useful for cases where:
 *       - Loan is paid off early
 *       - Loan tenor has expired
 *       - Administrative completion is required
 *
 *       Only APPROVED or DISBURSED loans can be marked as completed.
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               completionReason:
 *                 type: string
 *                 description: Reason for marking as completed
 *                 example: "PAID_OFF_EARLY"
 *     responses:
 *       200:
 *         description: Loan marked as completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid request or loan cannot be completed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/mark-completed",
  validateBody(markLoanCompletedSchema),
  TransactionController.markLoanCompleted
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/mark-repaid:
 *   patch:
 *     summary: Mark a loan as repaid
 *     description: |
 *       Mark a loan as repaid when the user has paid back their loan.
 *       This endpoint is used when a user has successfully repaid their loan
 *       and the loan should be marked as completed with repayment status.
 *
 *       **Business Rules:**
 *       - Only DISBURSED loans can be marked as repaid
 *       - Sets both repaidAt and completedAt timestamps
 *       - Changes status to LOAN_REPAID
 *       - Makes user eligible for new loans immediately
 *
 *       **Use Cases:**
 *       - User completes loan repayment
 *       - Administrative loan closure after payment
 *       - Bulk loan repayment processing
 *       - Customer service loan updates
 *     tags: [Loan Eligibility]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID (internal MongoDB ObjectId)
 *         example: "507f1f77bcf86cd799439011"
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               repaymentMethod:
 *                 type: string
 *                 description: Method used for repayment
 *                 example: "Bank Transfer"
 *                 enum:
 *                   - "Bank Transfer"
 *                   - "Cash"
 *                   - "Cheque"
 *                   - "Online Payment"
 *                   - "Debit Card"
 *                   - "Mobile Money"
 *               repaymentReference:
 *                 type: string
 *                 description: Reference number for the repayment transaction
 *                 example: "TXN-REF-2024-001"
 *               notes:
 *                 type: string
 *                 description: Additional notes about the repayment
 *                 example: "Full loan amount repaid via bank transfer"
 *           example:
 *             repaymentMethod: "Bank Transfer"
 *             repaymentReference: "TXN-REF-2024-001"
 *             notes: "Full loan amount repaid via bank transfer"
 *     responses:
 *       200:
 *         description: Loan marked as repaid successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "Loan marked as repaid successfully"
 *               data: {}
 *       400:
 *         description: Invalid request or loan cannot be marked as repaid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               already_repaid:
 *                 summary: Loan already repaid
 *                 value:
 *                   success: false
 *                   message: "Loan is already marked as repaid"
 *               invalid_status:
 *                 summary: Invalid loan status
 *                 value:
 *                   success: false
 *                   message: "Only disbursed loans can be marked as repaid"
 *               already_completed:
 *                 summary: Loan already completed
 *                 value:
 *                   success: false
 *                   message: "Loan is already completed"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Transaction not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.patch(
  "/:transactionId/mark-repaid",
  validateBody(markLoanRepaidSchema),
  TransactionController.markLoanRepaid
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/progress:
 *   get:
 *     summary: Get transaction progress tracking
 *     description: |
 *       Get detailed progress tracking information for a transaction showing workflow stages.
 *
 *       **Workflow Stages:**
 *       1. **Application Started** - Transaction created (always completed)
 *       2. **Submitted** - Application submitted for review
 *       3. **Under Review** - In approval process
 *       4. **Approved** - Ready for disbursement
 *       5. **Disbursed** - Loan disbursed to customer
 *
 *       **Stage Status:**
 *       - `completed` - Stage is finished
 *       - `active` - Currently at this stage
 *       - `pending` - Not yet reached this stage
 *
 *       **Access Control:**
 *       - Account Officers can only see their own transactions
 *       - Other roles can see transactions based on their permissions
 *     tags: [Progress Tracking]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID (MongoDB ObjectId)
 *         example: "687a4a82b2114af448562d15"
 *     responses:
 *       200:
 *         description: Transaction progress retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Transaction progress retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactionId:
 *                       type: string
 *                       description: Internal transaction ID
 *                       example: "687a4a82b2114af448562d15"
 *                     transactionNumber:
 *                       type: string
 *                       description: Human-readable transaction number
 *                       example: "TXN-2024-001"
 *                     currentStatus:
 *                       type: string
 *                       enum: [DRAFT, SUBMITTED, IN_PROGRESS, APPROVED, REJECTED, SENT_BACK, DISBURSED, COMPLETED, LOAN_REPAID]
 *                       description: Current transaction status
 *                       example: "IN_PROGRESS"
 *                     currentStage:
 *                       type: string
 *                       enum: [ACCOUNT_OFFICER, SUPERVISOR, HEAD_CONSUMER_LENDING, HEAD_RISK_MANAGEMENT, MANAGING_DIRECTOR, ACCOUNTANT]
 *                       description: Current approval stage
 *                       example: "HEAD_CONSUMER_LENDING"
 *                     progressPercentage:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 100
 *                       description: Overall progress percentage
 *                       example: 60
 *                     stages:
 *                       type: array
 *                       description: List of workflow stages with their status
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: Stage identifier
 *                             example: "under_review"
 *                           name:
 *                             type: string
 *                             description: Human-readable stage name
 *                             example: "Under Review"
 *                           status:
 *                             type: string
 *                             enum: [completed, active, pending]
 *                             description: Current status of this stage
 *                             example: "active"
 *                           timestamp:
 *                             type: string
 *                             format: date-time
 *                             nullable: true
 *                             description: When this stage was reached (null if not reached)
 *                             example: "2024-01-16T09:00:00Z"
 *                           order:
 *                             type: number
 *                             description: Stage order in workflow
 *                             example: 3
 *                     statusLabel:
 *                       type: string
 *                       description: Human-readable status label
 *                       example: "Under Review"
 *                     nextAction:
 *                       type: string
 *                       nullable: true
 *                       description: Description of next expected action
 *                       example: "Waiting for Head Consumer Lending approval"
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 *                       description: When transaction was last updated
 *                       example: "2024-01-16T09:00:00Z"
 *             example:
 *               success: true
 *               message: "Transaction progress retrieved successfully"
 *               data:
 *                 transactionId: "687a4a82b2114af448562d15"
 *                 transactionNumber: "TXN-2024-001"
 *                 currentStatus: "IN_PROGRESS"
 *                 currentStage: "HEAD_CONSUMER_LENDING"
 *                 progressPercentage: 60
 *                 stages:
 *                   - id: "draft"
 *                     name: "Application Started"
 *                     status: "completed"
 *                     timestamp: "2024-01-15T10:00:00Z"
 *                     order: 1
 *                   - id: "submitted"
 *                     name: "Submitted"
 *                     status: "completed"
 *                     timestamp: "2024-01-15T14:30:00Z"
 *                     order: 2
 *                   - id: "under_review"
 *                     name: "Under Review"
 *                     status: "active"
 *                     timestamp: "2024-01-16T09:00:00Z"
 *                     order: 3
 *                   - id: "approved"
 *                     name: "Approved"
 *                     status: "pending"
 *                     timestamp: null
 *                     order: 4
 *                   - id: "disbursed"
 *                     name: "Disbursed"
 *                     status: "pending"
 *                     timestamp: null
 *                     order: 5
 *                 statusLabel: "Under Review"
 *                 nextAction: "Waiting for Head Consumer Lending approval"
 *                 lastUpdated: "2024-01-16T09:00:00Z"
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Transaction not found"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Access denied to this transaction
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/:transactionId/progress",
  ProgressTrackingController.getTransactionProgress
);

/**
 * @swagger
 * /api/v1/transactions/progress/bulk:
 *   post:
 *     summary: Get progress for multiple transactions (bulk)
 *     description: |
 *       Get progress tracking information for multiple transactions at once.
 *       This endpoint is optimized for dashboard views and transaction lists
 *       where you need progress information for many transactions.
 *
 *       **Use Cases:**
 *       - Transaction list pages with progress indicators
 *       - Dashboard widgets showing multiple transaction statuses
 *       - Bulk progress updates for reporting
 *
 *       **Performance:**
 *       - Maximum 50 transactions per request
 *       - Parallel processing for better performance
 *       - Returns both successful and failed results
 *
 *       **Access Control:**
 *       - Account Officers can only see their own transactions
 *       - Other roles see transactions based on their permissions
 *       - Failed transactions (access denied) are returned in the failed array
 *     tags: [Progress Tracking]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - transactionIds
 *             properties:
 *               transactionIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 minItems: 1
 *                 maxItems: 50
 *                 description: Array of transaction IDs to get progress for
 *                 example: ["687a4a82b2114af448562d15", "687a4a82b2114af448562d16"]
 *           example:
 *             transactionIds: ["687a4a82b2114af448562d15", "687a4a82b2114af448562d16", "687a4a82b2114af448562d17"]
 *     responses:
 *       200:
 *         description: Bulk progress retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Progress retrieved for 2/3 transactions"
 *                 data:
 *                   type: object
 *                   properties:
 *                     successful:
 *                       type: array
 *                       description: Successfully retrieved transaction progress
 *                       items:
 *                         type: object
 *                         properties:
 *                           transactionId:
 *                             type: string
 *                             example: "687a4a82b2114af448562d15"
 *                           transactionNumber:
 *                             type: string
 *                             example: "TXN-2024-001"
 *                           currentStatus:
 *                             type: string
 *                             example: "IN_PROGRESS"
 *                           currentStage:
 *                             type: string
 *                             example: "HEAD_CONSUMER_LENDING"
 *                           progressPercentage:
 *                             type: number
 *                             example: 60
 *                           stages:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                 name:
 *                                   type: string
 *                                 status:
 *                                   type: string
 *                                   enum: [completed, active, pending]
 *                                 timestamp:
 *                                   type: string
 *                                   format: date-time
 *                                   nullable: true
 *                                 order:
 *                                   type: number
 *                           statusLabel:
 *                             type: string
 *                             example: "Under Review"
 *                           nextAction:
 *                             type: string
 *                             nullable: true
 *                             example: "Waiting for Head Consumer Lending approval"
 *                           lastUpdated:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-16T09:00:00Z"
 *                     failed:
 *                       type: array
 *                       description: Transactions that failed to retrieve progress
 *                       items:
 *                         type: object
 *                         properties:
 *                           transactionId:
 *                             type: string
 *                             example: "687a4a82b2114af448562d17"
 *                           error:
 *                             type: string
 *                             example: "Transaction not found"
 *                     summary:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: number
 *                           description: Total number of transactions requested
 *                           example: 3
 *                         successful:
 *                           type: number
 *                           description: Number of successfully retrieved transactions
 *                           example: 2
 *                         failed:
 *                           type: number
 *                           description: Number of failed transactions
 *                           example: 1
 *             example:
 *               success: true
 *               message: "Progress retrieved for 2/3 transactions"
 *               data:
 *                 successful:
 *                   - transactionId: "687a4a82b2114af448562d15"
 *                     transactionNumber: "TXN-2024-001"
 *                     currentStatus: "IN_PROGRESS"
 *                     currentStage: "HEAD_CONSUMER_LENDING"
 *                     progressPercentage: 60
 *                     statusLabel: "Under Review"
 *                     nextAction: "Waiting for Head Consumer Lending approval"
 *                     lastUpdated: "2024-01-16T09:00:00Z"
 *                   - transactionId: "687a4a82b2114af448562d16"
 *                     transactionNumber: "TXN-2024-002"
 *                     currentStatus: "APPROVED"
 *                     currentStage: "ACCOUNTANT"
 *                     progressPercentage: 80
 *                     statusLabel: "Approved"
 *                     nextAction: "Ready for disbursement"
 *                     lastUpdated: "2024-01-17T11:15:00Z"
 *                 failed:
 *                   - transactionId: "687a4a82b2114af448562d17"
 *                     error: "Transaction not found"
 *                 summary:
 *                   total: 3
 *                   successful: 2
 *                   failed: 1
 *       400:
 *         description: Invalid request - Bad transaction IDs array
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               empty_array:
 *                 summary: Empty transaction IDs array
 *                 value:
 *                   success: false
 *                   message: "Transaction IDs array is required"
 *               too_many:
 *                 summary: Too many transaction IDs
 *                 value:
 *                   success: false
 *                   message: "Maximum 50 transactions can be processed at once"
 *               invalid_format:
 *                 summary: Invalid request format
 *                 value:
 *                   success: false
 *                   message: "transactionIds must be an array"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/progress/bulk",
  ProgressTrackingController.getBulkTransactionProgress
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/transfer-ownership:
 *   patch:
 *     summary: Transfer ownership of a transaction to another account officer
 *     description: |
 *       Transfer ownership of a transaction from one account officer to another.
 *       This is useful when an account officer leaves the company or when
 *       workload needs to be redistributed.
 *
 *       **Access Control:**
 *       - Only supervisors and above can transfer transaction ownership
 *       - Transactions must be in DRAFT, SENT_BACK, SUBMITTED, IN_PROGRESS, or APPROVED status to be transferable
 *       - New owner must be an active account officer
 *
 *       **Use Cases:**
 *       - Account officer leaving the company
 *       - Workload redistribution
 *       - Temporary assignment changes
 *       - Emergency coverage
 *
 *       **Audit Trail:**
 *       - All transfers are logged for audit purposes
 *       - New owner receives notification about the transfer
 *       - Transfer reason is recorded if provided
 *     tags: [Transaction Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID to transfer
 *         example: "687a4a82b2114af448562d15"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newOwnerId
 *             properties:
 *               newOwnerId:
 *                 type: string
 *                 description: ID of the new owner (must be an account officer)
 *                 example: "687a4a82b2114af448562d20"
 *               transferReason:
 *                 type: string
 *                 description: Optional reason for the transfer
 *                 example: "Account officer leaving company"
 *           example:
 *             newOwnerId: "687a4a82b2114af448562d20"
 *             transferReason: "Account officer leaving company"
 *     responses:
 *       200:
 *         description: Transaction ownership transferred successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Transaction ownership transferred successfully"
 *       400:
 *         description: Invalid request or transfer not allowed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalid_status:
 *                 summary: Transaction not in transferable status
 *                 value:
 *                   success: false
 *                   message: "Transaction cannot be transferred. Current status: DISBURSED. Only draft, sent-back, submitted, in-progress, or approved transactions can be transferred."
 *               same_owner:
 *                 summary: Trying to transfer to same owner
 *                 value:
 *                   success: false
 *                   message: "Transaction is already owned by this user"
 *               inactive_user:
 *                 summary: New owner is inactive
 *                 value:
 *                   success: false
 *                   message: "Cannot transfer to an inactive user"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Access denied. Only supervisors and above can transfer transaction ownership."
 *       404:
 *         description: Transaction or new owner not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               transaction_not_found:
 *                 summary: Transaction not found
 *                 value:
 *                   success: false
 *                   message: "Transaction not found"
 *               user_not_found:
 *                 summary: New owner not found
 *                 value:
 *                   success: false
 *                   message: "New owner not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:transactionId/transfer-ownership",
  validateBody(transferTransactionOwnershipSchema),
  TransactionController.transferTransactionOwnership
);

/**
 * @swagger
 * /api/v1/transactions/transferable:
 *   get:
 *     summary: Get transactions that can be transferred
 *     description: |
 *       Get a list of transactions that are eligible for ownership transfer.
 *       Only transactions in DRAFT, SENT_BACK, SUBMITTED, IN_PROGRESS, or APPROVED status can be transferred.
 *
 *       **Access Control:**
 *       - Only supervisors and above can view transferable transactions
 *
 *       **Query Parameters:**
 *       - `userId` (optional): Filter by specific user to see only their transferable transactions
 *
 *       **Use Cases:**
 *       - View all transactions that need reassignment
 *       - Check specific user's transferable transactions before deactivation
 *       - Workload analysis and redistribution planning
 *     tags: [Transaction Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: false
 *         schema:
 *           type: string
 *         description: Filter by specific user ID
 *         example: "687a4a82b2114af448562d18"
 *     responses:
 *       200:
 *         description: Transferable transactions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Transferable transactions retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Transaction ID
 *                         example: "687a4a82b2114af448562d15"
 *                       transactionId:
 *                         type: string
 *                         description: Human-readable transaction ID
 *                         example: "TXN-2024-001"
 *                       status:
 *                         type: string
 *                         enum: [DRAFT, SENT_BACK, SUBMITTED, IN_PROGRESS, APPROVED]
 *                         example: "DRAFT"
 *                       loanType:
 *                         type: string
 *                         nullable: true
 *                         example: "CONSUMER_LOAN_PUBLIC"
 *                       requestedAmount:
 *                         type: number
 *                         nullable: true
 *                         example: 50000
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T10:00:00Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-15T14:30:00Z"
 *                       owner:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "687a4a82b2114af448562d18"
 *                           name:
 *                             type: string
 *                             example: "John Doe"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *                           isActive:
 *                             type: boolean
 *                             example: false
 *                       customerName:
 *                         type: string
 *                         nullable: true
 *                         description: Customer name if available
 *                         example: "Jane Smith"
 *                       phoneNumber:
 *                         type: string
 *                         nullable: true
 *                         example: "+1234567890"
 *                       bvn:
 *                         type: string
 *                         nullable: true
 *                         example: "***********"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Access denied. Only supervisors and above can view transferable transactions."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/transferable",
  validateQuery(transferableTransactionsQuerySchema),
  TransactionController.getTransferableTransactions
);

/**
 * @swagger
 * /api/v1/transactions/bulk-transfer:
 *   post:
 *     summary: Bulk transfer transactions from one user to another
 *     description: |
 *       Transfer all transferable transactions from one account officer to another.
 *       This is particularly useful when an account officer is leaving the company
 *       and all their pending work needs to be reassigned.
 *
 *       **Access Control:**
 *       - Only supervisors and above can perform bulk transfers
 *
 *       **Process:**
 *       - Finds all DRAFT, SENT_BACK, SUBMITTED, IN_PROGRESS, and APPROVED transactions owned by the source user
 *       - Transfers each transaction to the target user
 *       - Sends notifications to the new owner
 *       - Logs all transfers for audit purposes
 *
 *       **Use Cases:**
 *       - Account officer leaving company
 *       - Department restructuring
 *       - Emergency workload redistribution
 *       - Temporary coverage assignments
 *     tags: [Transaction Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fromUserId
 *               - toUserId
 *             properties:
 *               fromUserId:
 *                 type: string
 *                 description: ID of the user to transfer transactions from
 *                 example: "687a4a82b2114af448562d18"
 *               toUserId:
 *                 type: string
 *                 description: ID of the user to transfer transactions to (must be an account officer)
 *                 example: "687a4a82b2114af448562d20"
 *               transferReason:
 *                 type: string
 *                 description: Optional reason for the bulk transfer
 *                 example: "Account officer leaving company - workload redistribution"
 *           example:
 *             fromUserId: "687a4a82b2114af448562d18"
 *             toUserId: "687a4a82b2114af448562d20"
 *             transferReason: "Account officer leaving company - workload redistribution"
 *     responses:
 *       200:
 *         description: Bulk transfer completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully transferred 5 transaction(s)"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transferredCount:
 *                       type: number
 *                       description: Number of transactions transferred
 *                       example: 5
 *                     transactionIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: IDs of transferred transactions
 *                       example: ["687a4a82b2114af448562d15", "687a4a82b2114af448562d16"]
 *             example:
 *               success: true
 *               message: "Successfully transferred 5 transaction(s)"
 *               data:
 *                 transferredCount: 5
 *                 transactionIds: ["687a4a82b2114af448562d15", "687a4a82b2114af448562d16", "687a4a82b2114af448562d17"]
 *       400:
 *         description: Invalid request or transfer not allowed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               same_user:
 *                 summary: Trying to transfer to same user
 *                 value:
 *                   success: false
 *                   message: "Cannot transfer transactions to the same user"
 *               invalid_target:
 *                 summary: Target user is not an account officer
 *                 value:
 *                   success: false
 *                   message: "Target user must be an account officer"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Access denied. Only supervisors and above can perform bulk transfers."
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               from_user_not_found:
 *                 summary: Source user not found
 *                 value:
 *                   success: false
 *                   message: "Source user not found"
 *               to_user_not_found:
 *                 summary: Target user not found
 *                 value:
 *                   success: false
 *                   message: "Target user not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/bulk-transfer",
  validateBody(bulkTransferTransactionsSchema),
  TransactionController.bulkTransferTransactions
);

export default router;
