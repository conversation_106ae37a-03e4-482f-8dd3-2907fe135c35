import { Response } from 'express';
import { NotificationService } from '../services/notificationService';
import { ResponseHandler } from '../utils/response';
import { AuthenticatedRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';

export class NotificationController {
  static getNotifications = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const unreadOnly = req.query.unreadOnly === 'true';

    const result = await NotificationService.getUserNotifications(userId, page, limit, unreadOnly);

    ResponseHandler.success(res, 'Notifications retrieved successfully', result);
  });

  static markAsRead = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { notificationId } = req.params;
    const userId = req.user!.id;

    await NotificationService.markAsRead(notificationId, userId);

    ResponseHandler.success(res, 'Notification marked as read');
  });

  static markAllAsRead = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;

    await NotificationService.markAllAsRead(userId);

    ResponseHandler.success(res, 'All notifications marked as read');
  });

  static deleteNotification = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { notificationId } = req.params;
    const userId = req.user!.id;

    await NotificationService.deleteNotification(notificationId, userId);

    ResponseHandler.success(res, 'Notification deleted successfully');
  });

  static getUnreadCount = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;

    const count = await NotificationService.getUnreadCount(userId);

    ResponseHandler.success(res, 'Unread count retrieved successfully', { count });
  });
}
