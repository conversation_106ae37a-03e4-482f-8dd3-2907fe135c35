# Additional endpoints for swagger.yaml - append these to the main file

    get:
      tags:
        - Transactions
      summary: Get transactions
      description: Retrieve transactions with filtering and pagination
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: status
          in: query
          schema:
            $ref: '#/components/schemas/TransactionStatus'
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Transactions retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          transactions:
                            type: array
                            items:
                              $ref: '#/components/schemas/Transaction'
                          total:
                            type: integer
                          page:
                            type: integer
                          limit:
                            type: integer

  /transactions/{transactionId}:
    get:
      tags:
        - Transactions
      summary: Get specific transaction
      description: Retrieve a specific transaction by ID
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Transaction retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Transaction'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      tags:
        - Transactions
      summary: Delete transaction
      description: Delete a draft transaction (Account Officer only)
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Transaction deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          description: Cannot delete non-draft transaction
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /transactions/{transactionId}/loan-type:
    patch:
      tags:
        - Loan Types
      summary: Set loan type for transaction
      description: Set the loan type for a transaction (Account Officer only)
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanTypeSelectionRequest'
            examples:
              consumer_public:
                summary: Consumer Loan Public
                value:
                  loanType: "CONSUMER_LOAN_PUBLIC"
              consumer_private:
                summary: Consumer Loan Private
                value:
                  loanType: "CONSUMER_LOAN_PRIVATE"
              sme_individual:
                summary: SME Individual
                value:
                  loanType: "SME_INDIVIDUAL"
              sme_corporate:
                summary: SME Corporate
                value:
                  loanType: "SME_CORPORATE"
      responses:
        '200':
          description: Loan type set successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: Invalid loan type or transaction cannot be edited
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /transactions/{transactionId}/personal-info:
    patch:
      tags:
        - Transactions
      summary: Update personal information
      description: Update personal information with dynamic validation based on loan type
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PersonalInfoRequest'
            examples:
              consumer_public:
                summary: Consumer Loan Public Example
                value:
                  firstName: "John"
                  lastName: "Doe"
                  gender: "MALE"
                  maritalStatus: "SINGLE"
                  dateOfBirth: "1990-05-15"
                  email: "<EMAIL>"
                  phoneNumber: "08012345678"
                  street: "123 Main Street"
                  city: "Lagos"
                  state: "Lagos"
                  postalCode: "100001"
                  organizationName: "Federal Ministry of Finance"
                  ippisNumber: "FMF123456"
                  employmentDate: "2020-01-15"
              sme_corporate:
                summary: SME Corporate Example
                value:
                  firstName: "Sarah"
                  lastName: "Adebayo"
                  gender: "FEMALE"
                  maritalStatus: "SINGLE"
                  dateOfBirth: "1988-04-12"
                  email: "<EMAIL>"
                  phoneNumber: "***********"
                  street: "321 Innovation Hub"
                  city: "Ibadan"
                  state: "Oyo"
                  postalCode: "200001"
                  businessName: "TechCorp Solutions Limited"
                  registrationNumber: "RC1234567"
                  employmentDate: "2019-09-15"
      responses:
        '200':
          description: Personal information updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: Validation failed or loan type not set
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /transactions/{transactionId}/next-of-kin:
    patch:
      tags:
        - Transactions
      summary: Update next of kin information
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NextOfKinRequest'
      responses:
        '200':
          description: Next of kin information updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /transactions/{transactionId}/loan-info:
    patch:
      tags:
        - Transactions
      summary: Update loan information
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanInfoRequest'
      responses:
        '200':
          description: Loan information updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /transactions/{transactionId}/disbursement:
    patch:
      tags:
        - Transactions
      summary: Update disbursement information
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisbursementRequest'
      responses:
        '200':
          description: Disbursement information updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /transactions/{transactionId}/preview:
    get:
      tags:
        - Transactions
      summary: Get transaction preview
      description: Get detailed transaction preview with loan type information and completion status
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Transaction preview retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/TransactionPreview'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /transactions/{transactionId}/pdf:
    get:
      tags:
        - Transactions
      summary: Download transaction PDF
      description: Download a professionally formatted PDF of the transaction
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: PDF file
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /transactions/{transactionId}/submit:
    post:
      tags:
        - Transactions
      summary: Submit transaction for approval
      description: Submit a complete transaction for approval workflow
      parameters:
        - name: transactionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Transaction submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: Transaction incomplete or cannot be submitted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
