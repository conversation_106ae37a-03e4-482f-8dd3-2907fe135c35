import prisma from "../db/db";
import { TransactionStatus, ApprovalStage, UserRole } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";

export interface ProgressStageInfo {
  id: string;
  name: string;
  status: 'completed' | 'active' | 'pending';
  timestamp?: Date;
  order: number;
}

export interface TransactionProgress {
  transactionId: string;
  transactionNumber: string;
  currentStatus: TransactionStatus;
  currentStage: ApprovalStage;
  progressPercentage: number;
  stages: ProgressStageInfo[];
  lastUpdated: Date;
}

export class ProgressTrackingService {
  /**
   * Get transaction progress tracking data
   */
  static async getTransactionProgress(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<TransactionProgress> {
    // Build where clause based on user role
    const whereClause: any = { id: transactionId };
    
    // Account officers can only see their own transactions
    if (userRole === UserRole.ACCOUNT_OFFICER) {
      whereClause.createdById = userId;
    }

    const transaction = await prisma.transaction.findFirst({
      where: whereClause,
      include: {
        approvals: {
          include: {
            approver: {
              select: {
                firstName: true,
                lastName: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!transaction) {
      throw new OperationalError("Transaction not found", 404);
    }

    // Generate progress stages based on current workflow
    const stages = this.generateProgressStages(transaction);
    
    // Calculate progress percentage
    const completedStages = stages.filter(stage => stage.status === 'completed').length;
    const progressPercentage = Math.round((completedStages / stages.length) * 100);

    return {
      transactionId: transaction.id,
      transactionNumber: transaction.transactionId,
      currentStatus: transaction.status,
      currentStage: transaction.currentStage || ApprovalStage.ACCOUNT_OFFICER,
      progressPercentage,
      stages,
      lastUpdated: transaction.updatedAt,
    };
  }

  /**
   * Generate progress stages based on transaction workflow
   */
  private static generateProgressStages(transaction: any): ProgressStageInfo[] {
    const stages: ProgressStageInfo[] = [];

    // Stage 1: Draft/Application Started
    stages.push({
      id: 'draft',
      name: 'Application Started',
      status: this.getStageStatus('draft', transaction),
      timestamp: transaction.createdAt,
      order: 1,
    });

    // Stage 2: Submitted
    stages.push({
      id: 'submitted',
      name: 'Submitted',
      status: this.getStageStatus('submitted', transaction),
      timestamp: transaction.submittedAt,
      order: 2,
    });

    // Stage 3: Under Review
    stages.push({
      id: 'under_review',
      name: 'Under Review',
      status: this.getStageStatus('under_review', transaction),
      timestamp: this.getReviewStartTimestamp(transaction),
      order: 3,
    });

    // Stage 4: Approved
    stages.push({
      id: 'approved',
      name: 'Approved',
      status: this.getStageStatus('approved', transaction),
      timestamp: this.getApprovalTimestamp(transaction),
      order: 4,
    });

    // Stage 5: Disbursed
    stages.push({
      id: 'disbursed',
      name: 'Disbursed',
      status: this.getStageStatus('disbursed', transaction),
      timestamp: transaction.disbursedAt,
      order: 5,
    });

    return stages;
  }

  /**
   * Determine stage status based on transaction state
   */
  private static getStageStatus(
    stageId: string,
    transaction: any
  ): 'completed' | 'active' | 'pending' {
    const status = transaction.status;

    switch (stageId) {
      case 'draft':
        return 'completed'; // Always completed once transaction exists

      case 'submitted':
        if (status === TransactionStatus.DRAFT) return 'pending';
        return 'completed';

      case 'under_review':
        if ([TransactionStatus.DRAFT, TransactionStatus.SUBMITTED].includes(status)) {
          return status === TransactionStatus.SUBMITTED ? 'pending' : 'pending';
        }
        if (status === TransactionStatus.IN_PROGRESS) return 'active';
        if ([TransactionStatus.APPROVED, TransactionStatus.DISBURSED, TransactionStatus.COMPLETED, TransactionStatus.LOAN_REPAID].includes(status)) {
          return 'completed';
        }
        return 'pending';

      case 'approved':
        if (status === TransactionStatus.APPROVED) return 'active';
        if ([TransactionStatus.DISBURSED, TransactionStatus.COMPLETED, TransactionStatus.LOAN_REPAID].includes(status)) {
          return 'completed';
        }
        return 'pending';

      case 'disbursed':
        if ([TransactionStatus.DISBURSED, TransactionStatus.COMPLETED, TransactionStatus.LOAN_REPAID].includes(status)) {
          return 'completed';
        }
        return 'pending';

      default:
        return 'pending';
    }
  }

  /**
   * Get review start timestamp (first approval attempt)
   */
  private static getReviewStartTimestamp(transaction: any): Date | undefined {
    if (transaction.approvals && transaction.approvals.length > 0) {
      return transaction.approvals[0].createdAt;
    }
    return undefined;
  }

  /**
   * Get final approval timestamp
   */
  private static getApprovalTimestamp(transaction: any): Date | undefined {
    if (transaction.status === TransactionStatus.APPROVED && transaction.approvals) {
      // Find the last approval that resulted in APPROVED status
      const approvals = transaction.approvals
        .filter((a: any) => a.action === 'APPROVED')
        .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      return approvals.length > 0 ? approvals[0].createdAt : undefined;
    }
    return undefined;
  }

  /**
   * Get simple status label for frontend display
   */
  static getStatusLabel(status: TransactionStatus): string {
    const labels = {
      [TransactionStatus.DRAFT]: 'Draft',
      [TransactionStatus.SUBMITTED]: 'Submitted',
      [TransactionStatus.IN_PROGRESS]: 'Under Review',
      [TransactionStatus.APPROVED]: 'Approved',
      [TransactionStatus.REJECTED]: 'Rejected',
      [TransactionStatus.SENT_BACK]: 'Sent Back',
      [TransactionStatus.DISBURSED]: 'Disbursed',
      [TransactionStatus.COMPLETED]: 'Completed',
      [TransactionStatus.LOAN_REPAID]: 'Repaid',
    };

    return labels[status] || status;
  }

  /**
   * Get next expected action for the transaction
   */
  static getNextAction(transaction: any): string | undefined {
    const status = transaction.status;
    const currentStage = transaction.currentStage;

    switch (status) {
      case TransactionStatus.DRAFT:
        return 'Complete application and submit';
      
      case TransactionStatus.SUBMITTED:
        return 'Waiting for supervisor review';
      
      case TransactionStatus.IN_PROGRESS:
        switch (currentStage) {
          case ApprovalStage.HEAD_CONSUMER_LENDING:
            return 'Waiting for Head Consumer Lending approval';
          case ApprovalStage.HEAD_RISK_MANAGEMENT:
            return 'Waiting for Risk Management approval';
          case ApprovalStage.MANAGING_DIRECTOR:
            return 'Waiting for Managing Director approval';
          default:
            return 'Under review';
        }
      
      case TransactionStatus.APPROVED:
        return 'Ready for disbursement';
      
      case TransactionStatus.DISBURSED:
        return 'Loan active - awaiting repayment';
      
      case TransactionStatus.SENT_BACK:
        return 'Review feedback and resubmit';
      
      case TransactionStatus.REJECTED:
        return 'Application rejected';
      
      case TransactionStatus.COMPLETED:
      case TransactionStatus.LOAN_REPAID:
        return 'Transaction completed';
      
      default:
        return undefined;
    }
  }
}
