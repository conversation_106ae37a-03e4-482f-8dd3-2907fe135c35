import prisma from "../db/db";
import { TransactionStatus, ApprovalStage, UserRole } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";

export interface EligibleTransaction {
  id: string;
  transactionId: string;
  firstName: string;
  lastName: string;
  requestedAmount: number;
  accountName?: string;
  accountNumber?: string;
  bankName?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BulkDisbursementRequest {
  transactionIds: string[];
}

export interface DisbursementResult {
  transactionId: string;
  success: boolean;
  error?: string;
  amount?: number;
}

export interface BulkDisbursementSummary {
  totalTransactions: number;
  successfulDisbursements: number;
  failedDisbursements: number;
  totalAmount: number;
  successful: DisbursementResult[];
  failed: DisbursementResult[];
  processedAt: Date;
  processedBy: string;
}

export class BulkDisbursementService {
  /**
   * Get all transactions eligible for disbursement
   */
  static async getEligibleTransactions(
    accountantId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{
    transactions: EligibleTransaction[];
    total: number;
    hasMore: boolean;
  }> {
    // Verify the user is an accountant
    const accountant = await prisma.user.findUnique({
      where: { id: accountantId },
      select: { role: true, isActive: true },
    });

    if (!accountant || accountant.role !== UserRole.ACCOUNTANT) {
      throw new OperationalError(
        "Only accountants can access disbursement operations",
        403
      );
    }

    if (!accountant.isActive) {
      throw new OperationalError("Account is inactive", 403);
    }

    // Get eligible transactions (APPROVED status, ACCOUNTANT stage)
    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where: {
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
        select: {
          id: true,
          transactionId: true,
          firstName: true,
          lastName: true,
          requestedAmount: true,
          accountName: true,
          accountNumber: true,
          bankName: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: [
          { updatedAt: "asc" }, // Oldest approved first
          { createdAt: "asc" },
        ],
        take: limit,
        skip: offset,
      }),
      prisma.transaction.count({
        where: {
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
      }),
    ]);

    return {
      transactions: transactions as EligibleTransaction[],
      total,
      hasMore: offset + transactions.length < total,
    };
  }

  /**
   * Process bulk disbursement for multiple transactions
   */
  static async processBulkDisbursement(
    transactionIds: string[],
    accountantId: string
  ): Promise<BulkDisbursementSummary> {
    // Validate input
    if (!transactionIds || transactionIds.length === 0) {
      throw new OperationalError(
        "At least one transaction ID is required",
        400
      );
    }

    if (transactionIds.length > 100) {
      throw new OperationalError(
        "Maximum 100 transactions can be processed at once",
        400
      );
    }

    // Verify the user is an accountant
    const accountant = await prisma.user.findUnique({
      where: { id: accountantId },
      select: {
        id: true,
        role: true,
        isActive: true,
        firstName: true,
        lastName: true,
      },
    });

    if (!accountant || accountant.role !== UserRole.ACCOUNTANT) {
      throw new OperationalError(
        "Only accountants can perform bulk disbursements",
        403
      );
    }

    if (!accountant.isActive) {
      throw new OperationalError("Account is inactive", 403);
    }

    console.log("Starting bulk disbursement:", {
      transactionCount: transactionIds.length,
      accountantId,
      accountantName: `${accountant.firstName} ${accountant.lastName}`,
      timestamp: new Date().toISOString(),
    });

    // Remove duplicates
    const uniqueTransactionIds = [...new Set(transactionIds)];

    // Fetch and validate all transactions
    const transactions = await prisma.transaction.findMany({
      where: {
        id: { in: uniqueTransactionIds },
      },
      select: {
        id: true,
        transactionId: true,
        status: true,
        currentStage: true,
        requestedAmount: true,
        firstName: true,
        lastName: true,
        accountName: true,
        accountNumber: true,
        bankName: true,
        updatedAt: true,
      },
    });

    // Validate all transactions exist
    const foundIds = transactions.map((t) => t.id);
    const missingIds = uniqueTransactionIds.filter(
      (id) => !foundIds.includes(id)
    );

    if (missingIds.length > 0) {
      throw new OperationalError(
        `Transactions not found: ${missingIds.join(", ")}`,
        404
      );
    }

    // Process disbursements using database transaction for atomicity
    const results = await prisma.$transaction(async (tx) => {
      const successful: DisbursementResult[] = [];
      const failed: DisbursementResult[] = [];
      let totalAmount = 0;

      for (const transaction of transactions) {
        try {
          // Validate transaction eligibility
          if (transaction.status !== TransactionStatus.APPROVED) {
            failed.push({
              transactionId: transaction.transactionId,
              success: false,
              error: `Transaction status is ${transaction.status}, expected APPROVED`,
              amount: transaction.requestedAmount || 0,
            });
            continue;
          }

          if (transaction.currentStage !== ApprovalStage.ACCOUNTANT) {
            failed.push({
              transactionId: transaction.transactionId,
              success: false,
              error: `Transaction stage is ${transaction.currentStage}, expected ACCOUNTANT`,
              amount: transaction.requestedAmount || 0,
            });
            continue;
          }

          // Validate disbursement details
          if (
            !transaction.accountName ||
            !transaction.accountNumber ||
            !transaction.bankName
          ) {
            failed.push({
              transactionId: transaction.transactionId,
              success: false,
              error: "Missing disbursement account details",
              amount: transaction.requestedAmount || 0,
            });
            continue;
          }

          // Update transaction status to DISBURSED
          await tx.transaction.update({
            where: { id: transaction.id },
            data: {
              status: TransactionStatus.DISBURSED,
              disbursedAt: new Date(),
              updatedAt: new Date(),
            },
          });

          successful.push({
            transactionId: transaction.transactionId,
            success: true,
            amount: transaction.requestedAmount || 0,
          });

          totalAmount += transaction.requestedAmount || 0;

          console.log("Transaction disbursed:", {
            transactionId: transaction.transactionId,
            amount: transaction.requestedAmount,
            borrower: `${transaction.firstName} ${transaction.lastName}`,
          });
        } catch (error: any) {
          console.error("Failed to disburse transaction:", {
            transactionId: transaction.transactionId,
            error: error.message,
          });

          failed.push({
            transactionId: transaction.transactionId,
            success: false,
            error: error.message || "Unknown error occurred",
            amount: transaction.requestedAmount || 0,
          });
        }
      }

      // If any transaction failed, rollback all changes
      if (failed.length > 0) {
        const errorData = { failed, successful: [] };
        throw new OperationalError(
          `Bulk disbursement failed. ${failed.length} transaction(s) could not be processed. All changes have been rolled back.`,
          400
        );
      }

      return { successful, failed, totalAmount };
    });

    const summary: BulkDisbursementSummary = {
      totalTransactions: uniqueTransactionIds.length,
      successfulDisbursements: results.successful.length,
      failedDisbursements: results.failed.length,
      totalAmount: results.totalAmount,
      successful: results.successful,
      failed: results.failed,
      processedAt: new Date(),
      processedBy: accountantId,
    };

    console.log("Bulk disbursement completed:", {
      totalTransactions: summary.totalTransactions,
      successful: summary.successfulDisbursements,
      failed: summary.failedDisbursements,
      totalAmount: summary.totalAmount,
      accountantId,
    });

    return summary;
  }

  /**
   * Get bulk disbursement statistics
   */
  static async getDisbursementStats(accountantId: string): Promise<{
    eligibleCount: number;
    totalEligibleAmount: number;
    recentDisbursements: number;
  }> {
    // Verify the user is an accountant
    const accountant = await prisma.user.findUnique({
      where: { id: accountantId },
      select: { role: true, isActive: true },
    });

    if (!accountant || accountant.role !== UserRole.ACCOUNTANT) {
      throw new OperationalError(
        "Only accountants can access disbursement statistics",
        403
      );
    }

    const [eligibleTransactions, recentDisbursements] = await Promise.all([
      prisma.transaction.findMany({
        where: {
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
        select: {
          requestedAmount: true,
        },
      }),
      prisma.transaction.count({
        where: {
          status: TransactionStatus.DISBURSED,
          disbursedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
    ]);

    const totalEligibleAmount = eligibleTransactions.reduce(
      (sum, t) => sum + (t.requestedAmount || 0),
      0
    );

    return {
      eligibleCount: eligibleTransactions.length,
      totalEligibleAmount,
      recentDisbursements,
    };
  }
}
