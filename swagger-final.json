{"openapi": "3.0.0", "info": {"title": "AMS Loan Management System API", "description": "Complete API documentation for AMS Loan Management System with loan type selection, transaction management, and approval workflows.", "version": "3.0.0", "contact": {"name": "AMS Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8000/api", "description": "Development server"}, {"url": "https://api.ams-loan.com/api", "description": "Production server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "object"}}}, "LoanType": {"type": "string", "enum": ["CONSUMER_LOAN_PUBLIC", "CONSUMER_LOAN_PRIVATE", "SME_INDIVIDUAL", "SME_CORPORATE"]}, "TransactionStatus": {"type": "string", "enum": ["DRAFT", "SUBMITTED", "IN_PROGRESS", "APPROVED", "REJECTED", "SENT_BACK", "DISBURSED", "COMPLETED", "LOAN_REPAID"]}, "UserRole": {"type": "string", "enum": ["ACCOUNT_OFFICER", "SUPERVISOR", "HEAD_CONSUMER_LENDING", "HEAD_RISK_MANAGEMENT", "MANAGING_DIRECTOR", "ACCOUNTANT", "SUPER_ADMIN"]}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}}, "required": ["email", "password"]}, "OTPVerificationRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "otp": {"type": "string", "pattern": "^[0-9]{6}$"}}, "required": ["email", "otp"]}, "LoanTypeConfig": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/LoanType"}, "name": {"type": "string"}, "description": {"type": "string"}, "requiredFields": {"type": "array", "items": {"type": "string"}}}}, "LoanTypeSelectionRequest": {"type": "object", "properties": {"loanType": {"$ref": "#/components/schemas/LoanType"}}, "required": ["loanType"]}, "PersonalInfoRequest": {"type": "object", "properties": {"firstName": {"type": "string"}, "middleName": {"type": "string"}, "lastName": {"type": "string"}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "maritalStatus": {"type": "string", "enum": ["SINGLE", "MARRIED", "DIVORCED", "WIDOWED"]}, "dateOfBirth": {"type": "string", "format": "date"}, "email": {"type": "string", "format": "email"}, "phoneNumber": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "postalCode": {"type": "string"}, "employmentDate": {"type": "string", "format": "date"}, "organizationName": {"type": "string"}, "ippisNumber": {"type": "string"}, "remitaActivation": {"type": "string"}, "businessName": {"type": "string"}, "registrationNumber": {"type": "string"}}, "required": ["firstName", "lastName", "gender", "maritalStatus", "dateOfBirth", "email", "phoneNumber", "street", "city", "state", "postalCode", "employmentDate"]}, "NextOfKinRequest": {"type": "object", "properties": {"nokFirstName": {"type": "string"}, "nokMiddleName": {"type": "string"}, "nokLastName": {"type": "string"}, "nokRelationship": {"type": "string"}, "nokPhoneNumber": {"type": "string"}, "nokStreet": {"type": "string"}, "nokCity": {"type": "string"}, "nokState": {"type": "string"}, "nokPostalCode": {"type": "string"}}, "required": ["nokFirstName", "nokMiddleName", "nokLastName", "nokRelationship", "nokPhoneNumber", "nokStreet", "nokCity", "nokState"]}, "LoanInfoRequest": {"type": "object", "properties": {"requestedAmount": {"type": "number", "minimum": 1}, "loanTenor": {"type": "integer", "minimum": 1}, "repaymentMode": {"type": "string", "enum": ["MONTHLY", "QUARTERLY", "SEMI_ANNUALLY", "ANNUALLY"]}, "grossPay": {"type": "number", "minimum": 0}, "netPay": {"type": "number", "minimum": 0}, "purposeOfLoan": {"type": "string"}}, "required": ["requestedAmount", "loanTenor", "repaymentMode", "grossPay", "netPay", "purposeOfLoan"]}, "DisbursementRequest": {"type": "object", "properties": {"accountName": {"type": "string"}, "accountNumber": {"type": "string", "minLength": 10}, "bankName": {"type": "string"}}, "required": ["accountName", "accountNumber", "bankName"]}, "ApprovalActionRequest": {"type": "object", "properties": {"action": {"type": "string", "enum": ["APPROVED", "REJECTED", "SENT_BACK"]}, "comments": {"type": "string"}}, "required": ["action"]}, "LoanEligibilityRequest": {"type": "object", "properties": {"bvn": {"type": "string", "pattern": "^[0-9]{11}$", "description": "Bank Verification Number (exactly 11 digits)", "example": "***********"}}, "required": ["bvn"]}, "LoanEligibilityResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "You are not eligible for a new loan at this time"}, "data": {"$ref": "#/components/schemas/LoanEligibilityResult"}}}, "LoanEligibilityResult": {"type": "object", "properties": {"isEligible": {"type": "boolean", "description": "Whether the user is eligible for a new loan", "example": false}, "reason": {"type": "string", "description": "Reason for ineligibility (only present when isEligible is false)", "example": "You have an active loan (TXN-2024-001) that expires on 12/31/2024. You can apply for a new loan after this date."}, "activeLoan": {"$ref": "#/components/schemas/ActiveLoanInfo"}, "eligibilityDate": {"type": "string", "format": "date-time", "description": "Date when user becomes eligible for new loan (only present when isEligible is false)", "example": "2024-12-31T00:00:00.000Z"}}}, "ActiveLoanInfo": {"type": "object", "properties": {"transactionId": {"type": "string", "description": "Transaction ID of the active loan", "example": "TXN-2024-001"}, "status": {"$ref": "#/components/schemas/TransactionStatus"}, "disbursedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "Date when the loan was disbursed", "example": "2024-01-01T00:00:00.000Z"}, "loanTenor": {"type": "integer", "description": "Loan tenor in months", "example": 12}, "expectedCompletionDate": {"type": "string", "format": "date-time", "description": "Expected loan completion date", "example": "2024-12-31T00:00:00.000Z"}, "requestedAmount": {"type": "number", "description": "Loan amount", "example": 500000}}}, "LoanHistoryResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Loan history retrieved successfully"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LoanHistoryItem"}}}}, "LoanHistoryItem": {"type": "object", "properties": {"id": {"type": "string", "description": "Internal transaction ID", "example": "507f1f77bcf86cd799439011"}, "transactionId": {"type": "string", "description": "Human-readable transaction ID", "example": "TXN-2024-001"}, "status": {"$ref": "#/components/schemas/TransactionStatus"}, "requestedAmount": {"type": "number", "nullable": true, "description": "Requested loan amount", "example": 500000}, "loanTenor": {"type": "integer", "nullable": true, "description": "Loan tenor in months", "example": 12}, "disbursedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "Date when loan was disbursed", "example": "2024-01-01T00:00:00.000Z"}, "completedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "Date when loan was completed", "example": "2024-12-31T00:00:00.000Z"}, "expectedCompletionDate": {"type": "string", "format": "date-time", "nullable": true, "description": "Expected completion date based on tenor", "example": "2024-12-31T00:00:00.000Z"}, "isActive": {"type": "boolean", "description": "Whether this loan is currently active", "example": false}, "createdAt": {"type": "string", "format": "date-time", "description": "Date when transaction was created", "example": "2024-01-01T00:00:00.000Z"}, "submittedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "Date when transaction was submitted", "example": "2024-01-01T00:00:00.000Z"}, "rejectedAt": {"type": "string", "format": "date-time", "nullable": true, "description": "Date when transaction was rejected", "example": null}, "rejectionReason": {"type": "string", "nullable": true, "description": "Reason for rejection", "example": null}}}, "MarkLoanCompletedRequest": {"type": "object", "properties": {"completionReason": {"type": "string", "description": "Reason for marking loan as completed", "example": "PAID_OFF_EARLY", "enum": ["PAID_OFF_EARLY", "TENOR_EXPIRED", "ADMINISTRATIVE_COMPLETION", "DEFAULTED"]}}}, "MarkLoanRepaidRequest": {"type": "object", "properties": {"repaymentMethod": {"type": "string", "description": "Method used for repayment", "example": "Bank Transfer", "enum": ["Bank Transfer", "Cash", "Cheque", "Online Payment", "Debit Card", "Mobile Money"]}, "repaymentReference": {"type": "string", "description": "Reference number for the repayment transaction", "example": "TXN-REF-2024-001"}, "notes": {"type": "string", "description": "Additional notes about the repayment", "example": "Full loan amount repaid via bank transfer"}}}}}, "security": [{"bearerAuth": []}], "tags": [{"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Loan Types", "description": "Loan type selection and configuration"}, {"name": "Transactions", "description": "Transaction creation and management"}, {"name": "Approvals", "description": "Transaction approval workflow"}, {"name": "Documents", "description": "Document upload and management"}, {"name": "Dashboard", "description": "Analytics and reporting"}, {"name": "Notifications", "description": "System notifications"}, {"name": "Users", "description": "User management (Admin only)"}, {"name": "Loan Eligibility", "description": "Loan eligibility validation and BVN-based loan management"}], "paths": {"/auth/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user with email and password. Returns OTP requirement.", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}, "example": {"email": "<EMAIL>", "password": "password123"}}}}, "responses": {"200": {"description": "Login successful, OTP sent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Invalid credentials"}, "429": {"description": "Too many requests"}}}}, "/auth/verify-otp": {"post": {"tags": ["Authentication"], "summary": "Verify OTP and get tokens", "description": "Verify OTP code and receive access and refresh tokens", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OTPVerificationRequest"}}}}, "responses": {"200": {"description": "OTP verified successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Invalid or expired OTP"}}}}, "/auth/profile": {"get": {"tags": ["Authentication"], "summary": "Get user profile", "description": "Get current user's profile information", "responses": {"200": {"description": "Profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized"}}}}, "/transactions/loan-types": {"get": {"tags": ["Loan Types"], "summary": "Get available loan types", "description": "Retrieve all available loan types with their configurations", "responses": {"200": {"description": "Loan types retrieved successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LoanTypeConfig"}}}}]}}}}}}}, "/transactions": {"post": {"tags": ["Transactions"], "summary": "Create new transaction", "description": "Create a new transaction in draft status (Account Officer only)", "responses": {"201": {"description": "Transaction created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Account Officer role required"}}}, "get": {"tags": ["Transactions"], "summary": "Get transactions", "description": "Retrieve transactions with filtering and pagination", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/TransactionStatus"}}, {"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Transactions retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/transactions/{transactionId}": {"get": {"tags": ["Transactions"], "summary": "Get specific transaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction retrieved successfully"}, "404": {"description": "Transaction not found"}}}, "delete": {"tags": ["Transactions"], "summary": "Delete transaction", "description": "Delete a draft transaction (Account Officer only)", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction deleted successfully"}, "403": {"description": "Cannot delete non-draft transaction"}}}}, "/transactions/{transactionId}/loan-type": {"patch": {"tags": ["Loan Types"], "summary": "Set loan type for transaction", "description": "Set the loan type for a transaction (Account Officer only)", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanTypeSelectionRequest"}, "examples": {"consumer_public": {"summary": "Consumer Loan Public", "value": {"loanType": "CONSUMER_LOAN_PUBLIC"}}, "sme_corporate": {"summary": "SME Corporate", "value": {"loanType": "SME_CORPORATE"}}}}}}, "responses": {"200": {"description": "Loan type set successfully"}, "400": {"description": "Invalid loan type or transaction cannot be edited"}}}}, "/transactions/{transactionId}/personal-info": {"patch": {"tags": ["Transactions"], "summary": "Update personal information", "description": "Update personal information with dynamic validation based on loan type", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalInfoRequest"}}}}, "responses": {"200": {"description": "Personal information updated successfully"}, "400": {"description": "Validation failed or loan type not set"}}}}, "/transactions/{transactionId}/next-of-kin": {"patch": {"tags": ["Transactions"], "summary": "Update next of kin information", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NextOfKinRequest"}}}}, "responses": {"200": {"description": "Next of kin information updated successfully"}}}}, "/transactions/{transactionId}/loan-info": {"patch": {"tags": ["Transactions"], "summary": "Update loan information", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanInfoRequest"}}}}, "responses": {"200": {"description": "Loan information updated successfully"}}}}, "/transactions/{transactionId}/disbursement": {"patch": {"tags": ["Transactions"], "summary": "Update disbursement information", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisbursementRequest"}}}}, "responses": {"200": {"description": "Disbursement information updated successfully"}}}}, "/transactions/{transactionId}/preview": {"get": {"tags": ["Transactions"], "summary": "Get transaction preview", "description": "Get detailed transaction preview with loan type information and completion status", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction preview retrieved successfully"}, "404": {"description": "Transaction not found"}}}}, "/transactions/{transactionId}/pdf": {"get": {"tags": ["Transactions"], "summary": "Download transaction PDF", "description": "Download a professionally formatted PDF of the transaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "PDF file", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Transaction not found"}}}}, "/transactions/{transactionId}/download-all": {"get": {"tags": ["Transactions"], "summary": "Download all transaction files as ZIP", "description": "Downloads a ZIP archive containing all transaction documents and the transaction PDF", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "ZIP file containing all transaction documents and PDF", "content": {"application/zip": {"schema": {"type": "string", "format": "binary"}}}, "headers": {"Content-Disposition": {"description": "Attachment with filename", "schema": {"type": "string", "example": "attachment; filename=\"TXN17499886400668092.zip\""}}, "Content-Length": {"description": "Size of the ZIP file in bytes", "schema": {"type": "integer"}}}}, "404": {"description": "Transaction not found or no content available"}, "500": {"description": "Internal server error - Failed to create ZIP archive"}}}}, "/transactions/{transactionId}/submit": {"post": {"tags": ["Transactions"], "summary": "Submit transaction for approval", "description": "Submit a complete transaction for approval workflow", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Transaction submitted successfully"}, "400": {"description": "Transaction incomplete or cannot be submitted"}}}}, "/documents/{transactionId}/upload": {"post": {"tags": ["Documents"], "summary": "Upload single document", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"document": {"type": "string", "format": "binary"}}}}}}, "responses": {"201": {"description": "Document uploaded successfully"}}}}, "/approvals/pending": {"get": {"tags": ["Approvals"], "summary": "Get pending approvals", "description": "Get transactions pending approval for current user's role", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Pending approvals retrieved successfully"}}}}, "/approvals/pending-approvals": {"get": {"tags": ["Approvals"], "summary": "Get transactions pending approval at current user's stage", "description": "Retrieve transactions that are currently at the approval stage corresponding to the logged-in user's role. Each role can only see transactions at their specific approval stage.", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}, "description": "Page number for pagination"}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}, "description": "Number of items per page"}, {"name": "accountOfficerId", "in": "query", "schema": {"type": "string"}, "description": "Filter by specific account officer (creator of transactions)"}, {"name": "loanType", "in": "query", "schema": {"type": "string", "enum": ["CONSUMER_LOAN_PUBLIC", "CONSUMER_LOAN_PRIVATE", "SME_INDIVIDUAL", "SME_CORPORATE"]}, "description": "Filter by loan type"}], "responses": {"200": {"description": "Pending approvals retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Pending approvals retrieved successfully"}, "data": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "customerName": {"type": "string"}, "loanType": {"type": "string", "enum": ["CONSUMER_LOAN_PUBLIC", "CONSUMER_LOAN_PRIVATE", "SME_INDIVIDUAL", "SME_CORPORATE"]}, "requestedAmount": {"type": "number"}, "status": {"type": "string", "enum": ["SUBMITTED", "IN_PROGRESS", "APPROVED"]}, "currentStage": {"type": "string", "enum": ["SUPERVISOR", "HEAD_CONSUMER_LENDING", "HEAD_RISK_MANAGEMENT", "MANAGING_DIRECTOR", "ACCOUNTANT"]}, "createdAt": {"type": "string", "format": "date-time"}, "submittedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}}}, "documentsCount": {"type": "integer"}, "canApprove": {"type": "boolean"}, "stageInfo": {"type": "object", "properties": {"stageName": {"type": "string"}, "actionLabel": {"type": "string"}, "isUrgent": {"type": "boolean"}}}}}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Approval role required"}}}}, "/approvals/{transactionId}/process": {"post": {"tags": ["Approvals"], "summary": "Process approval", "description": "Approve, reject, or send back a transaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalActionRequest"}, "examples": {"approve": {"summary": "Approve transaction", "value": {"action": "APPROVED", "comments": "All documents verified and approved"}}, "reject": {"summary": "Reject transaction", "value": {"action": "REJECTED", "comments": "Insufficient documentation"}}}}}}, "responses": {"200": {"description": "Approval processed successfully"}}}}, "/dashboard/stats": {"get": {"tags": ["Dashboard"], "summary": "Get user dashboard stats", "description": "Get dashboard statistics for current user", "responses": {"200": {"description": "Dashboard stats retrieved successfully"}}}}, "/dashboard/my-transactions": {"get": {"tags": ["Dashboard"], "summary": "Get my transactions", "description": "Get transactions for current user with filtering", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/TransactionStatus"}}], "responses": {"200": {"description": "My transactions retrieved successfully"}}}}, "/notifications": {"get": {"tags": ["Notifications"], "summary": "Get user notifications", "description": "Get notifications for current user with pagination", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Notifications retrieved successfully"}}}}, "/notifications/{notificationId}/read": {"patch": {"tags": ["Notifications"], "summary": "Mark notification as read", "description": "Mark a specific notification as read", "parameters": [{"name": "notificationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Notification marked as read"}}}}, "/notifications/mark-all-read": {"patch": {"tags": ["Notifications"], "summary": "Mark all notifications as read", "description": "Mark all notifications as read for current user", "responses": {"200": {"description": "All notifications marked as read"}}}}, "/documents/{transactionId}": {"get": {"tags": ["Documents"], "summary": "Get transaction documents", "description": "Get all documents for a transaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Documents retrieved successfully"}}}}, "/documents/{transactionId}/upload-multiple": {"post": {"tags": ["Documents"], "summary": "Upload multiple documents", "description": "Upload multiple documents for a transaction (max 10 files)", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"documents": {"type": "array", "items": {"type": "string", "format": "binary"}, "maxItems": 10}}}}}}, "responses": {"201": {"description": "Documents uploaded successfully"}}}}, "/documents/download/{documentId}": {"get": {"tags": ["Documents"], "summary": "Download document", "description": "Download a specific document", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"302": {"description": "Redirect to document URL"}, "404": {"description": "Document not found"}}}}, "/documents/{documentId}/signed-url": {"get": {"tags": ["Documents"], "summary": "Generate signed URL for document access", "description": "Generate a time-limited signed URL for secure document access. The URL expires after the specified time (default 1 hour, max 24 hours).", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "description": "Document ID (MongoDB ObjectID format)", "example": "507f1f77bcf86cd799439011"}, {"name": "expiresIn", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 300, "maximum": 86400, "default": 3600}, "description": "URL expiration time in seconds (5 minutes to 24 hours)", "example": 3600}], "responses": {"200": {"description": "Signed URL generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Signed URL generated successfully"}, "data": {"type": "object", "properties": {"signedUrl": {"type": "string", "format": "uri", "description": "Time-limited signed URL for document access", "example": "https://r2.example.com/documents/file.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."}, "expiresAt": {"type": "string", "format": "date-time", "description": "ISO timestamp when the URL expires", "example": "2024-01-15T10:30:00.000Z"}, "documentId": {"type": "string", "description": "Document ID", "example": "507f1f77bcf86cd799439011"}, "fileName": {"type": "string", "description": "Original filename", "example": "passport_copy.pdf"}}, "required": ["signedUrl", "expiresAt", "documentId", "fileName"]}}}}}}, "400": {"description": "Bad request (invalid document ID or expiration time)"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden (insufficient permissions)"}, "404": {"description": "Document not found"}, "429": {"description": "Too many requests"}}}}, "/approvals/{transactionId}/history": {"get": {"tags": ["Approvals"], "summary": "Get approval history", "description": "Get approval history for a transaction", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Approval history retrieved successfully"}}}}, "/dashboard/system-overview": {"get": {"tags": ["Dashboard"], "summary": "Get system overview (Admin only)", "description": "Get system-wide overview statistics", "responses": {"200": {"description": "System overview retrieved successfully"}, "403": {"description": "Forbidden - Admin access required"}}}}, "/users": {"post": {"tags": ["Users"], "summary": "Create new user (Admin only)", "description": "Create a new user account", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "role": {"$ref": "#/components/schemas/UserRole"}, "monthlyTarget": {"type": "number", "minimum": 0}}, "required": ["email", "password", "firstName", "lastName", "role"]}}}}, "responses": {"201": {"description": "User created successfully"}, "403": {"description": "Forbidden - Admin access required"}}}, "get": {"tags": ["Users"], "summary": "Get all users (Admin only)", "description": "Get all users with filtering and pagination", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "role", "in": "query", "schema": {"$ref": "#/components/schemas/UserRole"}}, {"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Users retrieved successfully"}, "403": {"description": "Forbidden - Admin access required"}}}}, "/users/{userId}": {"get": {"tags": ["Users"], "summary": "Get specific user (Admin only)", "description": "Get a specific user by ID", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "User retrieved successfully"}, "404": {"description": "User not found"}, "403": {"description": "Forbidden - Admin access required"}}}, "patch": {"tags": ["Users"], "summary": "Update user (Admin only)", "description": "Update user information", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "role": {"$ref": "#/components/schemas/UserRole"}, "monthlyTarget": {"type": "number", "minimum": 0}, "isActive": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "User updated successfully"}, "404": {"description": "User not found"}, "403": {"description": "Forbidden - Admin access required"}}}, "delete": {"tags": ["Users"], "summary": "Deactivate user (Admin only)", "description": "Deactivate a user account", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "User deactivated successfully"}, "404": {"description": "User not found"}, "403": {"description": "Forbidden - Admin access required"}}}}, "/transactions/{transactionId}/approval-history": {"get": {"tags": ["Transactions"], "summary": "Get detailed approval history for a specific transaction", "description": "Returns a chronological approval history for a specific transaction showing the complete approval workflow journey. Displays each stage, approver details, actions taken, comments, and timestamps in a formatted structure.", "parameters": [{"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Transaction ID"}], "responses": {"200": {"description": "Approval history retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Approval history retrieved successfully"}, "data": {"type": "object", "properties": {"transactionId": {"type": "string", "example": "TXN-12345"}, "approvalHistory": {"type": "array", "items": {"type": "object", "properties": {"stage": {"type": "string", "enum": ["ACCOUNT_OFFICER", "SUPERVISOR", "HEAD_CONSUMER_LENDING", "HEAD_RISK_MANAGEMENT", "MANAGING_DIRECTOR", "ACCOUNTANT"], "example": "SUPERVISOR"}, "stageName": {"type": "string", "example": "Supervisor Review"}, "action": {"type": "string", "enum": ["APPROVED", "REJECTED", "SENT_BACK", "DISBURSED", "SUBMITTED"], "example": "APPROVED"}, "approver": {"type": "object", "properties": {"id": {"type": "string", "example": "user-123"}, "name": {"type": "string", "example": "<PERSON>"}, "role": {"type": "string", "enum": ["SUPER_ADMIN", "ACCOUNT_OFFICER", "SUPERVISOR", "HEAD_CONSUMER_LENDING", "HEAD_RISK_MANAGEMENT", "MANAGING_DIRECTOR", "ACCOUNTANT"], "example": "SUPERVISOR"}}}, "comments": {"type": "string", "nullable": true, "example": "Application looks good, approved for next stage"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-06-15T10:30:00Z"}, "formattedDate": {"type": "string", "example": "6/15/2025"}}}}}}}}}}}, "404": {"description": "Transaction not found"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal server error"}}}}, "/transactions/check-loan-eligibility": {"post": {"tags": ["Loan Eligibility"], "summary": "Check loan eligibility by BVN", "description": "Check if a user is eligible for a new loan based on their BVN. This endpoint validates the single active loan policy and returns detailed information about eligibility status.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanEligibilityRequest"}, "example": {"bvn": "***********"}}}}, "responses": {"200": {"description": "Eligibility check completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanEligibilityResponse"}, "examples": {"eligible": {"summary": "User is eligible for new loan", "value": {"success": true, "message": "You are eligible for a new loan", "data": {"isEligible": true}}}, "not_eligible": {"summary": "User has active loan", "value": {"success": true, "message": "You are not eligible for a new loan at this time", "data": {"isEligible": false, "reason": "You have an active loan (TXN-2024-001) that expires on 12/31/2024. You can apply for a new loan after this date.", "activeLoan": {"transactionId": "TXN-2024-001", "status": "DISBURSED", "disbursedAt": "2024-01-01T00:00:00.000Z", "loanTenor": 12, "expectedCompletionDate": "2024-12-31T00:00:00.000Z", "requestedAmount": 500000}, "eligibilityDate": "2024-12-31T00:00:00.000Z"}}}}}}}, "400": {"description": "Invalid BVN format or validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Authentication required"}, "500": {"description": "Internal server error"}}}}, "/transactions/loan-history/{bvn}": {"get": {"tags": ["Loan Eligibility"], "summary": "Get loan history by BVN", "description": "Retrieve complete loan history for a user based on their BVN. Shows all loans (except DRAFT status) with their current status, amounts, tenors, and completion information.", "parameters": [{"in": "path", "name": "bvn", "required": true, "schema": {"type": "string", "pattern": "^[0-9]{11}$"}, "description": "Bank Verification Number (exactly 11 digits)", "example": "***********"}], "responses": {"200": {"description": "Loan history retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanHistoryResponse"}, "example": {"success": true, "message": "Loan history retrieved successfully", "data": [{"id": "507f1f77bcf86cd799439011", "transactionId": "TXN-2024-001", "status": "COMPLETED", "requestedAmount": 500000, "loanTenor": 12, "disbursedAt": "2024-01-01T00:00:00.000Z", "completedAt": "2024-12-31T00:00:00.000Z", "expectedCompletionDate": "2024-12-31T00:00:00.000Z", "isActive": false, "createdAt": "2023-12-15T00:00:00.000Z", "submittedAt": "2023-12-20T00:00:00.000Z", "rejectedAt": null, "rejectionReason": null}]}}}}, "400": {"description": "Invalid BVN format"}, "401": {"description": "Unauthorized - Authentication required"}, "500": {"description": "Internal server error"}}}}, "/transactions/{transactionId}/mark-completed": {"patch": {"tags": ["Loan Eligibility"], "summary": "Mark a loan as completed", "description": "Manually mark a loan as completed. This is useful for cases where loan is paid off early, loan tenor has expired, or administrative completion is required.", "parameters": [{"in": "path", "name": "transactionId", "required": true, "schema": {"type": "string"}, "description": "Transaction ID (internal MongoDB ObjectId)", "example": "507f1f77bcf86cd799439011"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkLoanCompletedRequest"}, "example": {"completionReason": "PAID_OFF_EARLY"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> marked as completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "message": "<PERSON><PERSON> marked as completed successfully", "data": {}}}}}, "400": {"description": "Invalid request or loan cannot be completed"}, "401": {"description": "Unauthorized - Authentication required"}, "404": {"description": "Transaction not found"}, "500": {"description": "Internal server error"}}}}, "/transactions/{transactionId}/mark-repaid": {"patch": {"tags": ["Loan Eligibility"], "summary": "Mark a loan as repaid", "description": "Mark a loan as repaid when the user has paid back their loan. This endpoint is used when a user has successfully repaid their loan and the loan should be marked as completed with repayment status.", "parameters": [{"in": "path", "name": "transactionId", "required": true, "schema": {"type": "string"}, "description": "Transaction ID (internal MongoDB ObjectId)", "example": "507f1f77bcf86cd799439011"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkLoanRepaidRequest"}, "example": {"repaymentMethod": "Bank Transfer", "repaymentReference": "TXN-REF-2024-001", "notes": "Full loan amount repaid via bank transfer"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> marked as repaid successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"success": true, "message": "<PERSON><PERSON> marked as repaid successfully", "data": {}}}}}, "400": {"description": "Invalid request or loan cannot be marked as repaid"}, "401": {"description": "Unauthorized - Authentication required"}, "404": {"description": "Transaction not found"}, "500": {"description": "Internal server error"}}}}}}