import { R2Service } from "./r2Service";
import { CloudinaryService } from "./cloudinaryService";
import sharp from "sharp";
import { createReadStream, createWriteStream } from "fs";
import { pipeline } from "stream/promises";
import { Transform } from "stream";

/**
 * Optimized file service for handling file uploads, processing, and storage
 * Includes image optimization, streaming, and efficient memory usage
 */
export class OptimizedFileService {
  /**
   * Optimize image files before upload
   */
  static async optimizeImage(
    buffer: Buffer,
    options: {
      maxWidth?: number;
      maxHeight?: number;
      quality?: number;
      format?: "jpeg" | "png" | "webp";
    } = {}
  ): Promise<Buffer> {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 85,
      format = "jpeg",
    } = options;

    try {
      let sharpInstance = sharp(buffer);

      // Get image metadata
      const metadata = await sharpInstance.metadata();

      // Only resize if image is larger than max dimensions
      if (metadata.width && metadata.height) {
        if (metadata.width > maxWidth || metadata.height > maxHeight) {
          sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
            fit: "inside",
            withoutEnlargement: true,
          });
        }
      }

      // Convert and compress
      switch (format) {
        case "jpeg":
          sharpInstance = sharpInstance.jpeg({ quality, progressive: true });
          break;
        case "png":
          sharpInstance = sharpInstance.png({ quality, progressive: true });
          break;
        case "webp":
          sharpInstance = sharpInstance.webp({ quality });
          break;
      }

      return await sharpInstance.toBuffer();
    } catch (error) {
      console.warn("Image optimization failed, using original:", error);
      return buffer;
    }
  }

  /**
   * Stream-based file upload for large files
   * Note: This is a placeholder - implement based on your storage service
   */
  static async streamUpload(
    filePath: string,
    key: string,
    contentType: string,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    try {
      // This would need to be implemented with proper streaming
      console.log(
        "Stream upload not fully implemented for:",
        filePath,
        key,
        contentType
      );

      // Simulate progress
      if (onProgress) {
        onProgress(100);
      }

      return `https://example.com/${key}`; // Placeholder URL
    } catch (error) {
      console.error("Stream upload failed:", error);
      throw error;
    }
  }

  /**
   * Batch file processing with concurrency control
   */
  static async processBatchFiles(
    files: Array<{
      buffer: Buffer;
      originalName: string;
      mimeType: string;
    }>,
    options: {
      concurrency?: number;
      optimize?: boolean;
      generateThumbnails?: boolean;
    } = {}
  ): Promise<
    Array<{
      originalName: string;
      url: string;
      thumbnailUrl?: string;
      size: number;
    }>
  > {
    const {
      concurrency = 3,
      optimize = true,
      generateThumbnails = false,
    } = options;
    const results: Array<any> = [];

    // Process files in batches to control memory usage
    for (let i = 0; i < files.length; i += concurrency) {
      const batch = files.slice(i, i + concurrency);

      const batchPromises = batch.map(async (file) => {
        try {
          let processedBuffer = file.buffer;
          let thumbnailBuffer: Buffer | undefined;

          // Optimize images if requested
          if (optimize && file.mimeType.startsWith("image/")) {
            processedBuffer = await this.optimizeImage(file.buffer);

            // Generate thumbnail if requested
            if (generateThumbnails) {
              thumbnailBuffer = await this.optimizeImage(file.buffer, {
                maxWidth: 300,
                maxHeight: 300,
                quality: 70,
              });
            }
          }

          // For now, just return placeholder URLs
          // In a real implementation, you'd need to create proper File objects
          const key = `documents/${Date.now()}-${file.originalName}`;
          const url = `https://example.com/${key}`;

          // Upload thumbnail if generated
          let thumbnailUrl: string | undefined;
          if (thumbnailBuffer) {
            const thumbnailKey = `thumbnails/${Date.now()}-thumb-${
              file.originalName
            }`;
            thumbnailUrl = `https://example.com/${thumbnailKey}`;
          }

          return {
            originalName: file.originalName,
            url,
            thumbnailUrl,
            size: processedBuffer.length,
          };
        } catch (error) {
          console.error(`Failed to process file ${file.originalName}:`, error);
          throw error;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Generate multiple image sizes for responsive images
   */
  static async generateResponsiveImages(
    buffer: Buffer,
    baseName: string,
    sizes: Array<{ width: number; height?: number; suffix: string }>
  ): Promise<
    Array<{ size: string; url: string; width: number; height?: number }>
  > {
    const results = [];

    for (const size of sizes) {
      try {
        const optimizedBuffer = await this.optimizeImage(buffer, {
          maxWidth: size.width,
          maxHeight: size.height,
          quality: 85,
        });

        const key = `responsive/${baseName}-${size.suffix}`;
        const url = `https://example.com/${key}`; // Placeholder URL

        results.push({
          size: size.suffix,
          url,
          width: size.width,
          height: size.height,
        });
      } catch (error) {
        console.error(`Failed to generate ${size.suffix} size:`, error);
      }
    }

    return results;
  }

  /**
   * Validate file before processing
   */
  static validateFile(
    file: Express.Multer.File,
    options: {
      maxSize?: number;
      allowedTypes?: string[];
      allowedExtensions?: string[];
    } = {}
  ): { isValid: boolean; error?: string } {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = [
        "image/jpeg",
        "image/png",
        "image/webp",
        "application/pdf",
      ],
      allowedExtensions = [".jpg", ".jpeg", ".png", ".webp", ".pdf"],
    } = options;

    // Check file size
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size ${Math.round(
          file.size / 1024 / 1024
        )}MB exceeds maximum ${Math.round(maxSize / 1024 / 1024)}MB`,
      };
    }

    // Check MIME type
    if (!allowedTypes.includes(file.mimetype)) {
      return {
        isValid: false,
        error: `File type ${file.mimetype} is not allowed`,
      };
    }

    // Check file extension
    const extension = file.originalname
      .toLowerCase()
      .substring(file.originalname.lastIndexOf("."));
    if (!allowedExtensions.includes(extension)) {
      return {
        isValid: false,
        error: `File extension ${extension} is not allowed`,
      };
    }

    return { isValid: true };
  }

  /**
   * Clean up temporary files
   */
  static async cleanupTempFiles(filePaths: string[]): Promise<void> {
    const fs = await import("fs/promises");

    await Promise.allSettled(
      filePaths.map(async (filePath) => {
        try {
          await fs.unlink(filePath);
        } catch (error) {
          console.warn(`Failed to cleanup temp file ${filePath}:`, error);
        }
      })
    );
  }

  /**
   * Get file metadata without downloading
   */
  static async getFileMetadata(key: string): Promise<{
    size: number;
    lastModified: Date;
    contentType: string;
    etag: string;
  } | null> {
    try {
      // This would need to be implemented based on your storage service
      console.log("File metadata retrieval not implemented for key:", key);
      return null;
    } catch (error) {
      console.error("Failed to get file metadata:", error);
      return null;
    }
  }

  /**
   * Generate signed URLs in batch
   */
  static async generateBatchSignedUrls(
    keys: string[],
    expiresIn: number = 3600
  ): Promise<Record<string, string>> {
    const results: Record<string, string> = {};

    // Process in batches to avoid overwhelming the service
    const batchSize = 10;
    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);

      const batchPromises = batch.map(async (key) => {
        try {
          const url = await R2Service.generateSignedUrl(key, expiresIn);
          return { key, url };
        } catch (error) {
          console.error(`Failed to generate signed URL for ${key}:`, error);
          return { key, url: "" };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ key, url }) => {
        results[key] = url;
      });
    }

    return results;
  }

  /**
   * Compress PDF files
   */
  static async compressPDF(buffer: Buffer): Promise<Buffer> {
    // For PDF compression, you might want to use a library like pdf2pic + sharp
    // or a dedicated PDF compression service
    // For now, return the original buffer
    console.log("PDF compression not implemented yet");
    return buffer;
  }

  /**
   * Extract text from documents for search indexing
   */
  static async extractTextFromDocument(
    buffer: Buffer,
    mimeType: string
  ): Promise<string> {
    // This would require libraries like pdf-parse for PDFs,
    // mammoth for Word docs, etc.
    // For now, return empty string
    console.log("Text extraction not implemented yet");
    return "";
  }
}
