import ExcelJS from "exceljs";
import { UserRole, TransactionStatus } from "@prisma/client";
import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();
import path from "path";
import fs from "fs";

export class StreamingExportService {
  private static readonly ROLE_TO_STAGE = {
    [UserRole.SUPERVISOR]: "SUPERVISOR",
    [UserRole.HEAD_CONSUMER_LENDING]: "HEAD_CONSUMER_LENDING",
    [UserRole.HEAD_RISK_MANAGEMENT]: "HEAD_RISK_MANAGEMENT",
    [UserRole.ACCOUNTANT]: "ACCOUNTANT",
    [UserRole.SUPER_ADMIN]: "SUPER_ADMIN", // Super admin can see all
    [UserRole.ACCOUNT_OFFICER]: "ACCOUNT_OFFICER", // Account officers can see their own
    [UserRole.MANAGING_DIRECTOR]: "MANAGING_DIRECTOR", // Managing director (if exists)
  };

  /**
   * Export pending approvals using streaming XLSX (Memory Optimized)
   */
  static async exportPendingApprovalsStreaming(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
    } = {}
  ): Promise<{ filePath: string; filename: string; totalRows: number }> {
    console.log("🚀 Starting streaming XLSX export (Memory Optimized)...");

    const startTime = Date.now();
    const startMemory = process.memoryUsage();
    console.log("📊 Initial memory usage:", {
      rss: Math.round(startMemory.rss / 1024 / 1024) + "MB",
      heapUsed: Math.round(startMemory.heapUsed / 1024 / 1024) + "MB",
    });

    // Generate filename and file path
    const timestamp = Date.now();
    const filename = `pending-approvals-${userRole.toLowerCase()}-${timestamp}.xlsx`;
    const filePath = path.join(process.cwd(), "temp", filename);

    // Ensure temp directory exists
    const tempDir = path.dirname(filePath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    try {
      // Create streaming workbook
      const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
        filename: filePath,
        useStyles: false,
        useSharedStrings: false,
      });

      const worksheet = workbook.addWorksheet("Pending Approvals", {
        properties: { tabColor: { argb: "FF0000FF" } },
      });

      // Add headers
      const headers = [
        "Transaction ID",
        "Customer Name",
        "Account name",
        "Account number",
        "Bank name",
        "Requested amount",
        "IPPIS No.",
        "Phone number",
        "BVN",
        "Organisation name",
        "Loan tenure",
        "Account Officer",
        "Gender",
        "DOB",
        "Marital Status",
        "NIN",
        "Email address",
        "House address",
        "Employment date",
        "Date to Accountant",
        "Next of kin name",
        "Next of kin phone No",
        "Next of kin email",
        "Next of kin Address",
      ];

      worksheet.addRow(headers).commit();
      console.log("✅ Headers added to streaming worksheet");

      // Get database query for streaming
      const whereClause = this.buildWhereClause(userRole, userId, filters);

      console.log("🔍 Starting database streaming query...");

      // Stream data directly from database
      let rowCount = 0;
      let batchCount = 0;
      const BATCH_SIZE = 100;

      // Use Prisma's cursor-based pagination for memory efficiency
      let cursor: string | undefined;
      let hasMore = true;

      while (hasMore) {
        console.log(
          `📦 Processing batch ${++batchCount} (starting from cursor: ${
            cursor || "beginning"
          })`
        );

        const batchQuery: any = {
          where: whereClause,
          select: {
            // Basic transaction info
            id: true,
            transactionId: true,
            loanType: true,
            requestedAmount: true,
            loanTenor: true,
            status: true,
            currentStage: true,
            createdAt: true,
            updatedAt: true,

            // Personal Information
            firstName: true,
            middleName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            maritalStatus: true,
            bvn: true,
            nin: true,

            // Address Information
            street: true,
            city: true,
            state: true,
            postalCode: true,

            // Employment Information
            organizationName: true,
            employmentDate: true,
            ippisNumber: true,

            // Account Information
            accountName: true,
            accountNumber: true,
            bankName: true,

            // Next of Kin Information
            nokFirstName: true,
            nokMiddleName: true,
            nokLastName: true,
            nokRelationship: true,
            nokPhoneNumber: true,
            nokEmail: true,
            nokStreet: true,
            nokCity: true,
            nokState: true,
            nokPostalCode: true,

            // Account Officer
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
              },
            },

            // Approvals for date tracking
            approvals: {
              select: {
                id: true,
                stage: true,
                action: true,
                approverId: true,
                createdAt: true,
              },
            },
          },
          take: BATCH_SIZE,
          orderBy: {
            createdAt: "desc",
          },
        };

        if (cursor) {
          batchQuery.cursor = { id: cursor };
          batchQuery.skip = 1; // Skip the cursor record
        }

        const transactions = await prisma.transaction.findMany(batchQuery);

        if (transactions.length === 0) {
          hasMore = false;
          break;
        }

        // Filter eligible transactions
        const eligibleTransactions = this.filterEligibleTransactions(
          transactions,
          userId,
          this.ROLE_TO_STAGE[userRole]
        );

        console.log(
          `📊 Batch ${batchCount}: ${transactions.length} fetched, ${eligibleTransactions.length} eligible`
        );

        // Stream each transaction to Excel
        for (const transaction of eligibleTransactions) {
          const accountantStageDate = this.getAccountantStageDate(
            transaction.approvals
          );

          const rowData = [
            transaction.transactionId || "",
            `${transaction.firstName || ""} ${transaction.middleName || ""} ${
              transaction.lastName || ""
            }`.trim(),
            transaction.accountName || "",
            transaction.accountNumber || "",
            transaction.bankName || "",
            transaction.requestedAmount || 0,
            transaction.ippisNumber || "",
            transaction.phoneNumber || "",
            transaction.bvn || "",
            transaction.organizationName || "",
            transaction.loanTenor || "",
            transaction.createdBy
              ? `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`
              : "",
            transaction.gender || "",
            transaction.dateOfBirth
              ? new Date(transaction.dateOfBirth).toLocaleDateString("en-US")
              : "",
            transaction.maritalStatus || "",
            transaction.nin || "",
            transaction.email || "",
            [
              transaction.street,
              transaction.city,
              transaction.state,
              transaction.postalCode,
            ]
              .filter(Boolean)
              .join(", "),
            transaction.employmentDate
              ? new Date(transaction.employmentDate).toLocaleDateString("en-US")
              : "",
            accountantStageDate,
            `${transaction.nokFirstName || ""} ${
              transaction.nokMiddleName || ""
            } ${transaction.nokLastName || ""}`.trim(),
            transaction.nokPhoneNumber || "",
            transaction.nokEmail || "",
            [
              transaction.nokStreet,
              transaction.nokCity,
              transaction.nokState,
              transaction.nokPostalCode,
            ]
              .filter(Boolean)
              .join(", "),
          ];

          worksheet.addRow(rowData).commit();
          rowCount++;

          // Breathe every 50 rows to prevent blocking
          if (rowCount % 50 === 0) {
            await new Promise((resolve) => setImmediate(resolve));

            // Log memory usage periodically
            if (rowCount % 500 === 0) {
              const currentMemory = process.memoryUsage();
              console.log(`💾 Memory at row ${rowCount}:`, {
                heapUsed:
                  Math.round(currentMemory.heapUsed / 1024 / 1024) + "MB",
              });
            }
          }
        }

        // Set cursor for next batch
        if (transactions.length === BATCH_SIZE) {
          cursor = transactions[transactions.length - 1].id;
        } else {
          hasMore = false;
        }

        // Force garbage collection between batches
        if (global.gc && batchCount % 5 === 0) {
          global.gc();
          console.log("🗑️ Garbage collection triggered");
        }
      }

      // Finalize the workbook
      await workbook.commit();

      const endTime = Date.now();
      const endMemory = process.memoryUsage();

      console.log("✅ Streaming XLSX export completed:", {
        filename,
        totalRows: rowCount,
        duration: `${(endTime - startTime) / 1000}s`,
        finalMemory: Math.round(endMemory.heapUsed / 1024 / 1024) + "MB",
        memoryDelta:
          Math.round(
            (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024
          ) + "MB",
      });

      return {
        filePath,
        filename,
        totalRows: rowCount,
      };
    } catch (error) {
      console.error("❌ Streaming XLSX export failed:", error);

      // Clean up file on error
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      throw error;
    }
  }

  /**
   * Build where clause for database query
   */
  private static buildWhereClause(
    userRole: UserRole,
    userId: string,
    filters: any
  ) {
    const stage = this.ROLE_TO_STAGE[userRole];
    if (!stage) {
      throw new Error(`No approval stage found for role: ${userRole}`);
    }

    const whereClause: any = {
      currentStage: stage,
      status: {
        in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
      },
    };

    // Role-specific status filters
    if (userRole === UserRole.SUPERVISOR) {
      whereClause.status.in = [TransactionStatus.SUBMITTED];
    }
    if (userRole === UserRole.ACCOUNTANT) {
      whereClause.status.in.push(TransactionStatus.APPROVED);
    }

    // Apply filters
    if (filters.accountOfficerId) {
      whereClause.createdById = filters.accountOfficerId;
    }
    if (filters.accountOfficerIds && filters.accountOfficerIds.length > 0) {
      whereClause.createdById = { in: filters.accountOfficerIds };
    }
    if (filters.loanType) {
      whereClause.loanType = filters.loanType;
    }
    if (filters.dateFrom || filters.dateTo) {
      whereClause.createdAt = {};
      if (filters.dateFrom) {
        whereClause.createdAt.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        const endDate = new Date(filters.dateTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt.lte = endDate;
      }
    }
    if (filters.search) {
      whereClause.OR = [
        { transactionId: { contains: filters.search, mode: "insensitive" } },
        { firstName: { contains: filters.search, mode: "insensitive" } },
        { lastName: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ];
    }

    return whereClause;
  }

  /**
   * Filter transactions the user has already acted on
   */
  private static filterEligibleTransactions(
    transactions: any[],
    userId: string,
    stage: string
  ) {
    return transactions.filter((transaction: any) => {
      const userApprovals = transaction.approvals.filter(
        (approval: any) =>
          approval.approverId === userId && approval.stage === stage
      );

      if (userApprovals.length === 0) {
        return true;
      }

      const lastUserApproval = userApprovals.sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

      return transaction.updatedAt > lastUserApproval.createdAt;
    });
  }

  /**
   * Get the date when transaction reached accountant stage
   */
  private static getAccountantStageDate(approvals: any[]): string {
    if (!approvals || approvals.length === 0) {
      return "";
    }

    // Find Head Risk Management approval
    const headRiskApproval = approvals.find(
      (approval: any) =>
        approval.stage === "HEAD_RISK_MANAGEMENT" &&
        approval.action === "APPROVED"
    );

    if (headRiskApproval && headRiskApproval.createdAt) {
      return new Date(headRiskApproval.createdAt).toLocaleDateString("en-US");
    }

    // Fallback to latest approval
    const latestApproval = approvals
      .filter((approval: any) => approval.action === "APPROVED")
      .sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];

    if (latestApproval && latestApproval.createdAt) {
      return new Date(latestApproval.createdAt).toLocaleDateString("en-US");
    }

    return "";
  }

  /**
   * Clean up old export files
   */
  static async cleanupOldExports(maxAgeHours: number = 24) {
    const tempDir = path.join(process.cwd(), "temp");
    if (!fs.existsSync(tempDir)) {
      return;
    }

    const files = fs.readdirSync(tempDir);
    const now = Date.now();
    let deletedCount = 0;

    for (const file of files) {
      if (file.endsWith(".xlsx")) {
        const filePath = path.join(tempDir, file);
        const stats = fs.statSync(filePath);
        const ageHours = (now - stats.mtime.getTime()) / (1000 * 60 * 60);

        if (ageHours > maxAgeHours) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }
    }

    if (deletedCount > 0) {
      console.log(`🗑️ Cleaned up ${deletedCount} old export files`);
    }
  }
}
