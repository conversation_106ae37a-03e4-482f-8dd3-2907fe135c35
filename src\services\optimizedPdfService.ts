import PDFDocument from "pdfkit";
import axios from "axios";
import { CacheService } from "./cacheService";

/**
 * Optimized PDF Service with caching and performance improvements
 */
export class OptimizedPDFService {
  private static readonly LOGO_URL =
    "https://jy0s2swu0k.ufs.sh/f/LKSiq2B5GRS8Y1Ge3lQJkuxwrQE08LAg471DXC56PpqsycGj";
  
  private static logoBuffer: Buffer | null = null;
  private static logoDownloadPromise: Promise<Buffer | null> | null = null;

  /**
   * Download and cache logo with singleton pattern
   */
  private static async getLogoBuffer(): Promise<Buffer | null> {
    // Return cached logo if available
    if (this.logoBuffer) {
      return this.logoBuffer;
    }

    // If download is in progress, wait for it
    if (this.logoDownloadPromise) {
      return await this.logoDownloadPromise;
    }

    // Start logo download
    this.logoDownloadPromise = this.downloadLogo();
    this.logoBuffer = await this.logoDownloadPromise;
    this.logoDownloadPromise = null;

    return this.logoBuffer;
  }

  /**
   * Download logo from URL with error handling and caching
   */
  private static async downloadLogo(): Promise<Buffer | null> {
    try {
      console.log("📥 Downloading AMS logo from URL...");
      const response = await axios({
        method: "GET",
        url: this.LOGO_URL,
        responseType: "arraybuffer",
        timeout: 10000, // 10 second timeout
        maxContentLength: 5 * 1024 * 1024, // 5MB max
        headers: {
          'User-Agent': 'AMS-Loan-System/1.0',
        },
      });

      console.log("✅ Logo downloaded successfully");
      return Buffer.from(response.data);
    } catch (error) {
      console.error("❌ Failed to download logo:", error);
      return null;
    }
  }

  /**
   * Generate transaction PDF with caching and optimization
   */
  static async generateTransactionPDF(transaction: any): Promise<Buffer> {
    // Check cache first
    const cacheKey = `pdf_${transaction.id}_${transaction.updatedAt}`;
    const cachedPDF = CacheService.get<Buffer>(cacheKey);
    
    if (cachedPDF) {
      console.log(`📄 Using cached PDF for transaction ${transaction.transactionId}`);
      return cachedPDF;
    }

    console.log(`📄 Generating new PDF for transaction ${transaction.transactionId}`);

    return new Promise(async (resolve, reject) => {
      try {
        // Get logo buffer (cached)
        const logoBuffer = await this.getLogoBuffer();

        const doc = new PDFDocument({
          margin: 40,
          size: "A4",
          compress: true, // Enable PDF compression
          info: {
            Title: `AMS Loan Application - ${transaction.transactionId}`,
            Author: "AMS Loan Management System",
            Subject: "Loan Application Document",
            Creator: "AMS System",
            CreationDate: new Date(),
          },
        });

        const buffers: Buffer[] = [];

        doc.on("data", (chunk) => buffers.push(chunk));
        doc.on("end", () => {
          const pdfBuffer = Buffer.concat(buffers);
          
          // Cache the generated PDF for 1 hour
          CacheService.set(cacheKey, pdfBuffer, 3600);
          
          console.log(`✅ PDF generated and cached for transaction ${transaction.transactionId}`);
          resolve(pdfBuffer);
        });

        doc.on("error", (error) => {
          console.error("❌ PDF generation error:", error);
          reject(error);
        });

        // Create optimized PDF content
        await this.createOptimizedPDFContent(doc, logoBuffer, transaction);

        doc.end();
      } catch (error) {
        console.error("❌ PDF generation failed:", error);
        reject(error);
      }
    });
  }

  /**
   * Create optimized PDF content with better performance
   */
  private static async createOptimizedPDFContent(
    doc: PDFKit.PDFDocument,
    logoBuffer: Buffer | null,
    transaction: any
  ): Promise<void> {
    // Create professional header with cached logo
    await this.createProfessionalHeader(doc, logoBuffer, transaction);

    // Add sections efficiently
    this.addCustomerInformation(doc, transaction);
    this.addLoanInformation(doc, transaction);
    this.addEmploymentInformation(doc, transaction);
    this.addApprovalHistory(doc, transaction);
    this.addProfessionalFooter(doc);
  }

  /**
   * Create professional header with optimized logo handling
   */
  private static async createProfessionalHeader(
    doc: PDFKit.PDFDocument,
    logoBuffer: Buffer | null,
    transaction: any
  ): Promise<void> {
    const pageWidth = doc.page.width;
    const margin = doc.page.margins.left;

    // Add logo if available
    if (logoBuffer) {
      try {
        doc.image(logoBuffer, margin, 40, {
          width: 80,
          height: 60,
        });
      } catch (error) {
        console.warn("⚠️ Failed to add logo to PDF:", error);
      }
    }

    // Company header
    doc
      .fontSize(20)
      .font("Helvetica-Bold")
      .text("AMS LOAN MANAGEMENT SYSTEM", margin + 100, 50, {
        width: pageWidth - margin - 140,
        align: "left",
      });

    doc
      .fontSize(12)
      .font("Helvetica")
      .text("Loan Application Document", margin + 100, 75, {
        width: pageWidth - margin - 140,
        align: "left",
      });

    // Transaction details box
    const boxY = 110;
    doc
      .rect(margin, boxY, pageWidth - 2 * margin, 60)
      .stroke("#cccccc")
      .fillColor("#f8f9fa")
      .rect(margin + 1, boxY + 1, pageWidth - 2 * margin - 2, 58)
      .fill();

    doc
      .fillColor("#000000")
      .fontSize(14)
      .font("Helvetica-Bold")
      .text(`Transaction ID: ${transaction.transactionId}`, margin + 10, boxY + 15);

    doc
      .fontSize(10)
      .font("Helvetica")
      .text(`Status: ${transaction.status}`, margin + 10, boxY + 35)
      .text(`Generated: ${new Date().toLocaleDateString()}`, margin + 200, boxY + 35);

    // Move cursor down
    doc.y = boxY + 80;
  }

  /**
   * Add customer information section
   */
  private static addCustomerInformation(doc: PDFKit.PDFDocument, transaction: any): void {
    this.addSectionHeader(doc, "CUSTOMER INFORMATION");

    const customerInfo = [
      { label: "Full Name", value: `${transaction.firstName || ""} ${transaction.middleName || ""} ${transaction.lastName || ""}`.trim() },
      { label: "Email", value: transaction.email },
      { label: "Phone Number", value: transaction.phoneNumber },
      { label: "BVN", value: transaction.bvn },
      { label: "NIN", value: transaction.nin },
      { label: "Date of Birth", value: transaction.dateOfBirth ? new Date(transaction.dateOfBirth).toLocaleDateString() : "" },
      { label: "Gender", value: transaction.gender },
      { label: "Marital Status", value: transaction.maritalStatus },
    ];

    this.addInfoGrid(doc, customerInfo);
  }

  /**
   * Add loan information section
   */
  private static addLoanInformation(doc: PDFKit.PDFDocument, transaction: any): void {
    this.addSectionHeader(doc, "LOAN INFORMATION");

    const loanInfo = [
      { label: "Loan Type", value: transaction.loanTypeConfig?.name || transaction.loanType },
      { label: "Requested Amount", value: transaction.requestedAmount ? `NGN ${transaction.requestedAmount.toLocaleString()}` : "" },
      { label: "Loan Tenor", value: transaction.loanTenor ? `${transaction.loanTenor} months` : "" },
      { label: "Repayment Mode", value: transaction.repaymentMode },
      { label: "Purpose of Loan", value: transaction.purposeOfLoan },
      { label: "Gross Pay", value: transaction.grossPay ? `NGN ${transaction.grossPay.toLocaleString()}` : "" },
      { label: "Net Pay", value: transaction.netPay ? `NGN ${transaction.netPay.toLocaleString()}` : "" },
    ];

    this.addInfoGrid(doc, loanInfo);
  }

  /**
   * Add employment information section
   */
  private static addEmploymentInformation(doc: PDFKit.PDFDocument, transaction: any): void {
    this.addSectionHeader(doc, "EMPLOYMENT INFORMATION");

    const employmentInfo = [
      { label: "Organization", value: transaction.organizationName },
      { label: "IPPIS Number", value: transaction.ippisNumber },
      { label: "Employment Date", value: transaction.employmentDate ? new Date(transaction.employmentDate).toLocaleDateString() : "" },
      { label: "Business Name", value: transaction.businessName },
      { label: "Registration Number", value: transaction.registrationNumber },
    ];

    this.addInfoGrid(doc, employmentInfo.filter(info => info.value));
  }

  /**
   * Add approval history section
   */
  private static addApprovalHistory(doc: PDFKit.PDFDocument, transaction: any): void {
    if (!transaction.approvals || transaction.approvals.length === 0) {
      return;
    }

    this.addSectionHeader(doc, "APPROVAL HISTORY");

    transaction.approvals.forEach((approval: any, index: number) => {
      const approverName = `${approval.approver.firstName} ${approval.approver.lastName}`;
      const date = new Date(approval.createdAt).toLocaleDateString();
      
      doc
        .fontSize(10)
        .font("Helvetica-Bold")
        .text(`${index + 1}. ${approval.stage} - ${approverName}`, { continued: true })
        .font("Helvetica")
        .text(` (${date})`);

      doc
        .fontSize(9)
        .text(`Action: ${approval.action}`, { indent: 20 });

      if (approval.comments) {
        doc.text(`Comments: ${approval.comments}`, { indent: 20 });
      }

      doc.moveDown(0.5);
    });
  }

  /**
   * Add professional footer
   */
  private static addProfessionalFooter(doc: PDFKit.PDFDocument): void {
    const pageHeight = doc.page.height;
    const margin = doc.page.margins.left;
    const footerY = pageHeight - 60;

    doc
      .fontSize(8)
      .font("Helvetica")
      .fillColor("#666666")
      .text(
        "This document was generated automatically by AMS Loan Management System",
        margin,
        footerY,
        { align: "center" }
      )
      .text(
        `Generated on ${new Date().toLocaleString()}`,
        margin,
        footerY + 15,
        { align: "center" }
      );
  }

  /**
   * Add section header
   */
  private static addSectionHeader(doc: PDFKit.PDFDocument, title: string): void {
    doc.moveDown(1);
    doc
      .fontSize(12)
      .font("Helvetica-Bold")
      .fillColor("#2c3e50")
      .text(title);
    
    doc
      .moveTo(doc.page.margins.left, doc.y + 5)
      .lineTo(doc.page.width - doc.page.margins.right, doc.y + 5)
      .stroke("#2c3e50");
    
    doc.moveDown(0.5);
  }

  /**
   * Add information grid
   */
  private static addInfoGrid(doc: PDFKit.PDFDocument, items: Array<{ label: string; value: any }>): void {
    const validItems = items.filter(item => item.value);
    
    validItems.forEach((item, index) => {
      if (index % 2 === 0) {
        doc.moveDown(0.3);
      }

      const x = index % 2 === 0 ? doc.page.margins.left : doc.page.width / 2;
      const y = doc.y;

      doc
        .fontSize(9)
        .font("Helvetica-Bold")
        .fillColor("#34495e")
        .text(`${item.label}:`, x, y, { width: 200 });

      doc
        .font("Helvetica")
        .fillColor("#000000")
        .text(item.value || "N/A", x, y + 12, { width: 200 });
    });

    doc.moveDown(1);
  }

  /**
   * Clear cached logo (for testing or updates)
   */
  static clearLogoCache(): void {
    this.logoBuffer = null;
    this.logoDownloadPromise = null;
  }

  /**
   * Get PDF generation statistics
   */
  static getPDFStats(): {
    logosCached: boolean;
    cacheHitRate: number;
  } {
    return {
      logosCached: this.logoBuffer !== null,
      cacheHitRate: CacheService.getCacheHitRate(),
    };
  }
}
