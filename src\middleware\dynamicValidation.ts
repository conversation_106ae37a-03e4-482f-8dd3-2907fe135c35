import { Request, Response, NextFunction } from "express";
import { LoanType } from "@prisma/client";
import { getPersonalInfoSchemaForLoanType } from "../utils/validation";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import prisma from "../db/db";

export const validatePersonalInfoByLoanType = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { transactionId } = req.params;
    const userId = req.user!.id;

    // Get the transaction to check its loan type
    const transaction = await prisma.transaction.findFirst({
      where: {
        id: transactionId,
        createdById: userId,
      },
      select: {
        loanType: true,
      },
    });

    if (!transaction) {
      return ResponseHandler.error(
        res,
        "Transaction not found",
        undefined,
        404
      );
    }

    // If no loan type is set, use default validation
    if (!transaction.loanType) {
      return ResponseHandler.error(
        res,
        "Please select a loan type first",
        undefined,
        400
      );
    }

    // Get the appropriate schema for the loan type
    const schema = getPersonalInfoSchemaForLoanType(transaction.loanType);

    // Validate the request body
    const result = schema.safeParse(req.body);

    if (!result.success) {
      const errors: Record<string, string[]> = {};

      result.error.errors.forEach((err) => {
        const path = err.path.join(".");
        if (!errors[path]) {
          errors[path] = [];
        }
        errors[path].push(err.message);
      });

      return ResponseHandler.validationError(
        res,
        "Validation failed",
        errors,
        400
      );
    }

    // Replace req.body with validated data
    req.body = result.data;
    next();
  } catch (error) {
    console.error("Dynamic validation error:", error);
    return ResponseHandler.error(res, "Validation error", undefined, 500);
  }
};
