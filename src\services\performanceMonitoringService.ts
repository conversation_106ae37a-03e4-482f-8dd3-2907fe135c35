import { Request, Response, NextFunction } from "express";
import { CacheService } from "./cacheService";

/**
 * Performance monitoring service for tracking API performance and system health
 */
export class PerformanceMonitoringService {
  private static requestTimes: Map<string, number[]> = new Map();
  private static readonly MAX_STORED_TIMES = 100; // Keep last 100 requests per endpoint

  /**
   * Middleware to track request performance
   */
  static trackRequestPerformance() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      const endpoint = `${req.method} ${req.route?.path || req.path}`;

      // Store original end method
      const originalEnd = res.end;

      // Override end method to capture response time
      res.end = function(chunk?: any, encoding?: any) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // Store response time for this endpoint
        if (!PerformanceMonitoringService.requestTimes.has(endpoint)) {
          PerformanceMonitoringService.requestTimes.set(endpoint, []);
        }

        const times = PerformanceMonitoringService.requestTimes.get(endpoint)!;
        times.push(responseTime);

        // Keep only the last MAX_STORED_TIMES entries
        if (times.length > PerformanceMonitoringService.MAX_STORED_TIMES) {
          times.shift();
        }

        // Log slow requests (>2 seconds)
        if (responseTime > 2000) {
          console.warn(`🐌 Slow request detected: ${endpoint} took ${responseTime}ms`);
        }

        // Add performance headers
        res.setHeader('X-Response-Time', `${responseTime}ms`);
        res.setHeader('X-Timestamp', new Date().toISOString());

        // Call original end method
        return originalEnd.call(this, chunk, encoding);
      };

      next();
    };
  }

  /**
   * Get performance statistics for all endpoints
   */
  static getPerformanceStats() {
    const stats: Record<string, any> = {};

    for (const [endpoint, times] of this.requestTimes.entries()) {
      if (times.length === 0) continue;

      const sortedTimes = [...times].sort((a, b) => a - b);
      const sum = times.reduce((a, b) => a + b, 0);

      stats[endpoint] = {
        requestCount: times.length,
        averageTime: Math.round(sum / times.length),
        minTime: Math.min(...times),
        maxTime: Math.max(...times),
        medianTime: sortedTimes[Math.floor(sortedTimes.length / 2)],
        p95Time: sortedTimes[Math.floor(sortedTimes.length * 0.95)],
        p99Time: sortedTimes[Math.floor(sortedTimes.length * 0.99)],
      };
    }

    return stats;
  }

  /**
   * Get system health metrics
   */
  static getSystemHealth() {
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();
    const cacheStats = CacheService.getStats();
    const cacheHitRate = CacheService.getCacheHitRate();

    return {
      timestamp: new Date().toISOString(),
      uptime: {
        seconds: Math.floor(uptime),
        formatted: this.formatUptime(uptime),
      },
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024), // MB
      },
      cache: {
        keys: cacheStats.keys,
        hits: cacheStats.hits,
        misses: cacheStats.misses,
        hitRate: `${cacheHitRate.toFixed(2)}%`,
        ksize: cacheStats.ksize,
        vsize: cacheStats.vsize,
      },
      performance: this.getPerformanceStats(),
    };
  }

  /**
   * Get top slowest endpoints
   */
  static getSlowestEndpoints(limit: number = 10) {
    const stats = this.getPerformanceStats();
    
    return Object.entries(stats)
      .sort(([, a], [, b]) => (b as any).averageTime - (a as any).averageTime)
      .slice(0, limit)
      .map(([endpoint, data]) => ({
        endpoint,
        ...data,
      }));
  }

  /**
   * Get endpoints with high error rates
   */
  static getHighErrorRateEndpoints() {
    // This would require additional tracking of error rates
    // For now, return empty array - can be implemented later
    return [];
  }

  /**
   * Clear performance data
   */
  static clearPerformanceData() {
    this.requestTimes.clear();
  }

  /**
   * Log performance summary
   */
  static logPerformanceSummary() {
    const health = this.getSystemHealth();
    const slowest = this.getSlowestEndpoints(5);

    console.log("📊 Performance Summary:", {
      uptime: health.uptime.formatted,
      memory: `${health.memory.heapUsed}MB / ${health.memory.heapTotal}MB`,
      cacheHitRate: health.cache.hitRate,
      slowestEndpoints: slowest.map(e => ({
        endpoint: e.endpoint,
        avgTime: `${e.averageTime}ms`,
        requests: e.requestCount,
      })),
    });
  }

  /**
   * Format uptime in human readable format
   */
  private static formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (secs > 0) parts.push(`${secs}s`);

    return parts.join(' ') || '0s';
  }

  /**
   * Middleware to add cache headers for static content
   */
  static addCacheHeaders(maxAge: number = 3600) {
    return (req: Request, res: Response, next: NextFunction) => {
      // Only cache GET requests
      if (req.method === 'GET') {
        res.setHeader('Cache-Control', `public, max-age=${maxAge}`);
        res.setHeader('ETag', `"${Date.now()}"`);
      }
      next();
    };
  }

  /**
   * Health check endpoint data
   */
  static getHealthCheck() {
    const health = this.getSystemHealth();
    const isHealthy = health.memory.heapUsed < 500; // Less than 500MB

    return {
      status: isHealthy ? 'healthy' : 'warning',
      timestamp: health.timestamp,
      uptime: health.uptime.formatted,
      memory: health.memory,
      cache: health.cache,
      checks: {
        memory: {
          status: health.memory.heapUsed < 500 ? 'pass' : 'warn',
          value: `${health.memory.heapUsed}MB`,
          threshold: '500MB',
        },
        cache: {
          status: parseFloat(health.cache.hitRate) > 70 ? 'pass' : 'warn',
          value: health.cache.hitRate,
          threshold: '70%',
        },
      },
    };
  }
}
