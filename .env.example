# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ams_loan_db"

# Server
PORT=8000
NODE_ENV=development

# JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_SECRET=your_refresh_token_secret_here
REFRESH_TOKEN_EXPIRES_IN=7d

# OTP Configuration
OTP_EXPIRES_IN=300000
OTP_SECRET=your_otp_secret_here

# Email Configuration (for OTP and notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME="AMS Loan System"

# Frontend URL (for email links)
FRONTEND_URL="http://localhost:3000"

# File Upload
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Session
SESSION_TIMEOUT=3600000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# R2 (Cloudflare R2) Configuration
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_BUCKET_NAME=your-bucket-name
R2_REGION=auto
R2_PUBLIC_URL=https://your-custom-domain.com

# Database Cleanup Configuration
OTP_RETENTION_HOURS=1
NOTIFICATION_RETENTION_DAYS=10
PASSWORD_RESET_RETENTION_HOURS=1
CLEANUP_BATCH_SIZE=1000
OTP_CLEANUP_SCHEDULE="0 * * * *"
NOTIFICATION_CLEANUP_SCHEDULE="0 2 * * *"
PASSWORD_RESET_CLEANUP_SCHEDULE="0 * * * *"
FULL_CLEANUP_SCHEDULE="0 3 * * 0"
TZ=UTC
