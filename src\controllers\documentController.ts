import { Response } from "express";
import { DocumentService } from "../services/documentService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";

export class DocumentController {
  static uploadDocument = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const file = req.file;
      const { metadata } = req.body;

      if (!file) {
        return ResponseHandler.error(res, "No file uploaded");
      }

      const result = await DocumentService.uploadDocument(
        transactionId,
        file,
        userId,
        metadata
      );

      ResponseHandler.success(
        res,
        "Document uploaded successfully",
        result,
        201
      );
    }
  );

  static uploadDocumentFlexible = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const files = req.files as Express.Multer.File[];
      const { metadata } = req.body;

      // Parse metadata if it's a string (for multiple files, it should be an array)
      let parsedMetadata: string[] | undefined;
      if (metadata) {
        try {
          parsedMetadata = Array.isArray(metadata)
            ? metadata
            : JSON.parse(metadata);
        } catch {
          parsedMetadata = Array.isArray(metadata) ? metadata : [metadata];
        }
      }

      // Log upload attempt
      console.log(
        `Upload attempt: ${files.length} files for transaction ${transactionId}`
      );

      if (!files || files.length === 0) {
        return ResponseHandler.error(res, "No files uploaded");
      }

      if (files.length > 10) {
        return ResponseHandler.error(
          res,
          "Maximum 10 files allowed per upload"
        );
      }

      // Handle single file upload (backward compatibility)
      if (files.length === 1) {
        const singleMetadata =
          parsedMetadata && parsedMetadata[0] ? parsedMetadata[0] : undefined;
        const result = await DocumentService.uploadDocument(
          transactionId,
          files[0],
          userId,
          singleMetadata
        );
        return ResponseHandler.success(
          res,
          "Document uploaded successfully",
          result,
          201
        );
      }

      // Handle multiple files upload
      const results = await DocumentService.uploadMultipleDocuments(
        transactionId,
        files,
        userId,
        parsedMetadata
      );

      const message =
        results.failed.length > 0
          ? `${results.successful.length} documents uploaded successfully, ${results.failed.length} failed`
          : `${results.successful.length} documents uploaded successfully`;

      ResponseHandler.success(
        res,
        message,
        {
          successful: results.successful,
          failed: results.failed,
          totalUploaded: results.successful.length,
          totalFailed: results.failed.length,
        },
        201
      );
    }
  );

  static getTransactionDocuments = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const documents = await DocumentService.getTransactionDocuments(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Documents retrieved successfully",
        documents
      );
    }
  );

  static deleteDocument = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { documentId } = req.params;
      const userId = req.user!.id;

      await DocumentService.deleteDocument(documentId, userId);

      ResponseHandler.success(res, "Document deleted successfully");
    }
  );

  static downloadDocument = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { documentId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      // Validate documentId format (MongoDB ObjectID)
      if (!/^[0-9a-fA-F]{24}$/.test(documentId)) {
        return ResponseHandler.error(
          res,
          "Invalid document ID format. Expected MongoDB ObjectID.",
          undefined,
          400
        );
      }

      const document = await DocumentService.getDocumentFile(
        documentId,
        userId,
        userRole
      );

      // For R2, redirect to the public URL
      // The browser will handle the download
      res.redirect(document.fileUrl);
    }
  );

  static getDocumentStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      const stats = await DocumentService.getDocumentStats(
        transactionId,
        userId,
        userRole
      );

      ResponseHandler.success(
        res,
        "Document statistics retrieved successfully",
        stats
      );
    }
  );

  static getDocumentSignedUrl = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { documentId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      // Get expiration time from query parameter (default 1 hour)
      const expiresInSeconds = parseInt(req.query.expiresIn as string) || 3600;

      // Validate expiration time (max 24 hours for security)
      if (expiresInSeconds > 86400) {
        return ResponseHandler.error(
          res,
          "Expiration time cannot exceed 24 hours",
          undefined,
          400
        );
      }

      // Validate documentId format (MongoDB ObjectID)
      if (!/^[0-9a-fA-F]{24}$/.test(documentId)) {
        return ResponseHandler.error(
          res,
          "Invalid document ID format. Expected MongoDB ObjectID.",
          undefined,
          400
        );
      }

      const result = await DocumentService.generateDocumentSignedUrl(
        documentId,
        userId,
        userRole,
        expiresInSeconds
      );

      ResponseHandler.success(res, "Signed URL generated successfully", result);
    }
  );
}
