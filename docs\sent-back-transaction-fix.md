# Sent-Back Transaction 409 Error Fix

## Issue Description

**Problem**: Account officers are getting 409 errors when trying to update and resubmit transactions that have been sent back by approvers.

**Example Transaction Data**:

```json
{
  "_id": "688c7bf2c84f9b415f221973",
  "transactionId": "TXN17540372344587849",
  "status": "SENT_BACK",
  "currentStage": "ACCOUNT_OFFICER",
  "createdById": "687a0bc6b2114af448562c6a",
  "firstName": "MUSTAPHA",
  "lastName": "AMINU",
  "bvn": "***********"
}
```

**Error Message**: "Failed to process transaction. Request failed with status code 409"

## Root Cause Analysis

The issue occurs because:

1. **Wrong Endpoint Usage**: The frontend might be calling the approval endpoint (`POST /api/v1/approvals/{id}`) instead of the transaction submission endpoint (`PUT /api/v1/transactions/{id}/submit`)

2. **Workflow Confusion**: Account officers don't use the approval system - they use the transaction system to submit/resubmit transactions

3. **Poor Error Messages**: The original 409 errors didn't clearly explain what was wrong or how to fix it

## The Fix

### 1. Enhanced Approval Service Error Handling

**File**: `src/services/approvalService.ts`

**Change**: Added specific error message for account officers who try to use the approval endpoint:

```typescript
// Special error message for account officers
if (userRole === UserRole.ACCOUNT_OFFICER) {
  throw new OperationalError(
    `Account officers cannot use the approval endpoint. To resubmit a transaction that was sent back, please use the transaction submission endpoint: PUT /api/v1/transactions/${transaction.transactionId}/submit. This endpoint is specifically designed for account officers to submit or resubmit transactions.`,
    409 // Use 409 to indicate workflow conflict
  );
}
```

### 2. Improved Transaction Service Error Messages

**File**: `src/services/transactionService.ts`

**Change**: Added detailed error messages for different transaction status conflicts:

```typescript
// Provide specific error messages based on the situation
if (existingTransaction) {
  if (existingTransaction.createdById !== userId) {
    throw new OperationalError(
      "You can only edit transactions that you created. This transaction belongs to another user.",
      403
    );
  }

  if (
    existingTransaction.status === TransactionStatus.SUBMITTED ||
    existingTransaction.status === TransactionStatus.IN_PROGRESS
  ) {
    throw new OperationalError(
      "This transaction is currently under review and cannot be edited. If you need to make changes, please wait for it to be sent back or contact your supervisor.",
      409 // Use 409 for status conflicts
    );
  }

  // ... more specific error messages for each status
}
```

## Correct Workflow for Sent-Back Transactions

### ✅ **Correct Process**:

1. **Transaction is sent back** by approver:

   - Status: `SENT_BACK`
   - Current Stage: `ACCOUNT_OFFICER`

2. **Account officer can update** transaction details:

   ```http
   PATCH /api/v1/transactions/{id}/personal-info
   PATCH /api/v1/transactions/{id}/next-of-kin
   PATCH /api/v1/transactions/{id}/loan-info
   ```

3. **Account officer resubmits** transaction:
   ```http
   POST /api/v1/transactions/{id}/submit
   ```

### ❌ **Incorrect Process**:

Account officer trying to use approval endpoint:

```http
POST /api/v1/approvals/{id}  // This will now give a clear 409 error
```

## Error Messages Before vs After

### Before:

```
Failed to process transaction. Request failed with status code 409
```

### After:

```
Account officers cannot use the approval endpoint. To resubmit a transaction that was sent back, please use the transaction submission endpoint: PUT /api/v1/transactions/TXN17540372344587849/submit. This endpoint is specifically designed for account officers to submit or resubmit transactions.
```

## Testing the Fix

### 1. Run the Test Script

```bash
node test-sent-back-transaction.js
```

### 2. Manual Testing Steps

1. **Create a transaction** as an account officer
2. **Submit it** for approval
3. **Send it back** as a supervisor/approver
4. **Try to update** the transaction as the account officer
5. **Resubmit** using the correct endpoint

### 3. Expected Results

- ✅ Updating transaction details should work
- ✅ Resubmitting via PUT `/transactions/{id}/submit` should work
- ❌ Using POST `/approvals/{id}` should give clear error message

## Common Causes of 409 Errors in This Scenario

### 1. **Wrong Endpoint** (Most Likely)

- **Problem**: Frontend calling approval endpoint instead of submission endpoint
- **Solution**: Update frontend to use correct endpoints
- **Error**: Clear message directing to correct endpoint

### 2. **BVN Uniqueness Conflict**

- **Problem**: BVN already exists in another transaction
- **Solution**: Check database for duplicate BVNs
- **Error**: "This BVN (***********) is already associated with an existing loan application..."

### 3. **Transaction Ownership**

- **Problem**: User trying to edit transaction they didn't create
- **Solution**: Verify user is the original creator
- **Error**: "You can only edit transactions that you created..."

### 4. **Status Conflict**

- **Problem**: Transaction not in editable status
- **Solution**: Verify transaction is in SENT_BACK or DRAFT status
- **Error**: Specific message based on current status

## Frontend Integration

### Required Changes

1. **Check Current Implementation**:

   - Verify which endpoints are being called for sent-back transactions
   - Ensure proper error handling for 409 responses

2. **Update API Calls**:

   ```javascript
   // ✅ Correct for account officers
   const resubmitTransaction = async (transactionId) => {
     const response = await fetch(
       `/api/v1/transactions/${transactionId}/submit`,
       {
         method: "POST",
         headers: { Authorization: `Bearer ${token}` },
       }
     );
     return response;
   };

   // ❌ Incorrect for account officers
   const approveTransaction = async (transactionId) => {
     const response = await fetch(`/api/v1/approvals/${transactionId}`, {
       method: "POST",
       body: JSON.stringify({ action: "APPROVE" }),
     });
     return response; // Will get 409 error with clear message
   };
   ```

3. **Error Handling**:
   ```javascript
   try {
     await updateTransaction(transactionId, data);
   } catch (error) {
     if (error.status === 409) {
       // Show the detailed error message to user
       showError(error.message);
       // The message will now include specific guidance
     }
   }
   ```

## Database Verification

### Check for BVN Conflicts

```javascript
// Check if BVN exists in other transactions
db.transactions.find({
  bvn: "***********",
  _id: { $ne: ObjectId("688c7bf2c84f9b415f221973") },
});
```

### Verify Transaction Status

```javascript
// Check transaction details
db.transactions.findOne({
  _id: ObjectId("688c7bf2c84f9b415f221973"),
});
```

## Monitoring and Debugging

### 1. Server Logs

Look for these log entries:

```
🔍 Checking existing approval for transaction TXN17540372344587849 at stage ACCOUNT_OFFICER
❌ Account officers cannot use the approval endpoint
✅ Transaction updated successfully
```

### 2. Network Monitoring

In browser dev tools, check:

- Which endpoints are being called
- Request/response status codes
- Error messages in response body

### 3. Database Monitoring

Monitor for:

- Concurrent transaction updates
- BVN uniqueness violations
- Transaction status changes

## Prevention Strategies

### 1. **Frontend Validation**

- Validate user role before showing action buttons
- Use correct endpoints based on user role
- Implement proper error handling

### 2. **API Documentation**

- Clear documentation of which endpoints to use for each role
- Examples of correct workflow for each user type
- Error response documentation

### 3. **User Training**

- Train account officers on correct workflow
- Provide clear instructions for handling sent-back transactions
- Document common error scenarios and solutions

## Rollback Plan

If issues arise:

1. **Immediate**: Revert error message changes while keeping logging
2. **Partial**: Keep new error messages but adjust severity
3. **Full**: Revert all changes and investigate further

## Success Metrics

- ✅ Reduction in 409 error reports
- ✅ Clearer error messages in support tickets
- ✅ Faster resolution of sent-back transactions
- ✅ Improved user satisfaction with error handling

## Conclusion

This fix addresses the specific issue where account officers were getting confusing 409 errors when trying to work with sent-back transactions. The solution provides:

1. **Clear Error Messages**: Users now know exactly what went wrong and how to fix it
2. **Correct Workflow Guidance**: Specific instructions on which endpoints to use
3. **Better Debugging**: Enhanced logging for troubleshooting
4. **Improved User Experience**: Less confusion and faster problem resolution

The fix maintains backward compatibility while significantly improving the user experience for this common workflow scenario.
