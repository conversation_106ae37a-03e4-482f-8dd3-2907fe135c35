import { LoanType } from "@prisma/client";

export { LoanType };

export interface LoanTypeConfig {
  id: LoanType;
  name: string;
  description: string;
  requiredFields: string[];
}

export const LOAN_TYPE_CONFIGS: Record<LoanType, LoanTypeConfig> = {
  [LoanType.CONSUMER_LOAN_PUBLIC]: {
    id: LoanType.CONSUMER_LOAN_PUBLIC,
    name: "Consumer Loan Public",
    description: "Loan for public sector employees",
    requiredFields: ["organizationName", "ippisNumber", "employmentDate"],
  },
  [LoanType.CONSUMER_LOAN_PRIVATE]: {
    id: LoanType.CONSUMER_LOAN_PRIVATE,
    name: "Consumer Loan Private",
    description: "Loan for private sector employees",
    requiredFields: ["organizationName", "remitaActivation", "employmentDate"],
  },
  [LoanType.SME_INDIVIDUAL]: {
    id: LoanType.SME_INDIVIDUAL,
    name: "SME Individual",
    description: "Small and Medium Enterprise loan for individuals",
    requiredFields: ["organizationName", "remitaActivation", "employmentDate"],
  },
  [LoanType.SME_CORPORATE]: {
    id: LoanType.SME_CORPORATE,
    name: "SME Corporate",
    description: "Small and Medium Enterprise loan for corporations",
    requiredFields: ["businessName", "registrationNumber", "employmentDate"],
  },
};

export interface LoanTypeSelectionData {
  loanType: LoanType;
}

export interface PersonalInfoData {
  firstName: string;
  middleName?: string;
  lastName: string;
  gender: string;
  maritalStatus: string;
  dateOfBirth: Date;
  bvn: string;
  nin: string;
  email?: string;
  phoneNumber: string;
  street: string;
  city: string;
  state: string;
  postalCode?: string;

  // Common fields
  employmentDate: Date;

  // Consumer Loan Public specific
  organizationName?: string;
  ippisNumber?: string;

  // Consumer Loan Private & SME Individual specific
  remitaActivation?: string;

  // SME Corporate specific
  businessName?: string;
  registrationNumber?: string;
}

export function getRequiredFieldsForLoanType(loanType: LoanType): string[] {
  return LOAN_TYPE_CONFIGS[loanType]?.requiredFields || [];
}

export function validateLoanTypeFields(
  loanType: LoanType,
  data: Partial<PersonalInfoData>
): string[] {
  const requiredFields = getRequiredFieldsForLoanType(loanType);
  const missingFields: string[] = [];

  for (const field of requiredFields) {
    if (!data[field as keyof PersonalInfoData]) {
      missingFields.push(field);
    }
  }

  return missingFields;
}
