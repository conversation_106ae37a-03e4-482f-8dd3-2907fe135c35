import { Request, Response, NextFunction } from "express";
import { ResponseHand<PERSON> } from "../utils/response";

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class OperationalError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean = true;

  constructor(message: string, statusCode: number = 400) {
    super(message);
    this.statusCode = statusCode;
    this.name = "OperationalError";

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, OperationalError);
    }
  }
}

export const errorHandler = (
  error: AppError,
  _req: Request,
  res: Response,
  _next: NextFunction
): void => {
  console.error("Error:", error);

  // Default error values
  let statusCode = error.statusCode || 500;
  let message = error.message || "Internal server error";

  // Handle specific error types
  if (error.name === "ValidationError") {
    statusCode = 400;
    // Keep the original validation error message
    message = error.message || "Validation error";
  } else if (error.name === "CastError") {
    statusCode = 400;
    message = "Invalid data format";
  } else if (error.name === "JsonWebTokenError") {
    statusCode = 401;
    message = "Invalid token";
  } else if (error.name === "TokenExpiredError") {
    statusCode = 401;
    message = "Token expired";
  } else if (error.name === "MulterError") {
    statusCode = 400;
    if (error.message.includes("File too large")) {
      message = "File size too large";
    } else if (error.message.includes("Unexpected field")) {
      message = "Unexpected file field";
    } else {
      message = "File upload error";
    }
  } else if (error.name === "OperationalError" || error.isOperational) {
    // For operational errors, always preserve the original message
    statusCode = error.statusCode || 400;
    message = error.message;
  }

  // Only mask generic 500 errors that are not operational
  // ALWAYS preserve OperationalError messages regardless of environment
  if (
    process.env.NODE_ENV === "production" &&
    statusCode === 500 &&
    !error.isOperational &&
    error.name !== "OperationalError"
  ) {
    message = "Something went wrong";
  }

  // Ensure OperationalError messages are NEVER masked
  if (error.name === "OperationalError" || error.isOperational) {
    // Force preserve the original message for operational errors
    message = error.message;
    statusCode = error.statusCode || statusCode;
  }

  console.log("Sending error response:", {
    statusCode,
    message,
    isOperational: error.isOperational,
    errorName: error.name,
  });

  ResponseHandler.error(
    res,
    message,
    process.env.NODE_ENV === "development" ? error.stack : undefined,
    statusCode
  );
};

export const notFoundHandler = (
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  ResponseHandler.notFound(res, `Route ${req.originalUrl} not found`);
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
