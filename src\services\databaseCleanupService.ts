import * as cron from "node-cron";
import prisma from "../db/db";

interface CleanupResult {
  success: boolean;
  recordsDeleted: number;
  executionTime: number;
  error?: string;
}

interface CleanupStats {
  otpCleanup: CleanupResult;
  notificationCleanup: CleanupResult;
  passwordResetCleanup: CleanupResult;
  totalRecordsDeleted: number;
  totalExecutionTime: number;
}

export class DatabaseCleanupService {
  private static isInitialized = false;
  private static readonly OTP_RETENTION_HOURS = parseInt(
    process.env.OTP_RETENTION_HOURS || "1"
  );
  private static readonly NOTIFICATION_RETENTION_DAYS = parseInt(
    process.env.NOTIFICATION_RETENTION_DAYS || "10"
  );
  private static readonly PASSWORD_RESET_RETENTION_HOURS = parseInt(
    process.env.PASSWORD_RESET_RETENTION_HOURS || "1"
  );
  private static readonly CLEANUP_BATCH_SIZE = parseInt(
    process.env.CLEANUP_BATCH_SIZE || "1000"
  );

  /**
   * Clean up expired and used OTP codes
   * - Delete all used OTP codes
   * - Delete all unused OTP codes older than 1 hour (configurable)
   */
  static async cleanupOtpCodes(): Promise<CleanupResult> {
    const startTime = Date.now();
    console.log("🧹 Starting OTP codes cleanup...");

    try {
      const cutoffTime = new Date(
        Date.now() - this.OTP_RETENTION_HOURS * 60 * 60 * 1000
      );

      console.log(`📅 OTP cleanup cutoff time: ${cutoffTime.toISOString()}`);
      console.log(`🔧 Batch size: ${this.CLEANUP_BATCH_SIZE}`);

      let totalDeleted = 0;
      let batchCount = 0;

      // Process in batches to avoid database locks
      while (true) {
        const otpCodesToDelete = await prisma.otpCode.findMany({
          where: {
            OR: [
              // Delete all used OTP codes
              { isUsed: true },
              // Delete unused OTP codes older than retention period
              {
                isUsed: false,
                createdAt: {
                  lt: cutoffTime,
                },
              },
            ],
          },
          select: { id: true },
          take: this.CLEANUP_BATCH_SIZE,
        });

        if (otpCodesToDelete.length === 0) {
          break;
        }

        const idsToDelete = otpCodesToDelete.map((otp) => otp.id);

        const deleteResult = await prisma.otpCode.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });

        totalDeleted += deleteResult.count;
        batchCount++;

        console.log(
          `🗑️  Batch ${batchCount}: Deleted ${deleteResult.count} OTP codes`
        );

        // Small delay between batches to reduce database load
        if (otpCodesToDelete.length === this.CLEANUP_BATCH_SIZE) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      const executionTime = Date.now() - startTime;

      console.log(`✅ OTP cleanup completed:`, {
        recordsDeleted: totalDeleted,
        batchesProcessed: batchCount,
        executionTimeMs: executionTime,
        retentionHours: this.OTP_RETENTION_HOURS,
      });

      return {
        success: true,
        recordsDeleted: totalDeleted,
        executionTime,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      console.error("❌ OTP cleanup failed:", {
        error: errorMessage,
        executionTimeMs: executionTime,
      });

      return {
        success: false,
        recordsDeleted: 0,
        executionTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Clean up notifications with enhanced dual-criteria logic
   * - Delete ALL notifications where isRead = true (regardless of creation date)
   * - Delete ALL notifications where isRead = false AND createdAt is older than 10 days
   */
  static async cleanupNotifications(): Promise<CleanupResult> {
    const startTime = Date.now();
    console.log("🧹 Starting enhanced notifications cleanup...");

    try {
      const cutoffTime = new Date(
        Date.now() - this.NOTIFICATION_RETENTION_DAYS * 24 * 60 * 60 * 1000
      );

      console.log(`📅 Notification cleanup configuration:`, {
        retentionDays: this.NOTIFICATION_RETENTION_DAYS,
        cutoffTime: cutoffTime.toISOString(),
        batchSize: this.CLEANUP_BATCH_SIZE,
      });
      console.log(
        `🔧 Cleanup criteria: ALL read notifications + unread notifications older than ${this.NOTIFICATION_RETENTION_DAYS} days`
      );

      let totalDeleted = 0;
      let batchCount = 0;

      // Process in batches to avoid database locks
      while (true) {
        const notificationsToDelete = await prisma.notification.findMany({
          where: {
            OR: [
              // Criterion 1: Delete ALL read notifications (regardless of age)
              { isRead: true },
              // Criterion 2: Delete unread notifications older than retention period
              {
                isRead: false,
                createdAt: {
                  lt: cutoffTime,
                },
              },
            ],
          },
          select: { id: true, isRead: true, createdAt: true },
          take: this.CLEANUP_BATCH_SIZE,
        });

        if (notificationsToDelete.length === 0) {
          break;
        }

        const idsToDelete = notificationsToDelete.map(
          (notification) => notification.id
        );

        // Log breakdown of what's being deleted
        const readCount = notificationsToDelete.filter((n) => n.isRead).length;
        const unreadOldCount = notificationsToDelete.filter(
          (n) => !n.isRead
        ).length;

        const deleteResult = await prisma.notification.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });

        totalDeleted += deleteResult.count;
        batchCount++;

        console.log(
          `🗑️  Batch ${batchCount}: Deleted ${deleteResult.count} notifications (${readCount} read, ${unreadOldCount} unread old)`
        );

        // Small delay between batches to reduce database load
        if (notificationsToDelete.length === this.CLEANUP_BATCH_SIZE) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      const executionTime = Date.now() - startTime;

      console.log(`✅ Enhanced notification cleanup completed:`, {
        recordsDeleted: totalDeleted,
        batchesProcessed: batchCount,
        executionTimeMs: executionTime,
        retentionDays: this.NOTIFICATION_RETENTION_DAYS,
        criteria:
          "All read notifications + unread notifications older than retention period",
      });

      return {
        success: true,
        recordsDeleted: totalDeleted,
        executionTime,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      console.error("❌ Enhanced notification cleanup failed:", {
        error: errorMessage,
        executionTimeMs: executionTime,
      });

      return {
        success: false,
        recordsDeleted: 0,
        executionTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Clean up expired and used password reset tokens
   * - Delete all used password reset tokens
   * - Delete all unused password reset tokens older than 1 hour (configurable)
   */
  static async cleanupPasswordResetTokens(): Promise<CleanupResult> {
    const startTime = Date.now();
    console.log("🧹 Starting password reset tokens cleanup...");

    try {
      const cutoffTime = new Date(
        Date.now() - this.PASSWORD_RESET_RETENTION_HOURS * 60 * 60 * 1000
      );

      console.log(
        `📅 Password reset token cleanup cutoff time: ${cutoffTime.toISOString()}`
      );
      console.log(`🔧 Batch size: ${this.CLEANUP_BATCH_SIZE}`);

      let totalDeleted = 0;
      let batchCount = 0;

      // Process in batches to avoid database locks
      while (true) {
        const tokensToDelete = await prisma.passwordResetToken.findMany({
          where: {
            OR: [
              // Delete all used password reset tokens
              { isUsed: true },
              // Delete unused password reset tokens older than retention period
              {
                isUsed: false,
                createdAt: {
                  lt: cutoffTime,
                },
              },
            ],
          },
          select: { id: true },
          take: this.CLEANUP_BATCH_SIZE,
        });

        if (tokensToDelete.length === 0) {
          break;
        }

        const idsToDelete = tokensToDelete.map((token) => token.id);

        const deleteResult = await prisma.passwordResetToken.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });

        totalDeleted += deleteResult.count;
        batchCount++;

        console.log(
          `🗑️  Batch ${batchCount}: Deleted ${deleteResult.count} password reset tokens`
        );

        // Small delay between batches to reduce database load
        if (tokensToDelete.length === this.CLEANUP_BATCH_SIZE) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      const executionTime = Date.now() - startTime;

      console.log(`✅ Password reset token cleanup completed:`, {
        recordsDeleted: totalDeleted,
        batchesProcessed: batchCount,
        executionTimeMs: executionTime,
        retentionHours: this.PASSWORD_RESET_RETENTION_HOURS,
      });

      return {
        success: true,
        recordsDeleted: totalDeleted,
        executionTime,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      console.error("❌ Password reset token cleanup failed:", {
        error: errorMessage,
        executionTimeMs: executionTime,
      });

      return {
        success: false,
        recordsDeleted: 0,
        executionTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Run all cleanup operations
   */
  static async runFullCleanup(): Promise<CleanupStats> {
    const startTime = Date.now();
    console.log("🚀 Starting full database cleanup...");

    const otpCleanup = await this.cleanupOtpCodes();
    const notificationCleanup = await this.cleanupNotifications();
    const passwordResetCleanup = await this.cleanupPasswordResetTokens();

    const totalExecutionTime = Date.now() - startTime;
    const totalRecordsDeleted =
      otpCleanup.recordsDeleted +
      notificationCleanup.recordsDeleted +
      passwordResetCleanup.recordsDeleted;

    const stats: CleanupStats = {
      otpCleanup,
      notificationCleanup,
      passwordResetCleanup,
      totalRecordsDeleted,
      totalExecutionTime,
    };

    console.log("🎉 Full database cleanup completed:", {
      totalRecordsDeleted,
      totalExecutionTimeMs: totalExecutionTime,
      otpRecordsDeleted: otpCleanup.recordsDeleted,
      notificationRecordsDeleted: notificationCleanup.recordsDeleted,
      passwordResetRecordsDeleted: passwordResetCleanup.recordsDeleted,
      otpSuccess: otpCleanup.success,
      notificationSuccess: notificationCleanup.success,
      passwordResetSuccess: passwordResetCleanup.success,
    });

    // Log any errors
    if (
      !otpCleanup.success ||
      !notificationCleanup.success ||
      !passwordResetCleanup.success
    ) {
      console.error("⚠️  Some cleanup operations failed:", {
        otpError: otpCleanup.error,
        notificationError: notificationCleanup.error,
        passwordResetError: passwordResetCleanup.error,
      });
    }

    return stats;
  }

  /**
   * Get cleanup statistics without performing cleanup
   */
  static async getCleanupStats(): Promise<{
    otpCodesToCleanup: number;
    notificationsToCleanup: number;
    passwordResetTokensToCleanup: number;
    oldestOtpCode: Date | null;
    oldestNotification: Date | null;
    oldestPasswordResetToken: Date | null;
  }> {
    const otpCutoffTime = new Date(
      Date.now() - this.OTP_RETENTION_HOURS * 60 * 60 * 1000
    );
    const notificationCutoffTime = new Date(
      Date.now() - this.NOTIFICATION_RETENTION_DAYS * 24 * 60 * 60 * 1000
    );
    const passwordResetCutoffTime = new Date(
      Date.now() - this.PASSWORD_RESET_RETENTION_HOURS * 60 * 60 * 1000
    );

    const [
      otpCodesToCleanup,
      notificationsToCleanup,
      passwordResetTokensToCleanup,
      oldestOtp,
      oldestNotification,
      oldestPasswordResetToken,
    ] = await Promise.all([
      prisma.otpCode.count({
        where: {
          OR: [
            { isUsed: true },
            {
              isUsed: false,
              createdAt: { lt: otpCutoffTime },
            },
          ],
        },
      }),
      prisma.notification.count({
        where: {
          OR: [
            // Count ALL read notifications (regardless of age)
            { isRead: true },
            // Count unread notifications older than retention period
            {
              isRead: false,
              createdAt: { lt: notificationCutoffTime },
            },
          ],
        },
      }),
      prisma.passwordResetToken.count({
        where: {
          OR: [
            { isUsed: true },
            {
              isUsed: false,
              createdAt: { lt: passwordResetCutoffTime },
            },
          ],
        },
      }),
      prisma.otpCode.findFirst({
        orderBy: { createdAt: "asc" },
        select: { createdAt: true },
      }),
      prisma.notification.findFirst({
        orderBy: { createdAt: "asc" },
        select: { createdAt: true },
      }),
      prisma.passwordResetToken.findFirst({
        orderBy: { createdAt: "asc" },
        select: { createdAt: true },
      }),
    ]);

    return {
      otpCodesToCleanup,
      notificationsToCleanup,
      passwordResetTokensToCleanup,
      oldestOtpCode: oldestOtp?.createdAt || null,
      oldestNotification: oldestNotification?.createdAt || null,
      oldestPasswordResetToken: oldestPasswordResetToken?.createdAt || null,
    };
  }

  /**
   * Initialize scheduled cleanup jobs
   */
  static initializeScheduledJobs(): void {
    if (this.isInitialized) {
      console.log("⚠️  Database cleanup jobs already initialized");
      return;
    }

    console.log("📅 Initializing database cleanup jobs...");

    // OTP Cleanup - Run every hour
    const otpCleanupSchedule = process.env.OTP_CLEANUP_SCHEDULE || "0 * * * *";
    cron.schedule(
      otpCleanupSchedule,
      async () => {
        try {
          console.log("⏰ Running scheduled OTP cleanup...");
          await this.cleanupOtpCodes();
        } catch (error) {
          console.error("❌ Scheduled OTP cleanup failed:", error);
          // TODO: Add alerting/monitoring here
        }
      },
      {
        timezone: process.env.TZ || "UTC",
      }
    );

    // Notification Cleanup - Run daily at 2 AM
    const notificationCleanupSchedule =
      process.env.NOTIFICATION_CLEANUP_SCHEDULE || "0 2 * * *";
    cron.schedule(
      notificationCleanupSchedule,
      async () => {
        try {
          console.log("⏰ Running scheduled notification cleanup...");
          await this.cleanupNotifications();
        } catch (error) {
          console.error("❌ Scheduled notification cleanup failed:", error);
          // TODO: Add alerting/monitoring here
        }
      },
      {
        timezone: process.env.TZ || "UTC",
      }
    );

    // Password Reset Token Cleanup - Run every hour (same as OTP)
    const passwordResetCleanupSchedule =
      process.env.PASSWORD_RESET_CLEANUP_SCHEDULE || "0 * * * *";
    cron.schedule(
      passwordResetCleanupSchedule,
      async () => {
        try {
          console.log("⏰ Running scheduled password reset token cleanup...");
          await this.cleanupPasswordResetTokens();
        } catch (error) {
          console.error(
            "❌ Scheduled password reset token cleanup failed:",
            error
          );
          // TODO: Add alerting/monitoring here
        }
      },
      {
        timezone: process.env.TZ || "UTC",
      }
    );

    // Full Cleanup - Run weekly on Sunday at 3 AM
    const fullCleanupSchedule =
      process.env.FULL_CLEANUP_SCHEDULE || "0 3 * * 0";
    cron.schedule(
      fullCleanupSchedule,
      async () => {
        try {
          console.log("⏰ Running scheduled full database cleanup...");
          const stats = await this.runFullCleanup();

          // Log summary for monitoring
          console.log("📊 Weekly cleanup summary:", {
            totalRecordsDeleted: stats.totalRecordsDeleted,
            executionTimeMs: stats.totalExecutionTime,
            success:
              stats.otpCleanup.success && stats.notificationCleanup.success,
          });
        } catch (error) {
          console.error("❌ Scheduled full cleanup failed:", error);
          // TODO: Add alerting/monitoring here
        }
      },
      {
        timezone: process.env.TZ || "UTC",
      }
    );

    this.isInitialized = true;

    console.log("✅ Database cleanup jobs initialized:", {
      otpCleanupSchedule,
      notificationCleanupSchedule,
      passwordResetCleanupSchedule,
      fullCleanupSchedule,
      timezone: process.env.TZ || "UTC",
      otpRetentionHours: this.OTP_RETENTION_HOURS,
      notificationRetentionDays: this.NOTIFICATION_RETENTION_DAYS,
      passwordResetRetentionHours: this.PASSWORD_RESET_RETENTION_HOURS,
      batchSize: this.CLEANUP_BATCH_SIZE,
    });
  }

  /**
   * Stop all scheduled cleanup jobs
   */
  static stopScheduledJobs(): void {
    if (!this.isInitialized) {
      console.log("⚠️  Database cleanup jobs not initialized");
      return;
    }

    cron.getTasks().forEach((task) => {
      task.stop();
    });

    this.isInitialized = false;
    console.log("🛑 Database cleanup jobs stopped");
  }

  /**
   * Get status of cleanup jobs
   */
  static getJobStatus(): {
    isInitialized: boolean;
    activeJobs: number;
    configuration: {
      otpRetentionHours: number;
      notificationRetentionDays: number;
      passwordResetRetentionHours: number;
      batchSize: number;
      timezone: string;
    };
  } {
    const activeTasks = cron.getTasks();

    return {
      isInitialized: this.isInitialized,
      activeJobs: activeTasks.size,
      configuration: {
        otpRetentionHours: this.OTP_RETENTION_HOURS,
        notificationRetentionDays: this.NOTIFICATION_RETENTION_DAYS,
        passwordResetRetentionHours: this.PASSWORD_RESET_RETENTION_HOURS,
        batchSize: this.CLEANUP_BATCH_SIZE,
        timezone: process.env.TZ || "UTC",
      },
    };
  }

  /**
   * Manual cleanup trigger (for testing or emergency cleanup)
   */
  static async manualCleanup(
    options: {
      includeOtp?: boolean;
      includeNotifications?: boolean;
      includePasswordReset?: boolean;
    } = {}
  ): Promise<CleanupStats> {
    const {
      includeOtp = true,
      includeNotifications = true,
      includePasswordReset = true,
    } = options;

    console.log("🔧 Running manual database cleanup:", options);

    const startTime = Date.now();
    let otpCleanup: CleanupResult = {
      success: true,
      recordsDeleted: 0,
      executionTime: 0,
    };
    let notificationCleanup: CleanupResult = {
      success: true,
      recordsDeleted: 0,
      executionTime: 0,
    };
    let passwordResetCleanup: CleanupResult = {
      success: true,
      recordsDeleted: 0,
      executionTime: 0,
    };

    if (includeOtp) {
      otpCleanup = await this.cleanupOtpCodes();
    }

    if (includeNotifications) {
      notificationCleanup = await this.cleanupNotifications();
    }

    if (includePasswordReset) {
      passwordResetCleanup = await this.cleanupPasswordResetTokens();
    }

    const totalExecutionTime = Date.now() - startTime;
    const totalRecordsDeleted =
      otpCleanup.recordsDeleted +
      notificationCleanup.recordsDeleted +
      passwordResetCleanup.recordsDeleted;

    return {
      otpCleanup,
      notificationCleanup,
      passwordResetCleanup,
      totalRecordsDeleted,
      totalExecutionTime,
    };
  }
}
