import { Router } from "express";
import { UserController } from "../controllers/userController";
import { validateBody } from "../middleware/validation";
import { authenticate, requireSuperAdmin } from "../middleware/auth";
import { updateUserSchema } from "../utils/validation";

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * tags:
 *   name: User Management
 *   description: User management operations (CRUD operations for users)
 */

/**
 * @swagger
 * /api/v1/users/{userId}:
 *   delete:
 *     summary: Delete a user (Super Admin only)
 *     description: |
 *       Permanently delete a user from the system. This operation is irreversible.
 *       
 *       **Security Restrictions:**
 *       - Only SUPER_ADMIN role can delete users
 *       - Cannot delete users with active transactions
 *       - Cannot delete users with pending approvals
 *       - Cannot delete the last remaining Super Admin
 *       
 *       **Safety Checks:**
 *       - Validates user exists before deletion
 *       - Checks for active transactions created by the user
 *       - Checks for pending approvals assigned to the user
 *       - Prevents deletion of critical system users
 *       
 *       **Audit Logging:**
 *       - Logs all deletion attempts for security audit
 *       - Records the admin who performed the deletion
 *       - Includes timestamp and reason for deletion
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to delete
 *         example: "6877a5f56bc70e597c5ed79f"
 *     responses:
 *       200:
 *         description: User deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *             example:
 *               success: true
 *               message: "User deleted successfully"
 *               data: {}
 *       400:
 *         description: Cannot delete user due to business rules
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               active_transactions:
 *                 summary: User has active transactions
 *                 value:
 *                   success: false
 *                   message: "Cannot delete user with active transactions"
 *               pending_approvals:
 *                 summary: User has pending approvals
 *                 value:
 *                   success: false
 *                   message: "Cannot delete user with pending approvals"
 *               last_super_admin:
 *                 summary: Cannot delete last Super Admin
 *                 value:
 *                   success: false
 *                   message: "Cannot delete the last Super Admin user"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Super Admin role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "User not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete(
  "/:userId",
  requireSuperAdmin,
  UserController.deleteUser
);

/**
 * @swagger
 * /api/v1/users/{userId}:
 *   patch:
 *     summary: Update user details
 *     description: |
 *       Update user profile information with role-based access control.
 *       
 *       **Access Control:**
 *       - SUPER_ADMIN: Can update all fields for any user
 *       - Regular users: Can only update their own basic profile (firstName, lastName)
 *       - Role and isActive status can only be changed by SUPER_ADMIN
 *       
 *       **Validation Rules:**
 *       - Email must be unique across all users
 *       - Role must be a valid UserRole enum value
 *       - monthlyTarget must be positive number (if provided)
 *       - firstName and lastName are required if provided
 *       
 *       **Security Features:**
 *       - Prevents email conflicts with existing users
 *       - Validates role changes against enum values
 *       - Logs all profile updates for audit trail
 *       - Prevents privilege escalation attempts
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to update
 *         example: "6877a5f56bc70e597c5ed79f"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateUserRequest'
 *           examples:
 *             basic_profile_update:
 *               summary: Basic profile update (any user)
 *               value:
 *                 firstName: "John"
 *                 lastName: "Doe"
 *             admin_full_update:
 *               summary: Full profile update (Super Admin only)
 *               value:
 *                 firstName: "Jane"
 *                 lastName: "Smith"
 *                 email: "<EMAIL>"
 *                 role: "HEAD_CONSUMER_LENDING"
 *                 monthlyTarget: 75000
 *                 isActive: true
 *             role_change:
 *               summary: Role change (Super Admin only)
 *               value:
 *                 role: "SUPERVISOR"
 *             deactivate_user:
 *               summary: Deactivate user (Super Admin only)
 *               value:
 *                 isActive: false
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error or business rule violation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               email_exists:
 *                 summary: Email already exists
 *                 value:
 *                   success: false
 *                   message: "Email already exists for another user"
 *               invalid_role:
 *                 summary: Invalid role value
 *                 value:
 *                   success: false
 *                   message: "Invalid role specified"
 *               validation_error:
 *                 summary: Field validation error
 *                 value:
 *                   success: false
 *                   message: "firstName is required"
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               role_update_denied:
 *                 summary: Cannot update role without Super Admin privileges
 *                 value:
 *                   success: false
 *                   message: "Only Super Admin can update user roles"
 *               other_user_denied:
 *                 summary: Cannot update other user's profile
 *                 value:
 *                   success: false
 *                   message: "You can only update your own profile"
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "User not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/:userId",
  validateBody(updateUserSchema),
  UserController.updateUser
);

export default router;
