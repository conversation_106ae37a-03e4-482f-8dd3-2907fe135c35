import * as jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { UserRole } from "@prisma/client";

export interface JwtPayload {
  id: string;
  email: string;
  role: UserRole;
  firstName: string;
  lastName: string;
}

export class AuthUtils {
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  static async comparePassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  static generateAccessToken(payload: JwtPayload): string {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error("JWT_SECRET is not defined");
    }
    return jwt.sign(payload as object, secret, {
      expiresIn: process.env.JWT_EXPIRES_IN || "1h",
    } as jwt.SignOptions);
  }

  static generateRefreshToken(payload: JwtPayload): string {
    const secret = process.env.REFRESH_TOKEN_SECRET;
    if (!secret) {
      throw new Error("REFRESH_TOKEN_SECRET is not defined");
    }
    return jwt.sign(payload as object, secret, {
      expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || "7d",
    } as jwt.SignOptions);
  }

  static verifyAccessToken(token: string): JwtPayload {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error("JWT_SECRET is not defined");
    }
    return jwt.verify(token, secret) as JwtPayload;
  }

  static verifyRefreshToken(token: string): JwtPayload {
    const secret = process.env.REFRESH_TOKEN_SECRET;
    if (!secret) {
      throw new Error("REFRESH_TOKEN_SECRET is not defined");
    }
    return jwt.verify(token, secret) as JwtPayload;
  }

  static generateOTP(): string {
    return crypto.randomInt(100000, 999999).toString();
  }

  static generateTransactionId(): string {
    const prefix = "TXN";
    const timestamp = Date.now().toString();
    const random = crypto.randomInt(1000, 9999).toString();
    return `${prefix}${timestamp}${random}`;
  }

  static getOTPExpiryTime(): Date {
    const expiryMinutes =
      parseInt(process.env.OTP_EXPIRES_IN || "300000") / 60000; // Convert ms to minutes
    return new Date(Date.now() + expiryMinutes * 60 * 1000);
  }

  static isOTPExpired(expiryTime: Date): boolean {
    return new Date() > expiryTime;
  }
}
