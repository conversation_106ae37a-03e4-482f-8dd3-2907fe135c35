openapi: 3.0.0
info:
  title: AMS Loan Management System API
  description: |
    Complete API documentation for AMS Loan Management System with authentication, 
    transaction management, loan type selection, approval workflows, and document management.

    ## Features
    - **Loan Type Selection System**: 4 loan types with dynamic form fields
    - **Multi-step Transaction Creation**: 6 steps including loan type selection
    - **Transaction Preview & PDF Generation**: Professional document generation
    - **Role-based Approval Workflow**: Multi-level approval system
    - **Document Management**: Upload and manage transaction documents
    - **Dashboard & Analytics**: Comprehensive reporting and metrics

    ## Authentication
    All protected endpoints require a Bearer token in the Authorization header:
    ```
    Authorization: Bearer <your_jwt_token>
    ```

    ## Loan Types
    - **Consumer Loan Public**: organizationName, ippisNumber, employmentDate
    - **Consumer Loan Private**: organizationName, remitaActivation, employmentDate  
    - **SME Individual**: organizationName, remitaActivation, employmentDate
    - **SME Corporate**: businessName, registrationNumber, employmentDate
  version: 3.0.0
  contact:
    name: AMS Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000/api
    description: Development server
  - url: https://api.ams-loan.com/api
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login endpoint

  schemas:
    # Common Schemas
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
      required:
        - success
        - message

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
        error:
          type: object
      required:
        - success
        - message

    PaginationParams:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          default: 1
        limit:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
        sortBy:
          type: string
          default: createdAt
        sortOrder:
          type: string
          enum: [asc, desc]
          default: desc

    # Enums
    UserRole:
      type: string
      enum:
        - ACCOUNT_OFFICER
        - SUPERVISOR
        - HEAD_CONSUMER_LENDING
        - HEAD_RISK_MANAGEMENT
        - MANAGING_DIRECTOR
        - ACCOUNTANT
        - SUPER_ADMIN

    LoanType:
      type: string
      enum:
        - CONSUMER_LOAN_PUBLIC
        - CONSUMER_LOAN_PRIVATE
        - SME_INDIVIDUAL
        - SME_CORPORATE

    TransactionStatus:
      type: string
      enum:
        - DRAFT
        - SUBMITTED
        - IN_PROGRESS
        - APPROVED
        - REJECTED
        - SENT_BACK
        - DISBURSED
        - COMPLETED
        - LOAN_REPAID

    ApprovalStage:
      type: string
      enum:
        - ACCOUNT_OFFICER
        - SUPERVISOR
        - HEAD_CONSUMER_LENDING
        - HEAD_RISK_MANAGEMENT
        - MANAGING_DIRECTOR
        - ACCOUNTANT

    Gender:
      type: string
      enum: [MALE, FEMALE]

    MaritalStatus:
      type: string
      enum: [SINGLE, MARRIED, DIVORCED, WIDOWED]

    RepaymentMode:
      type: string
      enum: [MONTHLY, QUARTERLY, SEMI_ANNUALLY, ANNUALLY]

    FileType:
      type: string
      enum: [PDF, IMAGE, DOCUMENT]

    # Auth Schemas
    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
      required:
        - email
        - password

    OTPVerificationRequest:
      type: object
      properties:
        email:
          type: string
          format: email
        otp:
          type: string
          pattern: "^[0-9]{6}$"
      required:
        - email
        - otp

    RefreshTokenRequest:
      type: object
      properties:
        refreshToken:
          type: string
      required:
        - refreshToken

    AuthResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            accessToken:
              type: string
            refreshToken:
              type: string
            user:
              $ref: "#/components/schemas/User"

    # User Schemas
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        role:
          $ref: "#/components/schemas/UserRole"
        isActive:
          type: boolean
        monthlyTarget:
          type: number
        lastLogin:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
        firstName:
          type: string
        lastName:
          type: string
        role:
          $ref: "#/components/schemas/UserRole"
        monthlyTarget:
          type: number
          minimum: 0
      required:
        - email
        - password
        - firstName
        - lastName
        - role

    # Loan Type Schemas
    LoanTypeConfig:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/LoanType"
        name:
          type: string
        description:
          type: string
        requiredFields:
          type: array
          items:
            type: string

    LoanTypeSelectionRequest:
      type: object
      properties:
        loanType:
          $ref: "#/components/schemas/LoanType"
      required:
        - loanType

    # Transaction Schemas
    Transaction:
      type: object
      properties:
        id:
          type: string
        transactionId:
          type: string
        status:
          $ref: "#/components/schemas/TransactionStatus"
        currentStage:
          $ref: "#/components/schemas/ApprovalStage"
        loanType:
          $ref: "#/components/schemas/LoanType"
        # Personal Information
        firstName:
          type: string
        middleName:
          type: string
        lastName:
          type: string
        gender:
          $ref: "#/components/schemas/Gender"
        maritalStatus:
          $ref: "#/components/schemas/MaritalStatus"
        dateOfBirth:
          type: string
          format: date
        bvn:
          type: string
          minLength: 11
          maxLength: 11
          description: Bank Verification Number (11 digits)
        nin:
          type: string
          minLength: 11
          description: National Identification Number
        email:
          type: string
          format: email
        phoneNumber:
          type: string
        street:
          type: string
        city:
          type: string
        state:
          type: string
        postalCode:
          type: string
        employmentDate:
          type: string
          format: date
        # Loan type specific fields
        organizationName:
          type: string
        ippisNumber:
          type: string
        remitaActivation:
          type: string
        businessName:
          type: string
        registrationNumber:
          type: string
        # Next of Kin
        nokFirstName:
          type: string
        nokMiddleName:
          type: string
        nokLastName:
          type: string
        nokRelationship:
          type: string
        nokPhoneNumber:
          type: string
        nokStreet:
          type: string
        nokCity:
          type: string
        nokState:
          type: string
        nokPostalCode:
          type: string
        # Loan Information
        requestedAmount:
          type: number
        loanTenor:
          type: integer
        repaymentMode:
          $ref: "#/components/schemas/RepaymentMode"
        grossPay:
          type: number
        netPay:
          type: number
        purposeOfLoan:
          type: string
        # Disbursement
        accountName:
          type: string
        accountNumber:
          type: string
        bankName:
          type: string
        # Metadata
        createdById:
          type: string
        submittedAt:
          type: string
          format: date-time
        completedAt:
          type: string
          format: date-time
        rejectedAt:
          type: string
          format: date-time
        rejectionReason:
          type: string
        sentBackReason:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    TransactionPreview:
      allOf:
        - $ref: "#/components/schemas/Transaction"
        - type: object
          properties:
            loanTypeConfig:
              $ref: "#/components/schemas/LoanTypeConfig"
            isComplete:
              type: boolean
            canEdit:
              type: boolean
            createdBy:
              type: object
              properties:
                id:
                  type: string
                firstName:
                  type: string
                lastName:
                  type: string
                email:
                  type: string
            documents:
              type: array
              items:
                $ref: "#/components/schemas/Document"
            approvals:
              type: array
              items:
                $ref: "#/components/schemas/TransactionApproval"

    PersonalInfoRequest:
      type: object
      properties:
        firstName:
          type: string
        middleName:
          type: string
        lastName:
          type: string
        gender:
          $ref: "#/components/schemas/Gender"
        maritalStatus:
          $ref: "#/components/schemas/MaritalStatus"
        dateOfBirth:
          type: string
          format: date
        email:
          type: string
          format: email
        phoneNumber:
          type: string
        street:
          type: string
        city:
          type: string
        state:
          type: string
        postalCode:
          type: string
        employmentDate:
          type: string
          format: date
        # Consumer Loan Public
        organizationName:
          type: string
        ippisNumber:
          type: string
        # Consumer Loan Private & SME Individual
        remitaActivation:
          type: string
        # SME Corporate
        businessName:
          type: string
        registrationNumber:
          type: string
      required:
        - firstName
        - lastName
        - gender
        - maritalStatus
        - dateOfBirth
        - bvn
        - nin
        - phoneNumber
        - street
        - city
        - state
        - employmentDate

    NextOfKinRequest:
      type: object
      properties:
        nokFirstName:
          type: string
        nokMiddleName:
          type: string
        nokLastName:
          type: string
        nokRelationship:
          type: string
        nokPhoneNumber:
          type: string
        nokStreet:
          type: string
        nokCity:
          type: string
        nokState:
          type: string
        nokPostalCode:
          type: string
      required:
        - nokFirstName
        - nokMiddleName
        - nokLastName
        - nokRelationship
        - nokPhoneNumber
        - nokStreet
        - nokCity
        - nokState

    LoanInfoRequest:
      type: object
      properties:
        requestedAmount:
          type: number
          minimum: 1
        loanTenor:
          type: integer
          minimum: 1
        repaymentMode:
          $ref: "#/components/schemas/RepaymentMode"
        grossPay:
          type: number
          minimum: 0
        netPay:
          type: number
          minimum: 0
        purposeOfLoan:
          type: string
      required:
        - requestedAmount
        - loanTenor
        - repaymentMode
        - grossPay
        - netPay
        - purposeOfLoan

    DisbursementRequest:
      type: object
      properties:
        accountName:
          type: string
        accountNumber:
          type: string
          minLength: 10
        bankName:
          type: string
      required:
        - accountName
        - accountNumber
        - bankName

    # Document Schemas
    Document:
      type: object
      properties:
        id:
          type: string
        transactionId:
          type: string
        fileName:
          type: string
        originalName:
          type: string
        filePath:
          type: string
        cloudinaryId:
          type: string
        fileSize:
          type: integer
        fileType:
          $ref: "#/components/schemas/FileType"
        mimeType:
          type: string
        uploadedAt:
          type: string
          format: date-time

    DocumentSignedUrlResponse:
      type: object
      properties:
        signedUrl:
          type: string
          format: uri
          description: Time-limited signed URL for document access
          example: "https://r2.example.com/documents/file.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=..."
        expiresAt:
          type: string
          format: date-time
          description: ISO timestamp when the URL expires
          example: "2024-01-15T10:30:00.000Z"
        documentId:
          type: string
          description: Document ID
          example: "507f1f77bcf86cd799439011"
        fileName:
          type: string
          description: Original filename
          example: "passport_copy.pdf"
      required:
        - signedUrl
        - expiresAt
        - documentId
        - fileName

    # Approval Schemas
    TransactionApproval:
      type: object
      properties:
        id:
          type: string
        transactionId:
          type: string
        approverId:
          type: string
        stage:
          $ref: "#/components/schemas/ApprovalStage"
        action:
          type: string
          enum: [APPROVED, REJECTED, SENT_BACK]
        comments:
          type: string
        createdAt:
          type: string
          format: date-time
        approver:
          type: object
          properties:
            firstName:
              type: string
            lastName:
              type: string
            role:
              $ref: "#/components/schemas/UserRole"

    ApprovalActionRequest:
      type: object
      properties:
        action:
          type: string
          enum: [APPROVED, REJECTED, SENT_BACK]
        comments:
          type: string
      required:
        - action

    # Notification Schemas
    Notification:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        transactionId:
          type: string
        type:
          type: string
        title:
          type: string
        message:
          type: string
        isRead:
          type: boolean
        createdAt:
          type: string
          format: date-time

    # Dashboard Schemas
    DashboardStats:
      type: object
      properties:
        totalTransactions:
          type: integer
        pendingApprovals:
          type: integer
        approvedTransactions:
          type: integer
        rejectedTransactions:
          type: integer
        totalAmount:
          type: number
        monthlyTarget:
          type: number
        achievementPercentage:
          type: number

    TransactionTrends:
      type: object
      properties:
        period:
          type: string
        totalTransactions:
          type: integer
        totalAmount:
          type: number
        approvalRate:
          type: number

    # Filter Schemas
    TransactionFilters:
      type: object
      properties:
        status:
          $ref: "#/components/schemas/TransactionStatus"
        stage:
          $ref: "#/components/schemas/ApprovalStage"
        createdById:
          type: string
        dateFrom:
          type: string
          format: date
        dateTo:
          type: string
          format: date
        search:
          type: string

    # Loan Eligibility Schemas
    LoanEligibilityRequest:
      type: object
      properties:
        bvn:
          type: string
          pattern: "^[0-9]{11}$"
          description: Bank Verification Number (exactly 11 digits)
          example: "***********"
      required:
        - bvn

    LoanEligibilityResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "You are not eligible for a new loan at this time"
        data:
          $ref: "#/components/schemas/LoanEligibilityResult"

    LoanEligibilityResult:
      type: object
      properties:
        isEligible:
          type: boolean
          description: Whether the user is eligible for a new loan
          example: false
        reason:
          type: string
          description: Reason for ineligibility (only present when isEligible is false)
          example: "You have an active loan (TXN-2024-001) that expires on 12/31/2024. You can apply for a new loan after this date."
        activeLoan:
          $ref: "#/components/schemas/ActiveLoanInfo"
        eligibilityDate:
          type: string
          format: date-time
          description: Date when user becomes eligible for new loan (only present when isEligible is false)
          example: "2024-12-31T00:00:00.000Z"

    ActiveLoanInfo:
      type: object
      properties:
        transactionId:
          type: string
          description: Transaction ID of the active loan
          example: "TXN-2024-001"
        status:
          $ref: "#/components/schemas/TransactionStatus"
        disbursedAt:
          type: string
          format: date-time
          nullable: true
          description: Date when the loan was disbursed
          example: "2024-01-01T00:00:00.000Z"
        loanTenor:
          type: integer
          description: Loan tenor in months
          example: 12
        expectedCompletionDate:
          type: string
          format: date-time
          description: Expected loan completion date
          example: "2024-12-31T00:00:00.000Z"
        requestedAmount:
          type: number
          description: Loan amount
          example: 500000

    LoanHistoryResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Loan history retrieved successfully"
        data:
          type: array
          items:
            $ref: "#/components/schemas/LoanHistoryItem"

    LoanHistoryItem:
      type: object
      properties:
        id:
          type: string
          description: Internal transaction ID
          example: "507f1f77bcf86cd799439011"
        transactionId:
          type: string
          description: Human-readable transaction ID
          example: "TXN-2024-001"
        status:
          $ref: "#/components/schemas/TransactionStatus"
        requestedAmount:
          type: number
          nullable: true
          description: Requested loan amount
          example: 500000
        loanTenor:
          type: integer
          nullable: true
          description: Loan tenor in months
          example: 12
        disbursedAt:
          type: string
          format: date-time
          nullable: true
          description: Date when loan was disbursed
          example: "2024-01-01T00:00:00.000Z"
        completedAt:
          type: string
          format: date-time
          nullable: true
          description: Date when loan was completed
          example: "2024-12-31T00:00:00.000Z"
        expectedCompletionDate:
          type: string
          format: date-time
          nullable: true
          description: Expected completion date based on tenor
          example: "2024-12-31T00:00:00.000Z"
        isActive:
          type: boolean
          description: Whether this loan is currently active
          example: false
        createdAt:
          type: string
          format: date-time
          description: Date when transaction was created
          example: "2024-01-01T00:00:00.000Z"
        submittedAt:
          type: string
          format: date-time
          nullable: true
          description: Date when transaction was submitted
          example: "2024-01-01T00:00:00.000Z"
        rejectedAt:
          type: string
          format: date-time
          nullable: true
          description: Date when transaction was rejected
          example: null
        rejectionReason:
          type: string
          nullable: true
          description: Reason for rejection
          example: null

    MarkLoanCompletedRequest:
      type: object
      properties:
        completionReason:
          type: string
          description: Reason for marking loan as completed
          example: "PAID_OFF_EARLY"
          enum:
            - "PAID_OFF_EARLY"
            - "TENOR_EXPIRED"
            - "ADMINISTRATIVE_COMPLETION"
            - "DEFAULTED"

    MarkLoanRepaidRequest:
      type: object
      properties:
        repaymentMethod:
          type: string
          description: Method used for repayment
          example: "Bank Transfer"
          enum:
            - "Bank Transfer"
            - "Cash"
            - "Cheque"
            - "Online Payment"
            - "Debit Card"
            - "Mobile Money"
        repaymentReference:
          type: string
          description: Reference number for the repayment transaction
          example: "TXN-REF-2024-001"
        notes:
          type: string
          description: Additional notes about the repayment
          example: "Full loan amount repaid via bank transfer"

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management operations
  - name: Transactions
    description: Transaction creation and management
  - name: Loan Types
    description: Loan type selection and configuration
  - name: Approvals
    description: Transaction approval workflow
  - name: Documents
    description: Document upload and management
  - name: Dashboard
    description: Analytics and reporting
  - name: Notifications
    description: System notifications
  - name: Loan Eligibility
    description: Loan eligibility validation and BVN-based loan management

paths:
  # Authentication Endpoints
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user with email and password. Returns OTP requirement.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoginRequest"
            example:
              email: "<EMAIL>"
              password: "password123"
      responses:
        "200":
          description: Login successful, OTP sent
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApiResponse"
              example:
                success: true
                message: "OTP sent to your email"
                data: {}
        "400":
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "429":
          description: Too many requests
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /auth/verify-otp:
    post:
      tags:
        - Authentication
      summary: Verify OTP and get tokens
      description: Verify OTP code and receive access and refresh tokens
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OTPVerificationRequest"
            example:
              email: "<EMAIL>"
              otp: "123456"
      responses:
        "200":
          description: OTP verified successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthResponse"
              example:
                success: true
                message: "Login successful"
                data:
                  accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  user:
                    id: "64f8a1b2c3d4e5f6a7b8c9d0"
                    email: "<EMAIL>"
                    firstName: "John"
                    lastName: "Doe"
                    role: "ACCOUNT_OFFICER"
        "400":
          description: Invalid or expired OTP
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /auth/refresh-token:
    post:
      tags:
        - Authentication
      summary: Refresh access token
      description: Get new access token using refresh token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenRequest"
      responses:
        "200":
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthResponse"
        "401":
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /auth/profile:
    get:
      tags:
        - Authentication
      summary: Get user profile
      description: Get current user's profile information
      responses:
        "200":
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/User"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Transaction Endpoints
  /transactions/loan-types:
    get:
      tags:
        - Loan Types
      summary: Get available loan types
      description: Retrieve all available loan types with their configurations
      responses:
        "200":
          description: Loan types retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/LoanTypeConfig"
              example:
                success: true
                message: "Loan types retrieved successfully"
                data:
                  - id: "CONSUMER_LOAN_PUBLIC"
                    name: "Consumer Loan Public"
                    description: "Loan for public sector employees"
                    requiredFields:
                      ["organizationName", "ippisNumber", "employmentDate"]
                  - id: "CONSUMER_LOAN_PRIVATE"
                    name: "Consumer Loan Private"
                    description: "Loan for private sector employees"
                    requiredFields:
                      ["organizationName", "remitaActivation", "employmentDate"]

  /transactions:
    post:
      tags:
        - Transactions
      summary: Create new transaction
      description: Create a new transaction in draft status (Account Officer only)
      responses:
        "201":
          description: Transaction created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: string
                          transactionId:
                            type: string
              example:
                success: true
                message: "Transaction created successfully"
                data:
                  id: "64f8a1b2c3d4e5f6a7b8c9d0"
                  transactionId: "TXN20241201001"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden - Account Officer role required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Loan Eligibility Endpoints
  /transactions/check-loan-eligibility:
    post:
      tags:
        - Loan Eligibility
      summary: Check loan eligibility by BVN
      description: |
        Check if a user is eligible for a new loan based on their BVN.
        This endpoint validates the single active loan policy and returns
        detailed information about eligibility status.

        **Business Rules:**
        - Only one active loan per BVN is allowed
        - Active loans include APPROVED and DISBURSED status
        - Users become eligible after their current loan tenor expires
        - COMPLETED loans don't affect eligibility

        **Use Cases:**
        - Pre-application eligibility check
        - Loan application validation
        - Customer service inquiries
        - Frontend form validation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoanEligibilityRequest"
            example:
              bvn: "***********"
      responses:
        "200":
          description: Eligibility check completed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoanEligibilityResponse"
              examples:
                eligible:
                  summary: User is eligible for new loan
                  value:
                    success: true
                    message: "You are eligible for a new loan"
                    data:
                      isEligible: true
                not_eligible:
                  summary: User has active loan
                  value:
                    success: true
                    message: "You are not eligible for a new loan at this time"
                    data:
                      isEligible: false
                      reason: "You have an active loan (TXN-2024-001) that expires on 12/31/2024. You can apply for a new loan after this date."
                      activeLoan:
                        transactionId: "TXN-2024-001"
                        status: "DISBURSED"
                        disbursedAt: "2024-01-01T00:00:00.000Z"
                        loanTenor: 12
                        expectedCompletionDate: "2024-12-31T00:00:00.000Z"
                        requestedAmount: 500000
                      eligibilityDate: "2024-12-31T00:00:00.000Z"
        "400":
          description: Invalid BVN format or validation error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                invalid_bvn:
                  summary: Invalid BVN format
                  value:
                    success: false
                    message: "BVN must be exactly 11 digits"
                validation_error:
                  summary: Validation error
                  value:
                    success: false
                    message: "Validation failed"
                    errors:
                      bvn: ["BVN must contain only digits"]
        "401":
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                success: false
                message: "Authentication required"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                success: false
                message: "Error checking loan eligibility"

  /transactions/loan-history/{bvn}:
    get:
      tags:
        - Loan Eligibility
      summary: Get loan history by BVN
      description: |
        Retrieve complete loan history for a user based on their BVN.
        Shows all loans (except DRAFT status) with their current status,
        amounts, tenors, and completion information.

        **Returned Information:**
        - All submitted loans for the BVN
        - Loan status and amounts
        - Disbursement and completion dates
        - Expected completion dates based on tenor
        - Active/inactive status indicators

        **Use Cases:**
        - Customer service inquiries
        - Loan history review
        - Eligibility verification
        - Audit and compliance
      parameters:
        - in: path
          name: bvn
          required: true
          schema:
            type: string
            pattern: "^[0-9]{11}$"
          description: Bank Verification Number (exactly 11 digits)
          example: "***********"
      responses:
        "200":
          description: Loan history retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoanHistoryResponse"
              example:
                success: true
                message: "Loan history retrieved successfully"
                data:
                  - id: "507f1f77bcf86cd799439011"
                    transactionId: "TXN-2024-001"
                    status: "COMPLETED"
                    requestedAmount: 500000
                    loanTenor: 12
                    disbursedAt: "2024-01-01T00:00:00.000Z"
                    completedAt: "2024-12-31T00:00:00.000Z"
                    expectedCompletionDate: "2024-12-31T00:00:00.000Z"
                    isActive: false
                    createdAt: "2023-12-15T00:00:00.000Z"
                    submittedAt: "2023-12-20T00:00:00.000Z"
                    rejectedAt: null
                    rejectionReason: null
                  - id: "507f1f77bcf86cd799439012"
                    transactionId: "TXN-2024-002"
                    status: "DISBURSED"
                    requestedAmount: 750000
                    loanTenor: 18
                    disbursedAt: "2024-06-01T00:00:00.000Z"
                    completedAt: null
                    expectedCompletionDate: "2025-12-01T00:00:00.000Z"
                    isActive: true
                    createdAt: "2024-05-15T00:00:00.000Z"
                    submittedAt: "2024-05-20T00:00:00.000Z"
                    rejectedAt: null
                    rejectionReason: null
        "400":
          description: Invalid BVN format
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                success: false
                message: "BVN must be exactly 11 digits"
        "401":
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /transactions/{transactionId}/mark-completed:
    patch:
      tags:
        - Loan Eligibility
      summary: Mark a loan as completed
      description: |
        Manually mark a loan as completed. This is useful for cases where:
        - Loan is paid off early
        - Loan tenor has expired
        - Administrative completion is required
        - System maintenance or corrections

        **Business Rules:**
        - Only APPROVED or DISBURSED loans can be marked as completed
        - Completed loans cannot be marked as completed again
        - This action affects loan eligibility for the BVN
        - Completion reason is logged for audit purposes

        **Use Cases:**
        - Early loan repayment processing
        - Administrative loan closure
        - System maintenance operations
        - Bulk loan completion processing
      parameters:
        - in: path
          name: transactionId
          required: true
          schema:
            type: string
          description: Transaction ID (internal MongoDB ObjectId)
          example: "507f1f77bcf86cd799439011"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MarkLoanCompletedRequest"
            example:
              completionReason: "PAID_OFF_EARLY"
      responses:
        "200":
          description: Loan marked as completed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApiResponse"
              example:
                success: true
                message: "Loan marked as completed successfully"
                data: {}
        "400":
          description: Invalid request or loan cannot be completed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                already_completed:
                  summary: Loan already completed
                  value:
                    success: false
                    message: "Transaction is already completed"
                invalid_status:
                  summary: Invalid loan status
                  value:
                    success: false
                    message: "Only disbursed or approved loans can be marked as completed"
        "401":
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                success: false
                message: "Transaction not found"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
