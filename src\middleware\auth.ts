import { Request, Response, NextFunction } from "express";
import { AuthUtils } from "../utils/auth";
import { Response<PERSON>and<PERSON> } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { UserRole } from "@prisma/client";
import prisma from "../db/db";

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      ResponseHandler.unauthorized(res, "Access token required");
      return;
    }

    const token = authHeader.substring(7);

    try {
      const payload = AuthUtils.verifyAccessToken(token);

      // Verify user still exists and is active
      const user = await prisma.user.findUnique({
        where: { id: payload.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
        },
      });

      if (!user || !user.isActive) {
        ResponseHandler.unauthorized(res, "Invalid or inactive user");
        return;
      }

      req.user = {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      };

      next();
    } catch (error) {
      ResponseHandler.unauthorized(res, "Invalid or expired token");
      return;
    }
  } catch (error) {
    ResponseHandler.serverError(res, "Authentication error");
    return;
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void => {
    if (!req.user) {
      ResponseHandler.unauthorized(res, "Authentication required");
      return;
    }

    if (!roles.includes(req.user.role)) {
      ResponseHandler.forbidden(res, "Insufficient permissions");
      return;
    }

    next();
  };
};

// Specific role-based middleware
export const requireSuperAdmin = authorize(UserRole.SUPER_ADMIN);
export const requireAccountOfficer = authorize(UserRole.ACCOUNT_OFFICER);
export const requireSupervisor = authorize(UserRole.SUPERVISOR);
export const requireHeadConsumerLending = authorize(
  UserRole.HEAD_CONSUMER_LENDING
);
export const requireHeadRiskManagement = authorize(
  UserRole.HEAD_RISK_MANAGEMENT
);
export const requireManagingDirector = authorize(UserRole.MANAGING_DIRECTOR);
export const requireAccountant = authorize(UserRole.ACCOUNTANT);

// Combined role middleware for approval workflow
export const requireApprovalRole = authorize(
  UserRole.SUPERVISOR,
  UserRole.HEAD_CONSUMER_LENDING,
  UserRole.HEAD_RISK_MANAGEMENT,
  UserRole.MANAGING_DIRECTOR,
  UserRole.ACCOUNTANT,
  UserRole.SUPER_ADMIN
);

export const requireAdminRole = authorize(
  UserRole.SUPER_ADMIN,
  UserRole.MANAGING_DIRECTOR
);
