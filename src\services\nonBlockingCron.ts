import * as cron from "node-cron";
import { WorkerManager } from "./workerManager";

export class NonBlockingCron {
  private static jobs: Map<string, cron.ScheduledTask> = new Map();

  /**
   * Schedule non-blocking XLSX export cleanup
   */
  static scheduleExportCleanup() {
    console.log("📅 Scheduling non-blocking export cleanup...");

    // Run cleanup every day at 2 AM
    const cleanupJob = cron.schedule(
      "0 2 * * *",
      async () => {
        console.log("🗑️ Starting scheduled export cleanup (non-blocking)...");

        // Use setImmediate to prevent blocking the event loop
        setImmediate(async () => {
          try {
            const result = await WorkerManager.runCleanupInWorker(24); // Clean files older than 24 hours

            if (result.success) {
              console.log("✅ Scheduled cleanup completed successfully");
            } else {
              console.error("❌ Scheduled cleanup failed:", result.error);
            }
          } catch (error) {
            console.error("❌ Scheduled cleanup error:", error);
          }
        });
      },
      {
        timezone: "America/Chicago",
      }
    );

    this.jobs.set("exportCleanup", cleanupJob);
    console.log("✅ Export cleanup scheduled for 2:00 AM daily");
  }

  /**
   * Schedule memory monitoring
   */
  static scheduleMemoryMonitoring() {
    console.log("📅 Scheduling memory monitoring...");

    // Monitor memory every 30 minutes
    const memoryJob = cron.schedule("*/30 * * * *", () => {
      // Use setImmediate to prevent blocking
      setImmediate(() => {
        const memUsage = process.memoryUsage();
        const activeWorkers = WorkerManager.getActiveWorkerCount();

        console.log("💾 Memory Status:", {
          timestamp: new Date().toISOString(),
          rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
          external: Math.round(memUsage.external / 1024 / 1024) + "MB",
          activeWorkers,
        });

        // Alert if memory usage is high
        const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
        if (heapUsedMB > 500) {
          // Alert if heap usage > 500MB
          console.warn("⚠️ HIGH MEMORY USAGE DETECTED:", heapUsedMB + "MB");

          // Force garbage collection if available
          if (global.gc) {
            global.gc();
            console.log("🗑️ Forced garbage collection triggered");
          }
        }
      });
    });

    this.jobs.set("memoryMonitoring", memoryJob);
    console.log("✅ Memory monitoring scheduled every 30 minutes");
  }

  /**
   * Start all non-blocking cron jobs
   */
  static startAllJobs() {
    console.log("🚀 Starting all non-blocking cron jobs...");

    this.scheduleExportCleanup();
    this.scheduleMemoryMonitoring();

    console.log(`✅ ${this.jobs.size} cron jobs started successfully`);
  }

  /**
   * Stop all cron jobs
   */
  static stopAllJobs() {
    console.log("🛑 Stopping all cron jobs...");

    for (const [name, job] of this.jobs) {
      job.stop();
      console.log(`✅ Stopped job: ${name}`);
    }

    this.jobs.clear();
    console.log("✅ All cron jobs stopped");
  }

  /**
   * Get status of all jobs
   */
  static getJobsStatus() {
    const status: { [key: string]: boolean } = {};

    for (const [name, job] of this.jobs) {
      status[name] = job.getStatus() === "scheduled";
    }

    return {
      totalJobs: this.jobs.size,
      activeWorkers: WorkerManager.getActiveWorkerCount(),
      jobs: status,
    };
  }

  /**
   * Graceful shutdown
   */
  static async gracefulShutdown() {
    console.log("🔄 Starting graceful shutdown of cron system...");

    // Stop all cron jobs
    this.stopAllJobs();

    // Terminate all active workers
    await WorkerManager.terminateAllWorkers();

    console.log("✅ Graceful shutdown completed");
  }
}
