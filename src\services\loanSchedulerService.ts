import prisma from "../db/db";
import { TransactionStatus } from "@prisma/client";
import { LoanEligibilityService } from "./loanEligibilityService";

export class LoanSchedulerService {
  /**
   * Check for expired loans and mark them as completed
   * This should be run as a scheduled job (e.g., daily)
   */
  static async processExpiredLoans(): Promise<{
    processedCount: number;
    expiredLoans: Array<{
      transactionId: string;
      bvn: string;
      disbursedAt: Date;
      expectedCompletionDate: Date;
    }>;
  }> {
    console.log("🔄 Processing expired loans...");

    // Find all disbursed loans that might be expired
    const disbursedLoans = await prisma.transaction.findMany({
      where: {
        status: TransactionStatus.DISBURSED,
        disbursedAt: {
          not: null,
        },
        loanTenor: {
          not: null,
        },
      },
      select: {
        id: true,
        transactionId: true,
        bvn: true,
        disbursedAt: true,
        loanTenor: true,
      },
    });

    const currentDate = new Date();
    const expiredLoans: Array<{
      transactionId: string;
      bvn: string;
      disbursedAt: Date;
      expectedCompletionDate: Date;
    }> = [];

    let processedCount = 0;

    for (const loan of disbursedLoans) {
      if (!loan.disbursedAt || !loan.loanTenor) continue;

      // Calculate expected completion date
      const expectedCompletionDate = new Date(loan.disbursedAt);
      expectedCompletionDate.setMonth(
        expectedCompletionDate.getMonth() + loan.loanTenor
      );

      // Check if loan has expired
      if (currentDate >= expectedCompletionDate) {
        try {
          await LoanEligibilityService.markLoanAsCompleted(
            loan.id,
            "TENOR_EXPIRED"
          );

          expiredLoans.push({
            transactionId: loan.transactionId,
            bvn: loan.bvn || "N/A",
            disbursedAt: loan.disbursedAt,
            expectedCompletionDate,
          });

          processedCount++;
          console.log(
            `✅ Marked loan ${loan.transactionId} as completed (tenor expired)`
          );
        } catch (error) {
          console.error(
            `❌ Error processing loan ${loan.transactionId}:`,
            error
          );
        }
      }
    }

    console.log(`🎉 Processed ${processedCount} expired loans`);

    return {
      processedCount,
      expiredLoans,
    };
  }

  /**
   * Get loans that will expire soon (within specified days)
   * Useful for sending reminder notifications
   */
  static async getLoansExpiringSoon(daysAhead: number = 30): Promise<
    Array<{
      transactionId: string;
      bvn: string;
      disbursedAt: Date;
      expectedCompletionDate: Date;
      daysUntilExpiry: number;
      createdBy: {
        email: string;
        firstName: string;
        lastName: string;
      };
    }>
  > {
    const disbursedLoans = await prisma.transaction.findMany({
      where: {
        status: TransactionStatus.DISBURSED,
        disbursedAt: {
          not: null,
        },
        loanTenor: {
          not: null,
        },
      },
      select: {
        id: true,
        transactionId: true,
        bvn: true,
        disbursedAt: true,
        loanTenor: true,
        createdBy: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    const currentDate = new Date();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + daysAhead);

    const expiringSoon: Array<{
      transactionId: string;
      bvn: string;
      disbursedAt: Date;
      expectedCompletionDate: Date;
      daysUntilExpiry: number;
      createdBy: {
        email: string;
        firstName: string;
        lastName: string;
      };
    }> = [];

    for (const loan of disbursedLoans) {
      if (!loan.disbursedAt || !loan.loanTenor) continue;

      // Calculate expected completion date
      const expectedCompletionDate = new Date(loan.disbursedAt);
      expectedCompletionDate.setMonth(
        expectedCompletionDate.getMonth() + loan.loanTenor
      );

      // Check if loan expires within the specified days
      if (
        expectedCompletionDate > currentDate &&
        expectedCompletionDate <= cutoffDate
      ) {
        const daysUntilExpiry = Math.ceil(
          (expectedCompletionDate.getTime() - currentDate.getTime()) /
            (1000 * 60 * 60 * 24)
        );

        expiringSoon.push({
          transactionId: loan.transactionId,
          bvn: loan.bvn || "N/A",
          disbursedAt: loan.disbursedAt,
          expectedCompletionDate,
          daysUntilExpiry,
          createdBy: loan.createdBy,
        });
      }
    }

    return expiringSoon.sort((a, b) => a.daysUntilExpiry - b.daysUntilExpiry);
  }

  /**
   * Get statistics about loan tenors and completion rates
   */
  static async getLoanTenorStatistics(): Promise<{
    totalLoans: number;
    completedLoans: number;
    activeLoans: number;
    averageTenor: number;
    completionRate: number;
    tenorDistribution: Record<number, number>;
  }> {
    const allLoans = await prisma.transaction.findMany({
      where: {
        status: {
          in: [
            TransactionStatus.DISBURSED,
            TransactionStatus.COMPLETED,
            TransactionStatus.LOAN_REPAID,
          ],
        },
        loanTenor: {
          not: null,
        },
      },
      select: {
        status: true,
        loanTenor: true,
      },
    });

    const totalLoans = allLoans.length;
    const completedLoans = allLoans.filter(
      (loan) =>
        loan.status === TransactionStatus.COMPLETED ||
        loan.status === TransactionStatus.LOAN_REPAID
    ).length;
    const activeLoans = allLoans.filter(
      (loan) => loan.status === TransactionStatus.DISBURSED
    ).length;

    const tenors = allLoans.map((loan) => loan.loanTenor!);
    const averageTenor =
      tenors.length > 0
        ? tenors.reduce((sum, tenor) => sum + tenor, 0) / tenors.length
        : 0;
    const completionRate =
      totalLoans > 0 ? (completedLoans / totalLoans) * 100 : 0;

    // Calculate tenor distribution
    const tenorDistribution: Record<number, number> = {};
    tenors.forEach((tenor) => {
      tenorDistribution[tenor] = (tenorDistribution[tenor] || 0) + 1;
    });

    return {
      totalLoans,
      completedLoans,
      activeLoans,
      averageTenor: Math.round(averageTenor * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
      tenorDistribution,
    };
  }

  /**
   * Initialize scheduled jobs (to be called on server startup)
   * This would typically use a job scheduler like node-cron
   */
  static initializeScheduledJobs(): void {
    console.log("📅 Initializing loan scheduler jobs...");

    // Example: Run expired loan processing daily at midnight
    // const cron = require('node-cron');
    // cron.schedule('0 0 * * *', async () => {
    //   try {
    //     await this.processExpiredLoans();
    //   } catch (error) {
    //     console.error('Error in scheduled loan processing:', error);
    //   }
    // });

    // Example: Send expiry reminders weekly
    // cron.schedule('0 9 * * 1', async () => {
    //   try {
    //     const expiringSoon = await this.getLoansExpiringSoon(30);
    //     // Send reminder emails to users
    //     console.log(`Found ${expiringSoon.length} loans expiring soon`);
    //   } catch (error) {
    //     console.error('Error in scheduled reminder processing:', error);
    //   }
    // });

    console.log("✅ Loan scheduler jobs initialized");
  }
}
