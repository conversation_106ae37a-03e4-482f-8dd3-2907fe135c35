import prisma from '../db/db';
import { NotificationData } from '../types';
import { UserRole, NotificationType } from '@prisma/client';

export class NotificationService {
  static async createNotification(data: NotificationData): Promise<void> {
    await prisma.notification.create({
      data: {
        userId: data.userId,
        transactionId: data.transactionId,
        type: data.type as NotificationType,
        title: data.title,
        message: data.message,
      },
    });
  }

  static async notifyRoleUsers(
    role: UserRole,
    title: string,
    message: string,
    transactionId?: string
  ): Promise<void> {
    const users = await prisma.user.findMany({
      where: {
        role,
        isActive: true,
      },
      select: {
        id: true,
      },
    });

    const notifications = users.map(user => ({
      userId: user.id,
      transactionId,
      type: NotificationType.TRANSACTION_SUBMITTED,
      title,
      message,
    }));

    await prisma.notification.createMany({
      data: notifications,
    });
  }

  static async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20,
    unreadOnly: boolean = false
  ): Promise<{ notifications: any[]; total: number; unreadCount: number }> {
    const skip = (page - 1) * limit;

    const whereClause: any = { userId };
    if (unreadOnly) {
      whereClause.isRead = false;
    }

    const [notifications, total, unreadCount] = await Promise.all([
      prisma.notification.findMany({
        where: whereClause,
        include: {
          transaction: {
            select: {
              transactionId: true,
              status: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.notification.count({ where: whereClause }),
      prisma.notification.count({
        where: {
          userId,
          isRead: false,
        },
      }),
    ]);

    return {
      notifications,
      total,
      unreadCount,
    };
  }

  static async markAsRead(notificationId: string, userId: string): Promise<void> {
    await prisma.notification.updateMany({
      where: {
        id: notificationId,
        userId,
      },
      data: {
        isRead: true,
      },
    });
  }

  static async markAllAsRead(userId: string): Promise<void> {
    await prisma.notification.updateMany({
      where: {
        userId,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });
  }

  static async deleteNotification(notificationId: string, userId: string): Promise<void> {
    await prisma.notification.deleteMany({
      where: {
        id: notificationId,
        userId,
      },
    });
  }

  static async getUnreadCount(userId: string): Promise<number> {
    return await prisma.notification.count({
      where: {
        userId,
        isRead: false,
      },
    });
  }
}
