# Complete Swagger Documentation for AMS Loan System
# This file contains all endpoints - use this as your complete API documentation

# Document Management Endpoints
/documents/{transactionId}/upload:
  post:
    tags:
      - Documents
    summary: Upload single document
    description: Upload a single document for a transaction
    parameters:
      - name: transactionId
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              document:
                type: string
                format: binary
    responses:
      "201":
        description: Document uploaded successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      $ref: "#/components/schemas/Document"

/documents/{transactionId}/upload-multiple:
  post:
    tags:
      - Documents
    summary: Upload multiple documents
    description: Upload multiple documents for a transaction (max 10 files)
    parameters:
      - name: transactionId
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              documents:
                type: array
                items:
                  type: string
                  format: binary
                maxItems: 10
    responses:
      "201":
        description: Documents uploaded successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        successful:
                          type: array
                          items:
                            $ref: "#/components/schemas/Document"
                        failed:
                          type: array
                          items:
                            type: object

/documents/{transactionId}:
  get:
    tags:
      - Documents
    summary: Get transaction documents
    description: Get all documents for a transaction
    parameters:
      - name: transactionId
        in: path
        required: true
        schema:
          type: string
    responses:
      "200":
        description: Documents retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: "#/components/schemas/Document"

/documents/download/{documentId}:
  get:
    tags:
      - Documents
    summary: Download document
    description: Download a specific document
    parameters:
      - name: documentId
        in: path
        required: true
        schema:
          type: string
    responses:
      "302":
        description: Redirect to document URL
      "404":
        description: Document not found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"

/documents/{documentId}/signed-url:
  get:
    tags:
      - Documents
    summary: Generate signed URL for document access
    description: |
      Generate a time-limited signed URL for secure document access.
      The URL expires after the specified time (default 1 hour, max 24 hours).

      **Security Features:**
      - Role-based access control (account officers can only access their own documents)
      - Configurable expiration time with maximum limit
      - Audit logging for access attempts
      - Rate limiting to prevent abuse
    parameters:
      - name: documentId
        in: path
        required: true
        schema:
          type: string
          pattern: "^[0-9a-fA-F]{24}$"
        description: Document ID (MongoDB ObjectID format)
        example: "507f1f77bcf86cd799439011"
      - name: expiresIn
        in: query
        required: false
        schema:
          type: integer
          minimum: 300
          maximum: 86400
          default: 3600
        description: URL expiration time in seconds (5 minutes to 24 hours)
        example: 3600
    responses:
      "200":
        description: Signed URL generated successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: "Signed URL generated successfully"
                data:
                  $ref: "#/components/schemas/DocumentSignedUrlResponse"
      "400":
        description: Bad request (invalid document ID or expiration time)
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
      "401":
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
      "403":
        description: Forbidden (insufficient permissions)
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
      "404":
        description: Document not found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
      "429":
        description: Too many requests
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"

# Approval Endpoints
/approvals/pending:
  get:
    tags:
      - Approvals
    summary: Get pending approvals
    description: Get transactions pending approval for current user's role
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
    responses:
      "200":
        description: Pending approvals retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        transactions:
                          type: array
                          items:
                            $ref: "#/components/schemas/Transaction"
                        total:
                          type: integer

/approvals/pending-approvals:
  get:
    tags:
      - Approvals
    summary: Get transactions pending approval at current user's stage
    description: |
      Retrieve transactions that are currently at the approval stage corresponding to the logged-in user's role.
      Each role can only see transactions at their specific approval stage.

      **Role-Stage Mapping:**
      - SUPERVISOR: Transactions at SUPERVISOR stage
      - HEAD_CONSUMER_LENDING: Transactions at HEAD_CONSUMER_LENDING stage
      - HEAD_RISK_MANAGEMENT: Transactions at HEAD_RISK_MANAGEMENT stage
      - MANAGING_DIRECTOR: Transactions at MANAGING_DIRECTOR stage
      - ACCOUNTANT: Transactions at ACCOUNTANT stage (ready for disbursement)
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          minimum: 1
          default: 1
        description: Page number for pagination
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
        description: Number of items per page
      - name: accountOfficerId
        in: query
        schema:
          type: string
        description: Filter by specific account officer (creator of transactions)
      - name: loanType
        in: query
        schema:
          type: string
          enum:
            [
              CONSUMER_LOAN_PUBLIC,
              CONSUMER_LOAN_PRIVATE,
              SME_INDIVIDUAL,
              SME_CORPORATE,
            ]
        description: Filter by loan type
    responses:
      "200":
        description: Pending approvals retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: "Pending approvals retrieved successfully"
                data:
                  type: object
                  properties:
                    transactions:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                          customerName:
                            type: string
                          loanType:
                            type: string
                            enum:
                              [
                                CONSUMER_LOAN_PUBLIC,
                                CONSUMER_LOAN_PRIVATE,
                                SME_INDIVIDUAL,
                                SME_CORPORATE,
                              ]
                          requestedAmount:
                            type: number
                          status:
                            type: string
                            enum: [SUBMITTED, IN_PROGRESS, APPROVED]
                          currentStage:
                            type: string
                            enum:
                              [
                                SUPERVISOR,
                                HEAD_CONSUMER_LENDING,
                                HEAD_RISK_MANAGEMENT,
                                MANAGING_DIRECTOR,
                                ACCOUNTANT,
                              ]
                          createdAt:
                            type: string
                            format: date-time
                          submittedAt:
                            type: string
                            format: date-time
                          createdBy:
                            type: object
                            properties:
                              id:
                                type: string
                              firstName:
                                type: string
                              lastName:
                                type: string
                              email:
                                type: string
                          documentsCount:
                            type: integer
                          canApprove:
                            type: boolean
                          stageInfo:
                            type: object
                            properties:
                              stageName:
                                type: string
                              actionLabel:
                                type: string
                              isUrgent:
                                type: boolean
                    total:
                      type: integer
                    page:
                      type: integer
                    limit:
                      type: integer
                    totalPages:
                      type: integer
      "401":
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
      "403":
        description: Forbidden - Approval role required
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"

/approvals/{transactionId}/process:
  post:
    tags:
      - Approvals
    summary: Process approval
    description: Approve, reject, or send back a transaction
    parameters:
      - name: transactionId
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApprovalActionRequest"
          examples:
            approve:
              summary: Approve transaction
              value:
                action: "APPROVED"
                comments: "All documents verified and approved"
            reject:
              summary: Reject transaction
              value:
                action: "REJECTED"
                comments: "Insufficient documentation"
            send_back:
              summary: Send back for corrections
              value:
                action: "SENT_BACK"
                comments: "Please provide updated employment certificate"
    responses:
      "200":
        description: Approval processed successfully
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiResponse"

/approvals/{transactionId}/history:
  get:
    tags:
      - Approvals
    summary: Get approval history
    description: Get approval history for a transaction
    parameters:
      - name: transactionId
        in: path
        required: true
        schema:
          type: string
    responses:
      "200":
        description: Approval history retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: "#/components/schemas/TransactionApproval"

# Dashboard Endpoints
/dashboard/stats:
  get:
    tags:
      - Dashboard
    summary: Get user dashboard stats
    description: Get dashboard statistics for current user
    responses:
      "200":
        description: Dashboard stats retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      $ref: "#/components/schemas/DashboardStats"

/dashboard/my-transactions:
  get:
    tags:
      - Dashboard
    summary: Get my transactions
    description: Get transactions for current user with filtering
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
      - name: status
        in: query
        schema:
          $ref: "#/components/schemas/TransactionStatus"
      - name: dateFrom
        in: query
        schema:
          type: string
          format: date
      - name: dateTo
        in: query
        schema:
          type: string
          format: date
    responses:
      "200":
        description: My transactions retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        transactions:
                          type: array
                          items:
                            $ref: "#/components/schemas/Transaction"
                        total:
                          type: integer
                        page:
                          type: integer
                        limit:
                          type: integer

/dashboard/system-overview:
  get:
    tags:
      - Dashboard
    summary: Get system overview (Admin only)
    description: Get system-wide overview statistics
    responses:
      "200":
        description: System overview retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      $ref: "#/components/schemas/DashboardStats"

# Notification Endpoints
/notifications:
  get:
    tags:
      - Notifications
    summary: Get user notifications
    description: Get notifications for current user
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
    responses:
      "200":
        description: Notifications retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        notifications:
                          type: array
                          items:
                            $ref: "#/components/schemas/Notification"
                        total:
                          type: integer
                        unreadCount:
                          type: integer

/notifications/{notificationId}/read:
  patch:
    tags:
      - Notifications
    summary: Mark notification as read
    description: Mark a specific notification as read
    parameters:
      - name: notificationId
        in: path
        required: true
        schema:
          type: string
    responses:
      "200":
        description: Notification marked as read
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiResponse"

/notifications/mark-all-read:
  patch:
    tags:
      - Notifications
    summary: Mark all notifications as read
    description: Mark all notifications as read for current user
    responses:
      "200":
        description: All notifications marked as read
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApiResponse"

# User Management Endpoints (Admin only)
/users:
  post:
    tags:
      - Users
    summary: Create new user (Admin only)
    description: Create a new user account
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CreateUserRequest"
    responses:
      "201":
        description: User created successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      $ref: "#/components/schemas/User"

  get:
    tags:
      - Users
    summary: Get all users (Admin only)
    description: Get all users with filtering and pagination
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 10
      - name: role
        in: query
        schema:
          $ref: "#/components/schemas/UserRole"
      - name: isActive
        in: query
        schema:
          type: boolean
    responses:
      "200":
        description: Users retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        users:
                          type: array
                          items:
                            $ref: "#/components/schemas/User"
                        total:
                          type: integer

# Transaction Approval History Endpoint
/transactions/{transactionId}/approval-history:
  get:
    tags:
      - Transactions
    summary: Get detailed approval history for a specific transaction
    description: |
      Returns a chronological approval history for a specific transaction showing the complete approval workflow journey.
      Displays each stage, approver details, actions taken, comments, and timestamps in a formatted structure.

      **Features:**
      - Complete approval workflow journey
      - Human-readable stage names
      - Approver details with names and roles
      - Chronological order (oldest to newest)
      - Formatted dates for display
      - Comments and action details
    parameters:
      - name: transactionId
        in: path
        required: true
        schema:
          type: string
        description: Transaction ID
    responses:
      "200":
        description: Approval history retrieved successfully
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/ApiResponse"
                - type: object
                  properties:
                    data:
                      type: object
                      properties:
                        transactionId:
                          type: string
                          description: The transaction ID
                          example: "TXN-12345"
                        approvalHistory:
                          type: array
                          description: Chronological list of approval actions
                          items:
                            type: object
                            properties:
                              stage:
                                type: string
                                enum:
                                  [
                                    ACCOUNT_OFFICER,
                                    SUPERVISOR,
                                    HEAD_CONSUMER_LENDING,
                                    HEAD_RISK_MANAGEMENT,
                                    MANAGING_DIRECTOR,
                                    ACCOUNTANT,
                                  ]
                                description: Approval stage enum value
                                example: "SUPERVISOR"
                              stageName:
                                type: string
                                description: Human-readable stage name
                                example: "Supervisor Review"
                              action:
                                type: string
                                enum:
                                  [
                                    APPROVED,
                                    REJECTED,
                                    SENT_BACK,
                                    DISBURSED,
                                    SUBMITTED,
                                  ]
                                description: Action taken by the approver
                                example: "APPROVED"
                              approver:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    description: Approver user ID
                                    example: "user-123"
                                  name:
                                    type: string
                                    description: Full name of the approver
                                    example: "Jane Supervisor"
                                  role:
                                    type: string
                                    enum:
                                      [
                                        SUPER_ADMIN,
                                        ACCOUNT_OFFICER,
                                        SUPERVISOR,
                                        HEAD_CONSUMER_LENDING,
                                        HEAD_RISK_MANAGEMENT,
                                        MANAGING_DIRECTOR,
                                        ACCOUNTANT,
                                      ]
                                    description: Role of the approver
                                    example: "SUPERVISOR"
                              comments:
                                type: string
                                nullable: true
                                description: Comments provided by the approver
                                example: "Application looks good, approved for next stage"
                              timestamp:
                                type: string
                                format: date-time
                                description: ISO timestamp of the action
                                example: "2025-06-15T10:30:00Z"
                              formattedDate:
                                type: string
                                description: Human-readable date format
                                example: "6/15/2025"
      "404":
        description: Transaction not found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
      "401":
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ErrorResponse"
