import { Response } from "express";
import { BulkDisbursementService } from "../services/bulkDisbursementService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";

export class BulkDisbursementController {
  /**
   * Get transactions eligible for disbursement
   * GET /api/v1/transactions/eligible-for-disbursement
   */
  static getEligibleTransactions = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const accountantId = req.user!.id;
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      console.log("Getting eligible transactions:", { 
        accountantId, 
        limit, 
        offset 
      });

      // Validate pagination parameters
      if (limit < 1 || limit > 100) {
        return ResponseHandler.error(
          res,
          "Limit must be between 1 and 100",
          undefined,
          400
        );
      }

      if (offset < 0) {
        return ResponseHandler.error(
          res,
          "Offset must be non-negative",
          undefined,
          400
        );
      }

      const result = await BulkDisbursementService.getEligibleTransactions(
        accountantId,
        limit,
        offset
      );

      console.log("Eligible transactions retrieved:", {
        count: result.transactions.length,
        total: result.total,
        hasMore: result.hasMore,
      });

      ResponseHandler.success(
        res,
        "Eligible transactions retrieved successfully",
        {
          transactions: result.transactions,
          pagination: {
            total: result.total,
            limit,
            offset,
            hasMore: result.hasMore,
            returned: result.transactions.length,
          },
        }
      );
    }
  );

  /**
   * Process bulk disbursement
   * POST /api/v1/transactions/bulk-disburse
   */
  static processBulkDisbursement = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionIds } = req.body;
      const accountantId = req.user!.id;

      console.log("Processing bulk disbursement:", {
        accountantId,
        transactionCount: transactionIds?.length || 0,
        timestamp: new Date().toISOString(),
      });

      // Validate request body
      if (!transactionIds || !Array.isArray(transactionIds)) {
        return ResponseHandler.error(
          res,
          "transactionIds must be an array",
          undefined,
          400
        );
      }

      if (transactionIds.length === 0) {
        return ResponseHandler.error(
          res,
          "At least one transaction ID is required",
          undefined,
          400
        );
      }

      if (transactionIds.length > 100) {
        return ResponseHandler.error(
          res,
          "Maximum 100 transactions can be processed at once",
          undefined,
          400
        );
      }

      // Validate all transaction IDs are strings
      const invalidIds = transactionIds.filter(id => typeof id !== 'string' || !id.trim());
      if (invalidIds.length > 0) {
        return ResponseHandler.error(
          res,
          "All transaction IDs must be non-empty strings",
          undefined,
          400
        );
      }

      try {
        const summary = await BulkDisbursementService.processBulkDisbursement(
          transactionIds,
          accountantId
        );

        console.log("Bulk disbursement completed successfully:", {
          totalTransactions: summary.totalTransactions,
          successful: summary.successfulDisbursements,
          failed: summary.failedDisbursements,
          totalAmount: summary.totalAmount,
        });

        ResponseHandler.success(
          res,
          `Successfully disbursed ${summary.successfulDisbursements} of ${summary.totalTransactions} transactions`,
          summary
        );

      } catch (error: any) {
        console.error("Bulk disbursement failed:", {
          accountantId,
          transactionCount: transactionIds.length,
          error: error.message,
        });

        // If the error contains partial results, include them in the response
        if (error.data) {
          return ResponseHandler.error(
            res,
            error.message,
            error.data,
            error.statusCode || 400
          );
        }

        throw error;
      }
    }
  );

  /**
   * Get disbursement statistics
   * GET /api/v1/transactions/disbursement-stats
   */
  static getDisbursementStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const accountantId = req.user!.id;

      console.log("Getting disbursement statistics:", { accountantId });

      const stats = await BulkDisbursementService.getDisbursementStats(accountantId);

      console.log("Disbursement statistics retrieved:", stats);

      ResponseHandler.success(
        res,
        "Disbursement statistics retrieved successfully",
        stats
      );
    }
  );

  /**
   * Validate transactions for bulk disbursement (dry run)
   * POST /api/v1/transactions/validate-bulk-disbursement
   */
  static validateBulkDisbursement = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionIds } = req.body;
      const accountantId = req.user!.id;

      console.log("Validating bulk disbursement:", {
        accountantId,
        transactionCount: transactionIds?.length || 0,
      });

      // Validate request body
      if (!transactionIds || !Array.isArray(transactionIds)) {
        return ResponseHandler.error(
          res,
          "transactionIds must be an array",
          undefined,
          400
        );
      }

      if (transactionIds.length === 0) {
        return ResponseHandler.error(
          res,
          "At least one transaction ID is required",
          undefined,
          400
        );
      }

      if (transactionIds.length > 100) {
        return ResponseHandler.error(
          res,
          "Maximum 100 transactions can be processed at once",
          undefined,
          400
        );
      }

      // Remove duplicates
      const uniqueTransactionIds = [...new Set(transactionIds)];

      // Fetch and validate all transactions
      const transactions = await BulkDisbursementService.getEligibleTransactions(
        accountantId,
        1000, // Get all eligible transactions for validation
        0
      );

      const eligibleIds = transactions.transactions.map(t => t.id);
      const validTransactions = uniqueTransactionIds.filter(id => eligibleIds.includes(id));
      const invalidTransactions = uniqueTransactionIds.filter(id => !eligibleIds.includes(id));

      const totalAmount = transactions.transactions
        .filter(t => validTransactions.includes(t.id))
        .reduce((sum, t) => sum + t.requestedAmount, 0);

      const validationResult = {
        totalRequested: uniqueTransactionIds.length,
        validTransactions: validTransactions.length,
        invalidTransactions: invalidTransactions.length,
        totalAmount,
        canProceed: invalidTransactions.length === 0,
        issues: invalidTransactions.length > 0 ? [
          `${invalidTransactions.length} transaction(s) are not eligible for disbursement`
        ] : [],
      };

      console.log("Bulk disbursement validation completed:", validationResult);

      ResponseHandler.success(
        res,
        "Bulk disbursement validation completed",
        validationResult
      );
    }
  );
}
