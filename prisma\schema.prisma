// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  ACCOUNT_OFFICER
  SUPERVISOR
  HEAD_CONSUMER_LENDING
  HEAD_RISK_MANAGEMENT
  MANAGING_DIRECTOR
  ACCOUNTANT
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum MaritalStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
  SEPARATED
}

enum RepaymentMode {
  MONTHLY
  QUARTERLY
  SEMI_ANNUALLY
  ANNUALLY
}

enum TransactionStatus {
  DRAFT
  SUBMITTED
  IN_PROGRESS
  APPROVED
  REJECTED
  SENT_BACK
  DISBURSED
  COMPLETED
  LOAN_REPAID
}

enum ApprovalStage {
  ACCOUNT_OFFICER
  SUPERVISOR
  HEAD_CONSUMER_LENDING
  HEAD_RISK_MANAGEMENT
  MANAGING_DIRECTOR
  ACCOUNTANT
}

enum LoanType {
  CONSUMER_LOAN_PUBLIC
  CONSUMER_LOAN_PRIVATE
  SME_INDIVIDUAL
  SME_CORPORATE
}

enum NotificationType {
  TRANSACTION_SUBMITTED
  TRANSACTION_APPROVED
  TRANSACTION_REJECTED
  TRANSACTION_SENT_BACK
  TRANSACTION_DISBURSED
  TRANSACTION_LOAN_REPAID
  TRANSACTION_TRANSFERRED
  OTP_CODE
  SYSTEM_ALERT
}

enum FileType {
  PDF
  IMAGE
  DOCUMENT
}

// Models
model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  email         String    @unique
  password      String
  firstName     String
  lastName      String
  role          UserRole
  isActive      Boolean   @default(true)
  monthlyTarget Float? // Changed from Decimal to Float for MongoDB
  lastLogin     DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  createdTransactions Transaction[]         @relation("CreatedBy")
  approvals           TransactionApproval[]
  notifications       Notification[]
  otpCodes            OtpCode[]
  PasswordResetToken  PasswordResetToken[]

  // Indexes for performance optimization
  @@index([role])
  @@index([isActive])
  @@index([role, isActive])
  @@index([createdAt])
  @@map("users")
}

model Transaction {
  id            String            @id @default(auto()) @map("_id") @db.ObjectId
  transactionId String            @unique // Generated transaction ID
  status        TransactionStatus @default(DRAFT)
  currentStage  ApprovalStage?    @default(ACCOUNT_OFFICER)

  // Loan Type Selection
  loanType LoanType?

  // Step 1: Personal Information
  firstName        String?
  middleName       String?
  lastName         String?
  gender           Gender?
  maritalStatus    MaritalStatus?
  dateOfBirth      DateTime?
  bvn              String?
  nin              String?
  email            String?
  phoneNumber      String?
  street           String?
  city             String?
  state            String?
  postalCode       String?
  organizationName String?
  ippisNumber      String? // For Consumer Loan Public
  employmentDate   DateTime?

  // Additional fields for different loan types
  remitaActivation   String? // For Consumer Loan Private, SME Individual
  businessName       String? // For SME Corporate
  registrationNumber String? // For SME Corporate

  // Step 2: Next of Kin
  nokFirstName    String?
  nokMiddleName   String?
  nokLastName     String?
  nokEmail        String?
  nokRelationship String?
  nokPhoneNumber  String?
  nokStreet       String?
  nokCity         String?
  nokState        String?
  nokPostalCode   String?

  // Step 3: Loan Information
  requestedAmount Float? // Changed from Decimal to Float for MongoDB
  loanTenor       Int? // in months
  repaymentMode   RepaymentMode?
  grossPay        Float? // Changed from Decimal to Float for MongoDB
  netPay          Float? // Changed from Decimal to Float for MongoDB
  purposeOfLoan   String?

  // Step 4: Disbursement
  accountName   String?
  accountNumber String?
  bankName      String?

  // Step 5: Documents (handled separately in Document model)

  // Metadata
  createdById     String    @db.ObjectId
  submittedAt     DateTime?
  disbursedAt     DateTime? // When the loan was disbursed
  repaidAt        DateTime? // When the loan was repaid by user
  completedAt     DateTime? // When the loan tenor period ended or was completed
  rejectedAt      DateTime?
  rejectionReason String?
  sentBackReason  String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  createdBy     User                  @relation("CreatedBy", fields: [createdById], references: [id])
  approvals     TransactionApproval[]
  documents     Document[]
  notifications Notification[]

  // Indexes for performance optimization
  @@index([status])
  @@index([currentStage])
  @@index([createdById])
  @@index([status, currentStage])
  @@index([status, createdById])
  @@index([createdAt])
  @@index([bvn])
  @@index([firstName, lastName])
  @@index([email])
  @@index([disbursedAt])
  @@index([createdAt, status])
  @@map("transactions")
}

model TransactionApproval {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  transactionId String        @db.ObjectId
  approverId    String        @db.ObjectId
  stage         ApprovalStage
  action        String // APPROVED, REJECTED, SENT_BACK
  comments      String?
  createdAt     DateTime      @default(now())

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  approver    User        @relation(fields: [approverId], references: [id])

  @@unique([transactionId, stage])
  // Indexes for performance optimization
  @@index([transactionId])
  @@index([approverId])
  @@index([stage])
  @@index([createdAt])
  @@map("transaction_approvals")
}

model Document {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  transactionId String   @db.ObjectId
  fileName      String // R2 key (path in bucket)
  originalName  String
  filePath      String // R2 public URL
  // R2 specific fields
  r2Key         String? // R2 object key (for new uploads)
  r2Bucket      String? // R2 bucket name
  r2Etag        String? // R2 ETag for integrity
  // Legacy Cloudinary fields (for backward compatibility during migration)
  cloudinaryId  String? // Cloudinary public_id (for backward compatibility)
  fileSize      Int
  fileType      FileType
  mimeType      String
  metadata      String? // Custom metadata for document categorization/identification
  uploadedAt    DateTime @default(now())

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  // Indexes for performance optimization
  @@index([transactionId])
  @@index([fileType])
  @@index([uploadedAt])
  @@map("documents")
}

model Notification {
  id            String           @id @default(auto()) @map("_id") @db.ObjectId
  userId        String           @db.ObjectId
  transactionId String?          @db.ObjectId
  type          NotificationType
  title         String
  message       String
  isRead        Boolean          @default(false)
  createdAt     DateTime         @default(now())

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  transaction Transaction? @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  // Indexes for performance optimization
  @@index([userId])
  @@index([isRead])
  @@index([createdAt])
  @@index([userId, isRead])
  @@index([transactionId])
  @@map("notifications")
}

model OtpCode {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  code      String
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance optimization
  @@index([userId])
  @@index([isUsed])
  @@index([createdAt])
  @@index([expiresAt])
  @@index([isUsed, createdAt])
  @@map("otp_codes")
}

model PasswordResetToken {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  token     String   @unique
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance optimization
  @@index([userId])
  @@index([isUsed])
  @@index([createdAt])
  @@index([expiresAt])
  @@index([isUsed, createdAt])
  @@map("password_reset_tokens")
}

model SystemSettings {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  key         String   @unique
  value       String
  description String?
  updatedAt   DateTime @updatedAt

  @@map("system_settings")
}
