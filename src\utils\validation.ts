import { z } from "zod";
import {
  UserRole,
  Gender,
  MaritalStatus,
  RepaymentMode,
  TransactionStatus,
  ApprovalStage,
  LoanType,
} from "@prisma/client";

// Auth Schemas
export const loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(1, "Password is required"),
});

export const verifyOtpSchema = z.object({
  email: z.string().email("Invalid email format"),
  otp: z.string().length(6, "OTP must be 6 digits"),
});

export const createUserSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  role: z.nativeEnum(UserRole),
  monthlyTarget: z.number().positive().optional(),
});

export const createSuperAdminSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
});

export const updateUserSchema = z.object({
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  email: z.string().email("Invalid email format").optional(),
  role: z.nativeEnum(UserRole).optional(),
  monthlyTarget: z
    .number()
    .positive("Monthly target must be positive")
    .optional(),
  isActive: z.boolean().optional(),
});

export const bulkDisbursementSchema = z.object({
  transactionIds: z
    .array(z.string().min(1, "Transaction ID cannot be empty"))
    .min(1, "At least one transaction ID is required")
    .max(100, "Maximum 100 transactions can be processed at once"),
});

export const passwordResetRequestSchema = z.object({
  email: z.string().email("Invalid email format"),
});

export const passwordResetConfirmSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  newPassword: z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one lowercase letter, one uppercase letter, and one number"
    ),
});

export const validateResetTokenSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
});

export const loanEligibilitySchema = z.object({
  bvn: z
    .string()
    .min(11, "BVN must be exactly 11 digits")
    .max(11, "BVN must be exactly 11 digits")
    .regex(/^\d{11}$/, "BVN must contain only digits"),
});

export const markLoanCompletedSchema = z.object({
  completionReason: z.string().optional(),
});

export const markLoanRepaidSchema = z.object({
  repaymentMethod: z.string().optional(),
  repaymentReference: z.string().optional(),
  notes: z.string().optional(),
});

// Transaction Schemas
export const loanTypeSelectionSchema = z.object({
  loanType: z.nativeEnum(LoanType),
});

// Base personal info schema with common fields
const basePersonalInfoSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  gender: z.nativeEnum(Gender),
  maritalStatus: z.nativeEnum(MaritalStatus),
  dateOfBirth: z.string().transform((str) => new Date(str)),
  bvn: z
    .string()
    .min(11, "BVN must be exactly 11 digits")
    .max(11, "BVN must be exactly 11 digits")
    .regex(/^\d{11}$/, "BVN must contain only digits")
    .transform((val) => val.trim().replace(/\s+/g, "")), // Normalize BVN format
  nin: z
    .string()
    .min(11, "NIN must be at least 11 digits")
    .max(11, "NIN must be at least 11 digits"),
  email: z.string().email("Invalid email format").optional(),
  phoneNumber: z.string().min(10, "Phone number must be at least 10 digits"),
  street: z.string().min(1, "Street address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  postalCode: z.string().optional(),
  employmentDate: z.string().transform((str) => new Date(str)),
});

// Dynamic personal info schemas based on loan type
export const consumerLoanPublicSchema = basePersonalInfoSchema.extend({
  organizationName: z.string().min(1, "Organization name is required"),
  ippisNumber: z.string().min(1, "IPPIS number is required"),
});

export const consumerLoanPrivateSchema = basePersonalInfoSchema.extend({
  organizationName: z.string().min(1, "Organization name is required"),
  remitaActivation: z.string().min(1, "Remita activation is required"),
});

export const smeIndividualSchema = basePersonalInfoSchema.extend({
  organizationName: z.string().min(1, "Organization name is required"),
  remitaActivation: z.string().min(1, "Remita activation is required"),
});

export const smeCorporateSchema = basePersonalInfoSchema.extend({
  businessName: z.string().min(1, "Business name is required"),
  registrationNumber: z.string().min(1, "Registration number is required"),
});

// Default schema for backward compatibility
export const personalInfoSchema = consumerLoanPublicSchema;

export const nextOfKinSchema = z.object({
  nokFirstName: z.string().min(1, "Next of kin first name is required"),
  nokMiddleName: z.string().optional(),
  nokLastName: z.string().min(1, "Next of kin last name is required"),
  nokEmail: z.string().email("Invalid email format").optional(),
  nokRelationship: z.string().min(1, "Relationship is required"),
  nokPhoneNumber: z.string().min(10, "Phone number must be at least 10 digits"),
  nokStreet: z.string().min(1, "Street address is required"),
  nokCity: z.string().min(1, "City is required"),
  nokState: z.string().min(1, "State is required"),
  nokPostalCode: z.string().optional(),
});

export const loanInfoSchema = z.object({
  requestedAmount: z.number().positive("Requested amount must be positive"),
  loanTenor: z.number().int().positive("Loan tenor must be a positive integer"),
  repaymentMode: z.nativeEnum(RepaymentMode),
  grossPay: z.number().positive("Gross pay must be positive"),
  netPay: z.number().positive("Net pay must be positive"),
  purposeOfLoan: z.string().min(1, "Purpose of loan is required"),
});

export const disbursementSchema = z.object({
  accountName: z.string().min(1, "Account name is required"),
  accountNumber: z
    .string()
    .min(10, "Account number must be at least 10 digits"),
  bankName: z.string().min(1, "Bank name is required"),
});

export const approvalActionSchema = z.object({
  action: z.enum([
    "APPROVED",
    "REJECTED",
    "SENT_BACK",
    "DISBURSED",
    "SUBMITTED",
  ]),
  comments: z.string().optional(),
});

// Query Schemas
export const paginationSchema = z.object({
  page: z
    .string()
    .transform(Number)
    .pipe(z.number().int().positive())
    .optional(),
  limit: z
    .string()
    .transform(Number)
    .pipe(z.number().int().positive().max(100))
    .optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export const transactionFiltersSchema = z.object({
  status: z.string().optional(),
  stage: z.string().optional(),
  createdById: z.string().optional(),
  dateFrom: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
  dateTo: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
  search: z.string().optional(),
});

export const myTransactionFiltersSchema = z.object({
  status: z.string().optional(),
  dateFrom: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
  dateTo: z
    .string()
    .transform((str) => new Date(str))
    .optional(),
  search: z.string().optional(),
  accountOfficerName: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

// Helper function to get the appropriate personal info schema based on loan type
export function getPersonalInfoSchemaForLoanType(loanType: LoanType) {
  switch (loanType) {
    case LoanType.CONSUMER_LOAN_PUBLIC:
      return consumerLoanPublicSchema;
    case LoanType.CONSUMER_LOAN_PRIVATE:
      return consumerLoanPrivateSchema;
    case LoanType.SME_INDIVIDUAL:
      return smeIndividualSchema;
    case LoanType.SME_CORPORATE:
      return smeCorporateSchema;
    default:
      return personalInfoSchema; // Default fallback
  }
}

// Document Upload Schemas
export const documentUploadMetadataSchema = z.object({
  metadata: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val) return true; // Optional field
        try {
          // Try to parse as JSON to validate structure
          JSON.parse(val);
          return true;
        } catch {
          // If not valid JSON, treat as plain string (also valid)
          return val.length <= 1000; // Limit string length
        }
      },
      {
        message:
          "Metadata must be a valid JSON string or plain text (max 1000 characters)",
      }
    ),
});

// Pending Approvals Filter Schema
export const pendingApprovalsFiltersSchema = z.object({
  accountOfficerId: z.string().optional(),
  accountOfficerIds: z
    .union([
      z.string().transform((val) => val.split(",").map((id) => id.trim())),
      z.array(z.string()),
    ])
    .optional(),
  loanType: z
    .enum([
      "CONSUMER_LOAN_PUBLIC",
      "CONSUMER_LOAN_PRIVATE",
      "SME_INDIVIDUAL",
      "SME_CORPORATE",
    ])
    .optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  search: z.string().optional(),
  export: z
    .union([z.string(), z.boolean()])
    .transform((val) => {
      if (typeof val === "string") {
        return val.toLowerCase() === "true";
      }
      return val === true;
    })
    .optional(),
});

// Transaction Ownership Transfer Schemas
export const transferTransactionOwnershipSchema = z.object({
  newOwnerId: z.string().min(1, "New owner ID is required"),
  transferReason: z.string().optional(),
});

export const bulkTransferTransactionsSchema = z.object({
  fromUserId: z.string().min(1, "From user ID is required"),
  toUserId: z.string().min(1, "To user ID is required"),
  transferReason: z.string().optional(),
});

export const transferableTransactionsQuerySchema = z.object({
  userId: z.string().optional(),
});
