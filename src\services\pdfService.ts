import PDFDocument from "pdfkit";
import { LoanType } from "@prisma/client";
import { LOAN_TYPE_CONFIGS } from "../types/loanTypes";
import axios from "axios";

export class PDFService {
  private static readonly LOGO_URL =
    "https://jy0s2swu0k.ufs.sh/f/LKSiq2B5GRS8Y1Ge3lQJkuxwrQE08LAg471DXC56PpqsycGj";

  /**
   * Download logo from URL with error handling
   */
  private static async downloadLogo(): Promise<Buffer | null> {
    try {
      console.log("📥 Downloading AMS logo from URL...");
      const response = await axios({
        method: "GET",
        url: this.LOGO_URL,
        responseType: "arraybuffer",
        timeout: 10000, // 10 second timeout
        maxContentLength: 5 * 1024 * 1024, // 5MB max
      });

      console.log("✅ Logo downloaded successfully");
      return Buffer.from(response.data);
    } catch (error) {
      console.error("❌ Failed to download logo:", error);
      return null;
    }
  }

  /**
   * Format Nigerian Naira currency with NGN prefix for better compatibility
   */
  private static formatCurrency(amount: number): string {
    const formatted = new Intl.NumberFormat("en-NG", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
    return `NGN ${formatted}`;
  }

  /**
   * Format date in a professional manner
   */
  private static formatDate(date: Date | string): string {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  static async generateTransactionPDF(transaction: any): Promise<Buffer> {
    return new Promise(async (resolve, reject) => {
      try {
        // Download logo first
        const logoBuffer = await this.downloadLogo();

        const doc = new PDFDocument({
          margin: 40,
          size: "A4",
          info: {
            Title: `AMS Loan Application - ${transaction.transactionId}`,
            Author: "AMS Loan Management System",
            Subject: "Loan Application Document",
            Creator: "AMS System",
          },
        });
        const buffers: Buffer[] = [];

        doc.on("data", buffers.push.bind(buffers));
        doc.on("end", () => {
          const pdfBuffer = Buffer.concat(buffers);
          resolve(pdfBuffer);
        });

        // Create professional header with logo
        await this.createProfessionalHeader(doc, logoBuffer, transaction);

        // Add customer information section
        this.addCustomerInformation(doc, transaction);

        // Add loan information section
        this.addLoanInformation(doc, transaction);

        // Add employment information section
        this.addEmploymentInformation(doc, transaction);

        // Add approval history section
        this.addApprovalHistory(doc, transaction);

        // Add footer
        this.addProfessionalFooter(doc);

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Create professional header with logo and company information
   */
  private static async createProfessionalHeader(
    doc: InstanceType<typeof PDFDocument>,
    logoBuffer: Buffer | null,
    transaction: any
  ): Promise<void> {
    const pageWidth = doc.page.width;
    const margin = doc.page.margins.left;

    // Add logo if available
    if (logoBuffer) {
      try {
        doc.image(logoBuffer, margin, 40, {
          width: 80,
          height: 80,
          fit: [80, 80],
        });
      } catch (error) {
        console.error("Error adding logo to PDF:", error);
      }
    }

    // Company information (right side of header)
    const companyInfoX = pageWidth - margin - 200;
    doc
      .fontSize(10)
      .fillColor("#666666")
      .text("AMS Loan Management System", companyInfoX, 45, {
        width: 200,
        align: "right",
      })
      .text("Transaction ID: " + transaction.transactionId, companyInfoX, 60, {
        width: 200,
        align: "right",
      })
      .text("Generated: " + this.formatDate(new Date()), companyInfoX, 75, {
        width: 200,
        align: "right",
      })
      .text("Status: " + transaction.status, companyInfoX, 90, {
        width: 200,
        align: "right",
      });

    // Main title
    doc
      .fontSize(18)
      .fillColor("#E67E22") // AMS orange color
      .text("Consumer Public Loan", margin, 120, {
        width: pageWidth - 2 * margin,
        align: "left",
      });

    // Customer name
    const customerName = `${transaction.firstName || ""} ${
      transaction.middleName || ""
    } ${transaction.lastName || ""}`.trim();
    doc
      .fontSize(16)
      .fillColor("#2C3E50") // Dark blue-gray
      .text(customerName, margin, 145, {
        width: pageWidth - 2 * margin,
        align: "left",
      });

    // Add a line separator
    doc
      .moveTo(margin, 175)
      .lineTo(pageWidth - margin, 175)
      .strokeColor("#E67E22")
      .lineWidth(2)
      .stroke();

    doc.y = 190; // Set position for next content
  }

  /**
   * Add customer personal information section
   */
  private static addCustomerInformation(
    doc: InstanceType<typeof PDFDocument>,
    transaction: any
  ): void {
    const margin = doc.page.margins.left;
    const pageWidth = doc.page.width;

    // Section title
    doc
      .fontSize(14)
      .fillColor("#2C3E50")
      .text("Personal Information", margin, doc.y, { underline: true });

    doc.moveDown(0.5);

    // Personal details in two columns
    const leftColumnX = margin;
    const rightColumnX = pageWidth / 2;
    const startY = doc.y;

    doc.fontSize(10).fillColor("#333333");

    // Left column
    doc.text(
      `Full Name: ${transaction.firstName || ""} ${
        transaction.middleName || ""
      } ${transaction.lastName || ""}`,
      leftColumnX,
      startY
    );
    doc.text(
      `Gender: ${transaction.gender || "N/A"}`,
      leftColumnX,
      startY + 15
    );
    doc.text(
      `Marital Status: ${transaction.maritalStatus || "N/A"}`,
      leftColumnX,
      startY + 30
    );
    doc.text(`Email: ${transaction.email || "N/A"}`, leftColumnX, startY + 45);
    doc.text(
      `Phone: ${transaction.phoneNumber || "N/A"}`,
      leftColumnX,
      startY + 60
    );
    doc.text(`BVN: ${transaction.bvn || "N/A"}`, leftColumnX, startY + 75);
    doc.text(`NIN: ${transaction.nin || "N/A"}`, leftColumnX, startY + 90);

    // Right column
    doc.text(
      `Date of Birth: ${
        transaction.dateOfBirth
          ? this.formatDate(transaction.dateOfBirth)
          : "N/A"
      }`,
      rightColumnX,
      startY
    );
    doc.text(
      `State of Origin: ${transaction.stateOfOrigin || "N/A"}`,
      rightColumnX,
      startY + 15
    );
    doc.text(`LGA: ${transaction.lga || "N/A"}`, rightColumnX, startY + 30);
    doc.text(
      `Nationality: ${transaction.nationality || "N/A"}`,
      rightColumnX,
      startY + 45
    );

    // Address (full width) - adjusted spacing for new fields
    const addressY = Math.max(startY + 110, doc.y + 10);
    doc.text(
      `Address: ${transaction.street || ""} ${transaction.city || ""} ${
        transaction.state || ""
      }`.trim() || "N/A",
      leftColumnX,
      addressY,
      { width: pageWidth - 2 * margin }
    );

    doc.y = addressY + 25;
  }

  /**
   * Add loan information section
   */
  private static addLoanInformation(
    doc: InstanceType<typeof PDFDocument>,
    transaction: any
  ): void {
    const margin = doc.page.margins.left;
    const pageWidth = doc.page.width;

    doc.moveDown(1);

    // Section title
    doc
      .fontSize(14)
      .fillColor("#2C3E50")
      .text("Loan Information", margin, doc.y, { underline: true });

    doc.moveDown(0.5);

    // Loan details in two columns
    const leftColumnX = margin;
    const rightColumnX = pageWidth / 2;
    const startY = doc.y;

    doc.fontSize(10).fillColor("#333333");

    // Get loan type configuration
    const loanTypeConfig = transaction.loanType
      ? LOAN_TYPE_CONFIGS[
          transaction.loanType as keyof typeof LOAN_TYPE_CONFIGS
        ]
      : null;

    // Left column
    doc.text(
      `Loan Type: ${loanTypeConfig?.name || transaction.loanType || "N/A"}`,
      leftColumnX,
      startY
    );
    doc.text(
      `Requested Amount: ${
        transaction.requestedAmount
          ? this.formatCurrency(transaction.requestedAmount)
          : "N/A"
      }`,
      leftColumnX,
      startY + 15
    );
    doc.text(
      `Loan Tenor: ${transaction.loanTenor || "N/A"} months`,
      leftColumnX,
      startY + 30
    );
    doc.text(
      `Repayment Mode: ${transaction.repaymentMode || "N/A"}`,
      leftColumnX,
      startY + 45
    );

    // Right column
    doc.text(
      `Gross Pay: ${
        transaction.grossPay ? this.formatCurrency(transaction.grossPay) : "N/A"
      }`,
      rightColumnX,
      startY
    );
    doc.text(
      `Net Pay: ${
        transaction.netPay ? this.formatCurrency(transaction.netPay) : "N/A"
      }`,
      rightColumnX,
      startY + 15
    );
    doc.text(
      `Purpose: ${transaction.purposeOfLoan || "N/A"}`,
      rightColumnX,
      startY + 30,
      { width: pageWidth / 2 - margin }
    );

    // Bank information (full width)
    doc.moveDown(4);
    doc
      .fontSize(12)
      .fillColor("#2C3E50")
      .text("Bank Information", margin, doc.y, { underline: true });

    doc.moveDown(0.5);
    doc.fontSize(10).fillColor("#333333");

    const bankStartY = doc.y;
    doc.text(
      `Account Name: ${transaction.accountName || "N/A"}`,
      leftColumnX,
      bankStartY
    );
    doc.text(
      `Account Number: ${transaction.accountNumber || "N/A"}`,
      rightColumnX,
      bankStartY
    );
    doc.text(
      `Bank Name: ${transaction.bankName || "N/A"}`,
      leftColumnX,
      bankStartY + 15
    );

    doc.y = bankStartY + 40;
  }

  /**
   * Add employment information section
   */
  private static addEmploymentInformation(
    doc: InstanceType<typeof PDFDocument>,
    transaction: any
  ): void {
    const margin = doc.page.margins.left;
    const pageWidth = doc.page.width;

    doc.moveDown(1);

    // Section title
    doc
      .fontSize(14)
      .fillColor("#2C3E50")
      .text("Employment Information", margin, doc.y, { underline: true });

    doc.moveDown(0.5);

    const leftColumnX = margin;
    const rightColumnX = pageWidth / 2;
    const startY = doc.y;

    doc.fontSize(10).fillColor("#333333");

    // Handle different loan types
    if (transaction.loanType === LoanType.SME_CORPORATE) {
      // Business information for SME/Corporate loans
      doc.text(
        `Business Name: ${transaction.businessName || "N/A"}`,
        leftColumnX,
        startY
      );
      doc.text(
        `Registration Number: ${transaction.registrationNumber || "N/A"}`,
        rightColumnX,
        startY
      );
      doc.text(`Business Type: SME/Corporate`, leftColumnX, startY + 15);
    } else {
      // Employment information for individual loans
      doc.text(
        `Organization: ${transaction.organizationName || "N/A"}`,
        leftColumnX,
        startY
      );

      if (transaction.loanType === LoanType.CONSUMER_LOAN_PUBLIC) {
        doc.text(
          `IPPIS Number: ${transaction.ippisNumber || "N/A"}`,
          rightColumnX,
          startY
        );
      } else {
        doc.text(
          `Remita Activation: ${transaction.remitaActivation || "N/A"}`,
          rightColumnX,
          startY
        );
      }

      doc.text(
        `Employment Date: ${
          transaction.employmentDate
            ? this.formatDate(transaction.employmentDate)
            : "N/A"
        }`,
        leftColumnX,
        startY + 15
      );
      doc.text(
        `Job Title: ${transaction.jobTitle || "N/A"}`,
        rightColumnX,
        startY + 15
      );
    }

    doc.y = startY + 45;
  }

  /**
   * Add approval history section
   */
  private static addApprovalHistory(
    doc: InstanceType<typeof PDFDocument>,
    transaction: any
  ): void {
    const margin = doc.page.margins.left;
    const pageWidth = doc.page.width;

    // Check if we need a new page
    if (doc.y > doc.page.height - 200) {
      doc.addPage();
      doc.y = 50;
    }

    doc.moveDown(1);

    // Section title
    doc
      .fontSize(14)
      .fillColor("#2C3E50")
      .text("Approval History", margin, doc.y, { underline: true });

    doc.moveDown(0.5);

    if (transaction.approvals && transaction.approvals.length > 0) {
      doc.fontSize(10).fillColor("#333333");

      transaction.approvals.forEach((approval: any, index: number) => {
        const startY = doc.y;

        // Stage and action
        doc
          .fillColor("#E67E22")
          .text(`${approval.stage.replace(/_/g, " ")}: `, margin, startY, {
            continued: true,
          })
          .fillColor("#27AE60")
          .text(`${approval.action}`, { continued: true })
          .fillColor("#333333")
          .text(
            ` by ${approval.approver?.firstName || "Unknown"} ${
              approval.approver?.lastName || "User"
            }`
          );

        // Date
        doc.text(
          `Date: ${this.formatDate(approval.createdAt)}`,
          margin,
          startY + 12
        );

        // Comments if available
        if (approval.comments) {
          doc.text(`Comments: ${approval.comments}`, margin, startY + 24, {
            width: pageWidth - 2 * margin,
            indent: 10,
          });
          doc.moveDown(0.3);
        }

        doc.moveDown(0.5);

        // Check if we need a new page
        if (
          doc.y > doc.page.height - 100 &&
          index < transaction.approvals.length - 1
        ) {
          doc.addPage();
          doc.y = 50;
        }
      });
    } else {
      doc
        .fontSize(10)
        .fillColor("#666666")
        .text("No approval history available.", margin, doc.y);
      doc.moveDown(1);
    }
  }

  /**
   * Add professional footer
   */
  private static addProfessionalFooter(
    doc: InstanceType<typeof PDFDocument>
  ): void {
    const margin = doc.page.margins.left;
    const pageWidth = doc.page.width;
    const pageHeight = doc.page.height;

    // Position footer at bottom of page
    const footerY = pageHeight - 60;

    // Add separator line
    doc
      .moveTo(margin, footerY - 10)
      .lineTo(pageWidth - margin, footerY - 10)
      .strokeColor("#E67E22")
      .lineWidth(1)
      .stroke();

    // Footer text
    doc
      .fontSize(8)
      .fillColor("#666666")
      .text(
        `Generated on ${this.formatDate(
          new Date()
        )} at ${new Date().toLocaleTimeString("en-GB")}`,
        margin,
        footerY,
        {
          width: pageWidth - 2 * margin,
          align: "center",
        }
      );

    // Page number (if multiple pages)
    const pageCount = (doc as any).bufferedPageRange().count;
    if (pageCount > 1) {
      doc.text(
        `Page ${(doc as any).bufferedPageRange().start + 1} of ${pageCount}`,
        margin,
        footerY + 12,
        {
          width: pageWidth - 2 * margin,
          align: "center",
        }
      );
    }
  }
}
