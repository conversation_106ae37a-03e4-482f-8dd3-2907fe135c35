import { v2 as cloudinary } from "cloudinary";
import { Readable } from "stream";

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  original_filename: string;
  bytes: number;
  format: string;
  resource_type: string;
}

export class CloudinaryService {
  /**
   * Upload file buffer to Cloudinary
   */
  static async uploadFile(
    fileBuffer: Buffer,
    originalName: string,
    mimeType: string,
    transactionId: string
  ): Promise<CloudinaryUploadResult> {
    try {
      // Create a readable stream from buffer
      const stream = Readable.from(fileBuffer);

      // Determine resource type based on mime type
      const resourceType = mimeType.startsWith("image/") ? "image" : "raw";

      // Generate a unique public_id with transaction context
      const timestamp = Date.now();
      const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, "_");
      const publicId = `ams-loan-docs/${transactionId}/${timestamp}_${sanitizedName}`;

      return new Promise((resolve, reject) => {
        const uploadStream = cloudinary.uploader.upload_stream(
          {
            resource_type: resourceType,
            public_id: publicId,
            folder: "ams-loan-documents",
            use_filename: true,
            unique_filename: true,
            overwrite: false,
            type: "upload", // Explicitly set type to upload (public)
            access_mode: "public", // Ensure public access
            // Add tags for better organization
            tags: ["ams-loan", "transaction-document", transactionId],
            // Set context for metadata
            context: {
              transaction_id: transactionId,
              original_name: originalName,
              upload_date: new Date().toISOString(),
            },
          },
          (error, result) => {
            if (error) {
              console.error("Cloudinary upload error:", error);
              reject(
                new Error(
                  `Failed to upload file to Cloudinary: ${error.message}`
                )
              );
            } else if (result) {
              resolve({
                public_id: result.public_id,
                secure_url: result.secure_url,
                original_filename: result.original_filename || originalName,
                bytes: result.bytes,
                format: result.format,
                resource_type: result.resource_type,
              });
            } else {
              reject(new Error("Unknown error occurred during upload"));
            }
          }
        );

        // Pipe the stream to Cloudinary
        stream.pipe(uploadStream);
      });
    } catch (error) {
      console.error("Error uploading to Cloudinary:", error);
      throw new Error(
        `Failed to upload file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Delete file from Cloudinary
   */
  static async deleteFile(publicId: string): Promise<void> {
    try {
      const result = await cloudinary.uploader.destroy(publicId);

      if (result.result !== "ok") {
        console.warn(
          `Failed to delete file from Cloudinary: ${publicId}`,
          result
        );
        // Don't throw error for deletion failures as the file might already be deleted
      } else {
        console.log(`Successfully deleted file from Cloudinary: ${publicId}`);
      }
    } catch (error) {
      console.error("Error deleting from Cloudinary:", error);
      // Don't throw error for deletion failures
    }
  }

  /**
   * Get optimized URL for file display
   */
  static getOptimizedUrl(
    publicId: string,
    options: {
      width?: number;
      height?: number;
      quality?: string;
      format?: string;
    } = {}
  ): string {
    try {
      return cloudinary.url(publicId, {
        secure: true,
        ...options,
      });
    } catch (error) {
      console.error("Error generating optimized URL:", error);
      // Return the public_id as fallback (though this shouldn't happen)
      return publicId;
    }
  }

  /**
   * Get file info from Cloudinary
   */
  static async getFileInfo(publicId: string): Promise<any> {
    try {
      const result = await cloudinary.api.resource(publicId);
      return result;
    } catch (error) {
      console.error("Error getting file info from Cloudinary:", error);
      throw new Error(
        `Failed to get file info: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Generate a signed URL for secure file access
   */
  static generateSignedUrl(publicId: string, expiresIn: number = 3600): string {
    try {
      // For now, just return a regular secure URL since authentication is complex
      return cloudinary.url(publicId, { secure: true });
    } catch (error) {
      console.error("Error generating signed URL:", error);
      // Fallback to regular secure URL
      return cloudinary.url(publicId, { secure: true });
    }
  }

  /**
   * Make a file publicly accessible by updating its access mode
   */
  static async makeFilePublic(publicId: string): Promise<string> {
    try {
      // Update the asset to make it publicly accessible
      const result = await cloudinary.api.update(publicId, {
        access_mode: "public",
        type: "upload",
      });

      return result.secure_url;
    } catch (error) {
      console.error(`Error making file public ${publicId}:`, error);
      throw new Error(
        `Failed to make file public: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get the correct download URL for a file, making it public if needed
   */
  static async getDownloadableUrl(publicId: string): Promise<string> {
    try {
      // First try to get file info to check current access mode
      const fileInfo = await cloudinary.api.resource(publicId);

      // If it's already public, return the secure URL
      if (fileInfo.access_mode === "public") {
        return fileInfo.secure_url;
      }

      // If it's not public, make it public and return the new URL
      return await this.makeFilePublic(publicId);
    } catch (error) {
      console.error(`Error getting downloadable URL for ${publicId}:`, error);

      // If we can't get file info, try to make it public anyway
      try {
        return await this.makeFilePublic(publicId);
      } catch (makePublicError) {
        console.error(
          `Failed to make file public as fallback:`,
          makePublicError
        );
        // Final fallback - return a constructed URL
        return cloudinary.url(publicId, { secure: true });
      }
    }
  }

  /**
   * Validate Cloudinary configuration
   */
  static validateConfig(): boolean {
    const requiredEnvVars = [
      "CLOUDINARY_CLOUD_NAME",
      "CLOUDINARY_API_KEY",
      "CLOUDINARY_API_SECRET",
    ];

    const missingVars = requiredEnvVars.filter(
      (varName) => !process.env[varName]
    );

    if (missingVars.length > 0) {
      console.error("Missing Cloudinary environment variables:", missingVars);
      return false;
    }

    return true;
  }

  /**
   * Get storage usage statistics
   */
  static async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    byResourceType: Record<string, number>;
  }> {
    try {
      const result = await cloudinary.api.resources({
        type: "upload",
        prefix: "ams-loan-documents/",
        max_results: 500, // Adjust as needed
      });

      const stats = {
        totalFiles: result.resources.length,
        totalSize: result.resources.reduce(
          (sum: number, resource: any) => sum + resource.bytes,
          0
        ),
        byResourceType: {} as Record<string, number>,
      };

      result.resources.forEach((resource: any) => {
        const type = resource.resource_type;
        stats.byResourceType[type] = (stats.byResourceType[type] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error("Error getting storage stats:", error);
      throw new Error(
        `Failed to get storage stats: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }
}

// Validate configuration on module load
if (!CloudinaryService.validateConfig()) {
  console.warn(
    "⚠️  Cloudinary is not properly configured. File uploads will fail."
  );
}

export default CloudinaryService;
