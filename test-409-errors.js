#!/usr/bin/env node

/**
 * Test script to diagnose and test 409 conflict errors
 * This script helps identify common causes of 409 errors in the AMS system
 */

const http = require('http');

const BASE_URL = process.env.API_URL || 'http://localhost:8000';
const API_BASE = `${BASE_URL}/api/v1`;

// Test scenarios that commonly cause 409 errors
const testScenarios = [
  {
    name: "Check API Health",
    method: "GET",
    path: "/health",
    description: "Verify API is running and healthy"
  },
  {
    name: "Check Performance Metrics",
    method: "GET", 
    path: "/performance",
    description: "Check if performance monitoring is working"
  }
];

console.log('🔍 Testing 409 Conflict Error Scenarios...\n');
console.log(`Base URL: ${BASE_URL}`);
console.log(`API Base: ${API_BASE}\n`);

async function makeRequest(scenario) {
  return new Promise((resolve) => {
    const url = new URL(scenario.path, API_BASE);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 8000),
      path: url.pathname + url.search,
      method: scenario.method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AMS-409-Test/1.0',
        ...(scenario.headers || {})
      },
      timeout: 10000
    };

    const protocol = url.protocol === 'https:' ? require('https') : require('http');
    
    const req = protocol.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            scenario: scenario.name,
            status: res.statusCode,
            success: res.statusCode >= 200 && res.statusCode < 400,
            data: jsonData,
            headers: res.headers,
            rawData: data
          });
        } catch (error) {
          resolve({
            scenario: scenario.name,
            status: res.statusCode,
            success: res.statusCode >= 200 && res.statusCode < 400,
            data: null,
            error: 'Invalid JSON response',
            rawData: data.substring(0, 200) + (data.length > 200 ? '...' : '')
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        scenario: scenario.name,
        status: 'ERROR',
        success: false,
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        scenario: scenario.name,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timed out'
      });
    });

    if (scenario.body) {
      req.write(JSON.stringify(scenario.body));
    }
    
    req.end();
  });
}

async function runTests() {
  console.log('Scenario'.padEnd(30) + ' | Status | Result | Description');
  console.log('-'.repeat(80));
  
  const results = [];
  
  for (const scenario of testScenarios) {
    const result = await makeRequest(scenario);
    results.push(result);
    
    const statusColor = result.status === 409 ? '🔴' : 
                       result.success ? '🟢' : '🟡';
    const statusText = result.status.toString().padEnd(6);
    const resultText = result.success ? 'PASS' : 
                      result.status === 409 ? '409!' : 'FAIL';
    
    console.log(
      `${scenario.name.padEnd(30)} | ${statusText} | ${statusColor} ${resultText.padEnd(4)} | ${scenario.description}`
    );
    
    // If we get a 409, show detailed information
    if (result.status === 409) {
      console.log(`   🔍 409 Details:`);
      console.log(`      Message: ${result.data?.message || 'No message'}`);
      console.log(`      Error: ${result.data?.error || 'No error details'}`);
      if (result.data?.data) {
        console.log(`      Data: ${JSON.stringify(result.data.data, null, 8)}`);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 Test Summary:');
  console.log('-'.repeat(40));
  
  const successful = results.filter(r => r.success).length;
  const conflicts = results.filter(r => r.status === 409).length;
  const failed = results.filter(r => !r.success && r.status !== 409).length;
  
  console.log(`✅ Successful: ${successful}`);
  console.log(`🔴 409 Conflicts: ${conflicts}`);
  console.log(`❌ Other Failures: ${failed}`);
  console.log(`📈 Success Rate: ${((successful / results.length) * 100).toFixed(1)}%`);
  
  if (conflicts > 0) {
    console.log('\n🔴 409 Conflict Analysis:');
    console.log('-'.repeat(40));
    results.filter(r => r.status === 409).forEach(r => {
      console.log(`❌ ${r.scenario}:`);
      console.log(`   Message: ${r.data?.message || 'No message'}`);
      console.log(`   Likely Cause: Transaction state conflict or duplicate action`);
      console.log(`   Suggestions:`);
      console.log(`   - Refresh the page and try again`);
      console.log(`   - Check if another user has already processed this item`);
      console.log(`   - Verify the transaction is in the correct state`);
      console.log('');
    });
  }
  
  if (failed > 0) {
    console.log('\n❌ Other Failures:');
    console.log('-'.repeat(40));
    results.filter(r => !r.success && r.status !== 409).forEach(r => {
      console.log(`❌ ${r.scenario}: ${r.error || r.status}`);
    });
  }
  
  console.log('\n🔗 Useful Links:');
  console.log(`   📚 API Docs: ${BASE_URL}/api-docs`);
  console.log(`   🏥 Health Check: ${API_BASE}/health`);
  console.log(`   📊 Performance: ${API_BASE}/performance`);
  
  console.log('\n💡 Common 409 Error Causes:');
  console.log('   1. Duplicate approval attempts (same user acting twice)');
  console.log('   2. Multiple users trying to approve the same transaction');
  console.log('   3. Transaction in wrong status for the attempted action');
  console.log('   4. Workflow stage mismatch (user role vs transaction stage)');
  console.log('   5. Duplicate BVN in loan applications');
  
  console.log('\n🛠️  Debugging Tips:');
  console.log('   1. Check server logs for detailed error information');
  console.log('   2. Verify user permissions and role assignments');
  console.log('   3. Confirm transaction status and current approval stage');
  console.log('   4. Check for concurrent user actions on the same transaction');
  console.log('   5. Validate BVN uniqueness in loan applications');
  
  process.exit(conflicts > 0 ? 1 : 0);
}

// Check if server is running first
console.log(`🔍 Checking server connectivity at ${BASE_URL}...`);

const serverCheck = http.request({
  hostname: new URL(BASE_URL).hostname,
  port: new URL(BASE_URL).port || 8000,
  path: '/',
  method: 'GET',
  timeout: 5000
}, (res) => {
  console.log(`✅ Server is responding (Status: ${res.statusCode})\n`);
  runTests();
});

serverCheck.on('error', (error) => {
  console.error(`❌ Cannot connect to server at ${BASE_URL}`);
  console.error(`   Error: ${error.message}`);
  console.error(`   Make sure the server is running with: npm run dev`);
  process.exit(1);
});

serverCheck.on('timeout', () => {
  console.error(`❌ Server connection timed out at ${BASE_URL}`);
  console.error(`   Make sure the server is running with: npm run dev`);
  process.exit(1);
});

serverCheck.end();
