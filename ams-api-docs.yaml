openapi: 3.0.0
info:
  title: AMS Loan Management System API
  description: |
    Complete API documentation for AMS Loan Management System with authentication, 
    transaction management, loan type selection, approval workflows, and document management.

    ## 🚀 Features
    - **Loan Type Selection System**: 4 loan types with dynamic form fields
    - **Multi-step Transaction Creation**: 6 steps including loan type selection
    - **Transaction Preview & PDF Generation**: Professional document generation
    - **Role-based Approval Workflow**: Multi-level approval system
    - **Document Management**: Upload and manage transaction documents
    - **Dashboard & Analytics**: Comprehensive reporting and metrics

    ## 🔐 Authentication
    All protected endpoints require a Bearer token in the Authorization header:
    ```
    Authorization: Bearer <your_jwt_token>
    ```

    ## 💼 Loan Types & Dynamic Fields
    - **Consumer Loan Public**: organizationName, ippisNumber, employmentDate
    - **Consumer Loan Private**: organizationName, remitaActivation, employmentDate  
    - **SME Individual**: organizationName, remitaActivation, employmentDate
    - **SME Corporate**: businessName, registrationNumber, employmentDate

    ## 🔄 Transaction Workflow
    1. **Create Transaction** → Get transaction ID
    2. **Select Loan Type** → Choose from 4 options
    3. **Update Personal Info** → Use loan type-specific fields
    4. **Update Next of Kin** → Family contact information
    5. **Update Loan Info** → Amount, tenor, purpose
    6. **Update Disbursement** → Bank account details
    7. **Upload Documents** → Required supporting documents
    8. **Preview Transaction** → Review all details
    9. **Download PDF** → Get formatted document
    10. **Submit Transaction** → Start approval workflow

    ## 👥 User Roles
    - **ACCOUNT_OFFICER**: Create and manage transactions
    - **SUPERVISOR**: First level approval
    - **HEAD_CONSUMER_LENDING**: Second level approval
    - **HEAD_RISK_MANAGEMENT**: Risk assessment
    - **MANAGING_DIRECTOR**: Final approval for large amounts
    - **ACCOUNTANT**: Disbursement processing
    - **SUPER_ADMIN**: System administration
  version: 3.0.0
  contact:
    name: AMS Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8000/api
    description: Development server
  - url: https://api.ams-loan.com/api
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login endpoint

  schemas:
    # Common Response Schemas
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the request was successful
        message:
          type: string
          description: Human-readable message
        data:
          type: object
          description: Response data
      required:
        - success
        - message

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          description: Error message
        error:
          type: object
          description: Additional error details
      required:
        - success
        - message

    # Enum Schemas
    UserRole:
      type: string
      enum:
        - ACCOUNT_OFFICER
        - SUPERVISOR
        - HEAD_CONSUMER_LENDING
        - HEAD_RISK_MANAGEMENT
        - MANAGING_DIRECTOR
        - ACCOUNTANT
        - SUPER_ADMIN
      description: User roles in the system

    LoanType:
      type: string
      enum:
        - CONSUMER_LOAN_PUBLIC
        - CONSUMER_LOAN_PRIVATE
        - SME_INDIVIDUAL
        - SME_CORPORATE
      description: Available loan types

    TransactionStatus:
      type: string
      enum:
        - DRAFT
        - SUBMITTED
        - IN_PROGRESS
        - APPROVED
        - REJECTED
        - SENT_BACK
        - DISBURSED
        - COMPLETED
      description: Transaction status values

    ApprovalStage:
      type: string
      enum:
        - ACCOUNT_OFFICER
        - SUPERVISOR
        - HEAD_CONSUMER_LENDING
        - HEAD_RISK_MANAGEMENT
        - MANAGING_DIRECTOR
        - ACCOUNTANT
      description: Approval workflow stages

    Gender:
      type: string
      enum: [MALE, FEMALE]

    MaritalStatus:
      type: string
      enum: [SINGLE, MARRIED, DIVORCED, WIDOWED]

    RepaymentMode:
      type: string
      enum: [MONTHLY, QUARTERLY, SEMI_ANNUALLY, ANNUALLY]

    FileType:
      type: string
      enum: [PDF, IMAGE, DOCUMENT]

    # Authentication Schemas
    LoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User email address
        password:
          type: string
          minLength: 8
          description: User password
      required:
        - email
        - password
      example:
        email: "<EMAIL>"
        password: "password123"

    OTPVerificationRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User email address
        otp:
          type: string
          pattern: "^[0-9]{6}$"
          description: 6-digit OTP code
      required:
        - email
        - otp
      example:
        email: "<EMAIL>"
        otp: "123456"

    RefreshTokenRequest:
      type: object
      properties:
        refreshToken:
          type: string
          description: Valid refresh token
      required:
        - refreshToken

    AuthResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        data:
          type: object
          properties:
            accessToken:
              type: string
              description: JWT access token
            refreshToken:
              type: string
              description: JWT refresh token
            user:
              $ref: "#/components/schemas/User"

    # User Schemas
    User:
      type: object
      properties:
        id:
          type: string
          description: User unique identifier
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        role:
          $ref: "#/components/schemas/UserRole"
        isActive:
          type: boolean
        monthlyTarget:
          type: number
          description: Monthly target for account officers
        lastLogin:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
        firstName:
          type: string
        lastName:
          type: string
        role:
          $ref: "#/components/schemas/UserRole"
        monthlyTarget:
          type: number
          minimum: 0
      required:
        - email
        - password
        - firstName
        - lastName
        - role

    # Loan Type Schemas
    LoanTypeConfig:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/LoanType"
        name:
          type: string
          description: Human-readable loan type name
        description:
          type: string
          description: Loan type description
        requiredFields:
          type: array
          items:
            type: string
          description: List of required fields for this loan type
      example:
        id: "CONSUMER_LOAN_PUBLIC"
        name: "Consumer Loan Public"
        description: "Loan for public sector employees"
        requiredFields: ["organizationName", "ippisNumber", "employmentDate"]

    LoanTypeSelectionRequest:
      type: object
      properties:
        loanType:
          $ref: "#/components/schemas/LoanType"
      required:
        - loanType
      example:
        loanType: "CONSUMER_LOAN_PUBLIC"

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User authentication and authorization endpoints
  - name: Users
    description: User management operations (Admin only)
  - name: Transactions
    description: Transaction creation and management
  - name: Loan Types
    description: Loan type selection and configuration
  - name: Approvals
    description: Transaction approval workflow
  - name: Documents
    description: Document upload and management
  - name: Dashboard
    description: Analytics and reporting dashboards
  - name: Notifications
    description: System notifications management

paths:
  # 🔐 Authentication Endpoints
  /auth/login:
    post:
      tags: [Authentication]
      summary: User login
      description: |
        Authenticate user with email and password.
        Returns success message and triggers OTP email.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoginRequest"
      responses:
        "200":
          description: Login successful, OTP sent to email
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApiResponse"
              example:
                success: true
                message: "OTP sent to your email"
                data: {}
        "400":
          description: Invalid credentials
        "429":
          description: Too many login attempts

  /auth/verify-otp:
    post:
      tags: [Authentication]
      summary: Verify OTP and get tokens
      description: |
        Verify the 6-digit OTP code sent to email.
        Returns access token, refresh token, and user information.
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OTPVerificationRequest"
      responses:
        "200":
          description: OTP verified successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthResponse"
        "400":
          description: Invalid or expired OTP

  /auth/refresh-token:
    post:
      tags: [Authentication]
      summary: Refresh access token
      description: Get new access token using valid refresh token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenRequest"
      responses:
        "200":
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthResponse"
        "401":
          description: Invalid refresh token

  /auth/profile:
    get:
      tags: [Authentication]
      summary: Get user profile
      description: Get current authenticated user's profile information
      responses:
        "200":
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/User"

  # 💼 Loan Types Endpoints
  /transactions/loan-types:
    get:
      tags: [Loan Types]
      summary: Get available loan types
      description: |
        Retrieve all available loan types with their configurations.
        Shows required fields for each loan type.
      responses:
        "200":
          description: Loan types retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ApiResponse"
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/LoanTypeConfig"
              example:
                success: true
                message: "Loan types retrieved successfully"
                data:
                  - id: "CONSUMER_LOAN_PUBLIC"
                    name: "Consumer Loan Public"
                    description: "Loan for public sector employees"
                    requiredFields:
                      ["organizationName", "ippisNumber", "employmentDate"]
                  - id: "CONSUMER_LOAN_PRIVATE"
                    name: "Consumer Loan Private"
                    description: "Loan for private sector employees"
                    requiredFields:
                      ["organizationName", "remitaActivation", "employmentDate"]
                  - id: "SME_INDIVIDUAL"
                    name: "SME Individual"
                    description: "Small and Medium Enterprise loan for individuals"
                    requiredFields:
                      ["organizationName", "remitaActivation", "employmentDate"]
                  - id: "SME_CORPORATE"
                    name: "SME Corporate"
                    description: "Small and Medium Enterprise loan for corporations"
                    requiredFields:
                      ["businessName", "registrationNumber", "employmentDate"]
