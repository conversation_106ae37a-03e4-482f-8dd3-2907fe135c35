import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";

// Import middleware
import { errorHandler, notFoundHandler } from "./middleware/errorHandler";
import { generalLimiter } from "./middleware/rateLimiter";

// Import routes
import apiRoutes from "./routes";
import { setupSwagger } from "./config/swagger";
import { R2Service } from "./services/r2Service";
import { DatabaseCleanupService } from "./services/databaseCleanupService";
import { NonBlockingCron } from "./services/nonBlockingCron";
import { PerformanceMonitoringService } from "./services/performanceMonitoringService";
import { CacheService } from "./services/cacheService";

// Load environment variables
dotenv.config();

// Initialize R2 Service if configured
try {
  R2Service.initialize();
  console.log("✅ R2 Service initialized successfully");
} catch (error) {
  console.warn(
    "⚠️  R2 Service not configured or failed to initialize:",
    error instanceof Error ? error.message : error
  );
  console.warn("File uploads and document access may not work properly");
}

// Initialize Database Cleanup Service
try {
  DatabaseCleanupService.initializeScheduledJobs();
  console.log("✅ Database cleanup jobs initialized successfully");
} catch (error) {
  console.error(
    "❌ Failed to initialize database cleanup jobs:",
    error instanceof Error ? error.message : error
  );
}

// Initialize performance monitoring
try {
  // Log cache and performance stats every 30 minutes
  setInterval(() => {
    CacheService.logCacheStats();
    PerformanceMonitoringService.logPerformanceSummary();
  }, 30 * 60 * 1000);

  console.log("✅ Performance monitoring initialized successfully");
} catch (error) {
  console.error(
    "❌ Failed to initialize performance monitoring:",
    error instanceof Error ? error.message : error
  );
}

const app = express();
const PORT = process.env.PORT || 8000;

app.set("trust proxy", 1);

// Security middleware
app.use(helmet());
app.use(
  cors({
    origin: [
      "https://ams-finance.vercel.app",
      "https://amslms.vercel.app",
      "https://ams-staging.vercel.app",
      "http://localhost:3000",
    ],
    credentials: true,
  })
);

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Performance monitoring with error handling
try {
  app.use(PerformanceMonitoringService.trackRequestPerformance());
  console.log("✅ Performance monitoring middleware enabled");
} catch (error) {
  console.error("❌ Failed to setup performance monitoring:", error);
}

// Rate limiting
app.use(generalLimiter);

// Body parsing middleware
app.use(express.json({ limit: "20mb" }));
app.use(express.urlencoded({ extended: true, limit: "20mb" }));

// Setup Swagger documentation with error handling
try {
  setupSwagger(app);
  console.log("✅ API documentation setup completed");
} catch (error) {
  console.error("❌ Failed to setup API documentation:", error);
  // Add fallback route for documentation
  app.get("/api-docs", (_req, res) => {
    res.status(503).json({
      success: false,
      message: "API documentation is temporarily unavailable",
      error: "Documentation setup failed",
      timestamp: new Date().toISOString(),
    });
  });
}

// API routes
app.use("/api/v1", apiRoutes);

// Root endpoint
app.get("/", (_req, res) => {
  res.json({
    success: true,
    message: "Welcome to AMS Loan Management System API",
    version: "1.0.0",
    documentation: "/api-docs",
    health: "/api/v1/health",
    performance: "/api/v1/performance",
  });
});

// Performance monitoring endpoint
app.get("/api/v1/performance", (_req, res) => {
  const health = PerformanceMonitoringService.getSystemHealth();
  res.json({
    success: true,
    data: health,
  });
});

// Health check endpoint
app.get("/api/v1/health", (_req, res) => {
  const health = PerformanceMonitoringService.getHealthCheck();
  res.status(health.status === "healthy" ? 200 : 503).json({
    success: health.status === "healthy",
    data: health,
  });
});

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 AMS Loan System API is running on http://localhost:${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);

  // Start non-blocking cron jobs
  console.log("🕐 Initializing non-blocking cron jobs...");
  try {
    NonBlockingCron.startAllJobs();
    console.log("✅ Non-blocking cron system initialized");
  } catch (error) {
    console.error("❌ Failed to initialize cron jobs:", error);
  }
});

// Graceful shutdown handling
const gracefulShutdown = async (signal: string) => {
  console.log(`🔄 ${signal} received, starting graceful shutdown...`);

  try {
    // Close server
    server.close(() => {
      console.log("✅ Server closed");
    });

    // Shutdown cron jobs
    await NonBlockingCron.gracefulShutdown();
    console.log("✅ Cron jobs stopped");

    process.exit(0);
  } catch (error) {
    console.error("❌ Error during graceful shutdown:", error);
    process.exit(1);
  }
};

process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// Handle uncaught exceptions
process.on("uncaughtException", async (error) => {
  console.error("❌ Uncaught Exception:", error);
  console.error("Stack trace:", error.stack);
  await gracefulShutdown("UNCAUGHT_EXCEPTION");
});

process.on("unhandledRejection", async (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  await gracefulShutdown("UNHANDLED_REJECTION");
});
