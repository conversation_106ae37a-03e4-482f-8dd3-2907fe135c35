import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";

// Import middleware
import { errorHandler, notFoundHandler } from "./middleware/errorHandler";
import { generalLimiter } from "./middleware/rateLimiter";

// Import routes
import apiRoutes from "./routes";
import { setupSwagger } from "./config/swagger";
import { R2Service } from "./services/r2Service";
import { DatabaseCleanupService } from "./services/databaseCleanupService";
import { NonBlockingCron } from "./services/nonBlockingCron";
import { PerformanceMonitoringService } from "./services/performanceMonitoringService";
import { CacheService } from "./services/cacheService";

// Load environment variables
dotenv.config();

// Initialize R2 Service if configured
try {
  R2Service.initialize();
  console.log("✅ R2 Service initialized successfully");
} catch (error) {
  console.warn(
    "⚠️  R2 Service not configured or failed to initialize:",
    error instanceof Error ? error.message : error
  );
  console.warn("File uploads and document access may not work properly");
}

// Initialize Database Cleanup Service
try {
  DatabaseCleanupService.initializeScheduledJobs();
  console.log("✅ Database cleanup jobs initialized successfully");
} catch (error) {
  console.error(
    "❌ Failed to initialize database cleanup jobs:",
    error instanceof Error ? error.message : error
  );
}

// Initialize performance monitoring
try {
  // Log cache and performance stats every 30 minutes
  setInterval(() => {
    CacheService.logCacheStats();
    PerformanceMonitoringService.logPerformanceSummary();
  }, 30 * 60 * 1000);

  console.log("✅ Performance monitoring initialized successfully");
} catch (error) {
  console.error(
    "❌ Failed to initialize performance monitoring:",
    error instanceof Error ? error.message : error
  );
}

const app = express();
const PORT = process.env.PORT || 8000;

app.set("trust proxy", 1);

// Security middleware
app.use(helmet());
app.use(
  cors({
    origin: [
      "https://ams-finance.vercel.app",
      "https://amslms.vercel.app",
      "https://ams-staging.vercel.app",
      "http://localhost:3000",
    ],
    credentials: true,
  })
);

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Performance monitoring
app.use(PerformanceMonitoringService.trackRequestPerformance());

// Rate limiting
app.use(generalLimiter);

// Body parsing middleware
app.use(express.json({ limit: "20mb" }));
app.use(express.urlencoded({ extended: true, limit: "20mb" }));

// Setup Swagger documentation
setupSwagger(app);

// API routes
app.use("/api/v1", apiRoutes);

// Root endpoint
app.get("/", (_req, res) => {
  res.json({
    success: true,
    message: "Welcome to AMS Loan Management System API",
    version: "1.0.0",
    documentation: "/api-docs",
    health: "/api/v1/health",
    performance: "/api/v1/performance",
  });
});

// Performance monitoring endpoint
app.get("/api/v1/performance", (_req, res) => {
  const health = PerformanceMonitoringService.getSystemHealth();
  res.json({
    success: true,
    data: health,
  });
});

// Health check endpoint
app.get("/api/v1/health", (_req, res) => {
  const health = PerformanceMonitoringService.getHealthCheck();
  res.status(health.status === "healthy" ? 200 : 503).json({
    success: health.status === "healthy",
    data: health,
  });
});

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AMS Loan System API is running on http://localhost:${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/v1/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);

  // Start non-blocking cron jobs
  console.log("🕐 Initializing non-blocking cron jobs...");
  NonBlockingCron.startAllJobs();
  console.log("✅ Non-blocking cron system initialized");
});

// Graceful shutdown handling
process.on("SIGTERM", async () => {
  console.log("🔄 SIGTERM received, starting graceful shutdown...");
  await NonBlockingCron.gracefulShutdown();
  process.exit(0);
});

process.on("SIGINT", async () => {
  console.log("🔄 SIGINT received, starting graceful shutdown...");
  await NonBlockingCron.gracefulShutdown();
  process.exit(0);
});

// Handle uncaught exceptions
process.on("uncaughtException", async (error) => {
  console.error("❌ Uncaught Exception:", error);
  await NonBlockingCron.gracefulShutdown();
  process.exit(1);
});

process.on("unhandledRejection", async (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  await NonBlockingCron.gracefulShutdown();
  process.exit(1);
});
