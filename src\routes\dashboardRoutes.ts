import { Router } from "express";
import { DashboardController } from "../controllers/dashboardController";
import {
  authenticate,
  requireAdminRole,
  requireApprovalRole,
} from "../middleware/auth";
import { validateQuery } from "../middleware/validation";
import {
  paginationSchema,
  myTransactionFiltersSchema,
} from "../utils/validation";

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/dashboard/stats:
 *   get:
 *     summary: Get user dashboard statistics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User dashboard statistics
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/stats", DashboardController.getUserDashboard);

/**
 * @swagger
 * /api/v1/dashboard/my-transactions:
 *   get:
 *     summary: Get user's transactions with filters
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [DRAFT, SUBMITTED, UNDER_REVIEW, APPROVED, REJECTED, SENT_BACK]
 *         description: Filter by transaction status
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter from date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter to date
 *     responses:
 *       200:
 *         description: User's transactions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/my-transactions",
  validateQuery(paginationSchema.merge(myTransactionFiltersSchema)),
  DashboardController.getMyTransactions
);

/**
 * @swagger
 * /api/v1/dashboard/my-transactions/stats:
 *   get:
 *     summary: Get user's transaction statistics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter from date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter to date
 *     responses:
 *       200:
 *         description: User's transaction statistics
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/my-transactions/stats",
  validateQuery(
    myTransactionFiltersSchema.pick({ dateFrom: true, dateTo: true })
  ),
  DashboardController.getMyTransactionStats
);

/**
 * @swagger
 * /api/v1/dashboard/account-officers:
 *   get:
 *     summary: Get account officers list
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of account officers
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/account-officers", DashboardController.getAccountOfficers);

/**
 * @swagger
 * /api/v1/dashboard/activities:
 *   get:
 *     summary: Get recent activities
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Recent activities
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/activities", DashboardController.getActivities);

/**
 * @swagger
 * /api/v1/dashboard/user-management:
 *   get:
 *     summary: Get user management data
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User management data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/user-management", DashboardController.getUserManagement);

/**
 * @swagger
 * /api/v1/dashboard/system-overview:
 *   get:
 *     summary: Get system overview (Admin only)
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System overview data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Admin role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/system-overview",
  requireAdminRole,
  DashboardController.getSystemOverview
);

/**
 * @swagger
 * /api/v1/dashboard/transaction-trends:
 *   get:
 *     summary: Get transaction trends (Admin only)
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Transaction trends data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Admin role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/transaction-trends",
  requireAdminRole,
  DashboardController.getTransactionTrends
);

/**
 * @swagger
 * /api/v1/dashboard/approval-metrics:
 *   get:
 *     summary: Get approval metrics (Admin only)
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Approval metrics data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Admin role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/approval-metrics",
  requireAdminRole,
  DashboardController.getApprovalMetrics
);

/**
 * @swagger
 * /api/v1/dashboard/performance-overview:
 *   get:
 *     summary: Get performance overview for account officers
 *     description: Retrieves performance metrics for all account officers based on transactions approved by Head Risk Management (final approval stage). Includes achievement percentages, approved transaction amounts, and monthly targets. Shows individual performance data and overall well-performed percentage.
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Performance overview retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Performance overview retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     wellPerformedPercentage:
 *                       type: number
 *                       description: Percentage of account officers performing above 80% of their target
 *                       example: 95
 *                     performers:
 *                       type: array
 *                       description: List of account officers with their performance metrics
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: Account officer ID
 *                             example: "sample-1"
 *                           name:
 *                             type: string
 *                             description: Full name of the account officer
 *                             example: "Gift Okoli"
 *                           initials:
 *                             type: string
 *                             description: Initials of the account officer
 *                             example: "GO"
 *                           approvedAmount:
 *                             type: number
 *                             description: Total amount from transactions approved by Head Risk Management for current month
 *                             example: 9600000
 *                           monthlyTarget:
 *                             type: number
 *                             description: Monthly target amount
 *                             example: ********
 *                           achievementPercentage:
 *                             type: number
 *                             description: Achievement percentage (approved transaction amount / monthly target * 100)
 *                             example: 80
 *                           transactionCount:
 *                             type: number
 *                             description: Number of transactions approved by Head Risk Management
 *                             example: 12
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/performance-overview",
  requireApprovalRole,
  DashboardController.getPerformanceOverview
);

export default router;
