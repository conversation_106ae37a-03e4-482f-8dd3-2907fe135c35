{"compilerOptions": {"module": "commonjs", "target": "es2020", "moduleResolution": "node", "noImplicitAny": true, "removeComments": true, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "rootDir": "./src", "outDir": "./dist", "baseUrl": "./src", "paths": {"@/*": ["./*"]}, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}