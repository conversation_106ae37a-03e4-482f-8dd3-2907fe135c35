import prisma from "../db/db";
import { UserR<PERSON>, TransactionStatus, ApprovalStage } from "@prisma/client";

/**
 * Query optimization service for complex database operations
 * Provides optimized queries with proper indexing and minimal data fetching
 */
export class QueryOptimizationService {
  
  /**
   * Optimized transaction count queries with proper indexing
   */
  static async getTransactionCounts(whereClause: any) {
    // Use Promise.all for parallel execution with optimized queries
    return Promise.all([
      // Total transactions - uses index on createdById or general count
      prisma.transaction.count({ where: whereClause }),

      // Pending transactions - uses compound index on status
      prisma.transaction.count({
        where: {
          ...whereClause,
          status: {
            in: [TransactionStatus.SUBMITTED, TransactionStatus.IN_PROGRESS],
          },
        },
      }),

      // Approved transactions - uses compound index on status + currentStage
      prisma.transaction.count({
        where: {
          ...whereClause,
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
      }),

      // Rejected transactions - uses index on status
      prisma.transaction.count({
        where: {
          ...whereClause,
          status: TransactionStatus.REJECTED,
        },
      }),
    ]);
  }

  /**
   * Optimized transaction aggregation queries
   */
  static async getTransactionAggregations(whereClause: any, currentMonth: Date, nextMonth: Date) {
    return Promise.all([
      // Total amount - uses compound index on status + currentStage
      prisma.transaction.aggregate({
        where: {
          ...whereClause,
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
        _sum: {
          requestedAmount: true,
        },
      }),

      // Monthly amount - uses compound index on createdAt + status
      prisma.transaction.aggregate({
        where: {
          ...whereClause,
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
          createdAt: {
            gte: currentMonth,
            lt: nextMonth,
          },
        },
        _sum: {
          requestedAmount: true,
        },
      }),
    ]);
  }

  /**
   * Optimized user queries with minimal data selection
   */
  static async getActiveAccountOfficers() {
    return prisma.user.findMany({
      where: {
        role: UserRole.ACCOUNT_OFFICER,
        isActive: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        monthlyTarget: true,
      },
      // Uses compound index on role + isActive
    });
  }

  /**
   * Optimized transaction queries with selective field loading
   */
  static async getTransactionsWithMinimalData(
    whereClause: any,
    skip: number,
    limit: number,
    sortBy: string = "createdAt",
    sortOrder: "asc" | "desc" = "desc"
  ) {
    return Promise.all([
      prisma.transaction.findMany({
        where: whereClause,
        select: {
          id: true,
          transactionId: true,
          status: true,
          currentStage: true,
          firstName: true,
          lastName: true,
          email: true,
          requestedAmount: true,
          createdAt: true,
          updatedAt: true,
          createdBy: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.transaction.count({ where: whereClause }),
    ]);
  }

  /**
   * Optimized search queries with proper indexing
   */
  static buildSearchWhereClause(search: string, baseWhereClause: any = {}) {
    if (!search) return baseWhereClause;

    return {
      ...baseWhereClause,
      OR: [
        { transactionId: { contains: search, mode: "insensitive" } },
        { firstName: { contains: search, mode: "insensitive" } },
        { lastName: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { bvn: { contains: search, mode: "insensitive" } },
      ],
    };
  }

  /**
   * Optimized approval queries
   */
  static async getApprovalMetrics() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return Promise.all([
      // Average approval time - uses index on createdAt
      prisma.transactionApproval.groupBy({
        by: ["stage"],
        where: {
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
        _count: {
          stage: true,
        },
      }),

      // Approval actions breakdown - uses index on createdAt
      prisma.transactionApproval.groupBy({
        by: ["action"],
        where: {
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
        _count: {
          action: true,
        },
      }),
    ]);
  }

  /**
   * Optimized document queries
   */
  static async getDocumentsByTransaction(transactionId: string) {
    return prisma.document.findMany({
      where: { transactionId },
      select: {
        id: true,
        originalName: true,
        fileType: true,
        fileSize: true,
        uploadedAt: true,
        r2Key: true,
        filePath: true,
      },
      orderBy: {
        uploadedAt: "desc",
      },
      // Uses index on transactionId
    });
  }

  /**
   * Optimized notification queries
   */
  static async getUserNotifications(
    userId: string,
    limit: number = 10,
    onlyUnread: boolean = false
  ) {
    const whereClause: any = { userId };
    if (onlyUnread) {
      whereClause.isRead = false;
    }

    return prisma.notification.findMany({
      where: whereClause,
      select: {
        id: true,
        type: true,
        title: true,
        message: true,
        isRead: true,
        createdAt: true,
        transactionId: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      // Uses compound index on userId + isRead
    });
  }

  /**
   * Optimized bulk operations
   */
  static async getBulkEligibleTransactions(limit: number, offset: number) {
    return Promise.all([
      prisma.transaction.findMany({
        where: {
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
        select: {
          id: true,
          transactionId: true,
          firstName: true,
          lastName: true,
          requestedAmount: true,
          accountName: true,
          accountNumber: true,
          bankName: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: [
          { updatedAt: "asc" },
          { createdAt: "asc" },
        ],
        take: limit,
        skip: offset,
        // Uses compound index on status + currentStage
      }),
      prisma.transaction.count({
        where: {
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
        },
      }),
    ]);
  }

  /**
   * Optimized loan eligibility check
   */
  static async checkLoanEligibilityByBVN(bvn: string) {
    return prisma.transaction.findFirst({
      where: {
        bvn,
        status: {
          in: [
            TransactionStatus.SUBMITTED,
            TransactionStatus.IN_PROGRESS,
            TransactionStatus.APPROVED,
            TransactionStatus.DISBURSED,
          ],
        },
      },
      select: {
        id: true,
        transactionId: true,
        status: true,
        disbursedAt: true,
        loanTenor: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      // Uses index on bvn
    });
  }

  /**
   * Optimized performance queries for dashboard
   */
  static async getPerformanceData(currentMonth: Date, nextMonth: Date) {
    return Promise.all([
      // Account officers - uses compound index on role + isActive
      this.getActiveAccountOfficers(),

      // Approved transactions for current month - uses compound indexes
      prisma.transaction.groupBy({
        by: ["createdById"],
        where: {
          status: TransactionStatus.APPROVED,
          currentStage: ApprovalStage.ACCOUNTANT,
          createdAt: {
            gte: currentMonth,
            lt: nextMonth,
          },
        },
        _sum: {
          requestedAmount: true,
        },
        _count: {
          id: true,
        },
        // Uses compound index on createdAt + status
      }),
    ]);
  }

  /**
   * Optimized cleanup queries
   */
  static async getCleanupCounts(
    otpCutoffTime: Date,
    notificationCutoffTime: Date,
    passwordResetCutoffTime: Date
  ) {
    return Promise.all([
      // OTP codes to cleanup - uses compound index on isUsed + createdAt
      prisma.otpCode.count({
        where: {
          OR: [
            { isUsed: true },
            {
              isUsed: false,
              createdAt: { lt: otpCutoffTime },
            },
          ],
        },
      }),

      // Notifications to cleanup - uses compound index on isRead + createdAt
      prisma.notification.count({
        where: {
          OR: [
            { isRead: true },
            {
              isRead: false,
              createdAt: { lt: notificationCutoffTime },
            },
          ],
        },
      }),

      // Password reset tokens to cleanup - uses compound index on isUsed + createdAt
      prisma.passwordResetToken.count({
        where: {
          OR: [
            { isUsed: true },
            {
              isUsed: false,
              createdAt: { lt: passwordResetCutoffTime },
            },
          ],
        },
      }),
    ]);
  }
}
