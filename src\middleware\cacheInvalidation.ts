import { Request, Response, NextFunction } from "express";
import { CacheService } from "../services/cacheService";
import { UserRole } from "@prisma/client";

/**
 * Middleware for automatic cache invalidation when data changes
 */
export class CacheInvalidationMiddleware {
  
  /**
   * Invalidate user-specific cache after user operations
   */
  static invalidateUserCache(userId?: string, role?: UserRole) {
    return (req: Request, res: Response, next: NextFunction) => {
      // Store original json method
      const originalJson = res.json;

      // Override json method to invalidate cache after successful response
      res.json = function(body: any) {
        // Only invalidate cache for successful operations (2xx status codes)
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const targetUserId = userId || req.params.userId || req.params.id;
            const targetRole = role || (req as any).user?.role;
            
            if (targetUserId) {
              CacheService.invalidateUserCache(targetUserId, targetRole);
            }
          } catch (error) {
            console.warn("Cache invalidation warning:", error);
          }
        }

        // Call original json method
        return originalJson.call(this, body);
      };

      next();
    };
  }

  /**
   * Invalidate transaction-related cache after transaction operations
   */
  static invalidateTransactionCache() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalJson = res.json;

      res.json = function(body: any) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            CacheService.invalidateTransactionCache();
          } catch (error) {
            console.warn("Transaction cache invalidation warning:", error);
          }
        }

        return originalJson.call(this, body);
      };

      next();
    };
  }

  /**
   * Invalidate system-wide cache after system operations
   */
  static invalidateSystemCache() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalJson = res.json;

      res.json = function(body: any) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            CacheService.invalidateSystemCache();
          } catch (error) {
            console.warn("System cache invalidation warning:", error);
          }
        }

        return originalJson.call(this, body);
      };

      next();
    };
  }

  /**
   * Invalidate all cache after major operations
   */
  static invalidateAllCache() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalJson = res.json;

      res.json = function(body: any) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            CacheService.flushAll();
          } catch (error) {
            console.warn("Full cache invalidation warning:", error);
          }
        }

        return originalJson.call(this, body);
      };

      next();
    };
  }

  /**
   * Invalidate dashboard cache specifically
   */
  static invalidateDashboardCache() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalJson = res.json;

      res.json = function(body: any) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            // Invalidate system overview and performance overview
            CacheService.invalidateSystemCache();
            
            // Also invalidate user dashboard stats for all users
            // This is a bit aggressive but ensures consistency
            CacheService.flushAll();
          } catch (error) {
            console.warn("Dashboard cache invalidation warning:", error);
          }
        }

        return originalJson.call(this, body);
      };

      next();
    };
  }
}
