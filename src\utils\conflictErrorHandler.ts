import { TransactionStatus, ApprovalStage, UserRole } from "@prisma/client";

/**
 * Utility class for handling conflict errors (409 status codes)
 * Provides consistent error messages and debugging information
 */
export class ConflictErrorHandler {
  /**
   * Generate user-friendly error message for transaction status conflicts
   */
  static getTransactionStatusConflictMessage(
    status: TransactionStatus,
    action: string = "process"
  ): string {
    const statusMessages: Record<TransactionStatus, string> = {
      [TransactionStatus.DRAFT]:
        "This transaction is still in draft status and has not been submitted for approval yet.",
      [TransactionStatus.SUBMITTED]:
        "This transaction has been submitted and is being processed.",
      [TransactionStatus.IN_PROGRESS]:
        "This transaction is currently in progress.",
      [TransactionStatus.APPROVED]:
        "This transaction has already been fully approved and is awaiting disbursement.",
      [TransactionStatus.REJECTED]:
        "This transaction has been rejected and cannot be processed further.",
      [TransactionStatus.SENT_BACK]:
        "This transaction has been sent back to the account officer for corrections.",
      [TransactionStatus.DISBURSED]:
        "This transaction has already been disbursed.",
      [TransactionStatus.COMPLETED]: "This transaction has been completed.",
      [TransactionStatus.LOAN_REPAID]: "This loan has been fully repaid.",
    };

    const statusMessage =
      statusMessages[status] || `Transaction status is ${status}.`;

    return `Cannot ${action} this transaction. ${statusMessage} Please refresh the page to see the current status.`;
  }

  /**
   * Generate user-friendly error message for approval stage conflicts
   */
  static getApprovalStageConflictMessage(
    currentStage: ApprovalStage | null,
    requiredStage: ApprovalStage,
    userRole: UserRole
  ): string {
    const stageMessages: Record<ApprovalStage, string> = {
      [ApprovalStage.ACCOUNT_OFFICER]: "Account Officer processing",
      [ApprovalStage.SUPERVISOR]: "Supervisor approval",
      [ApprovalStage.HEAD_CONSUMER_LENDING]: "Head Consumer Lending approval",
      [ApprovalStage.HEAD_RISK_MANAGEMENT]: "Head Risk Management approval",
      [ApprovalStage.MANAGING_DIRECTOR]: "Managing Director approval",
      [ApprovalStage.ACCOUNTANT]: "Accountant processing",
    };

    const currentStageMessage =
      (currentStage && stageMessages[currentStage]) ||
      currentStage ||
      "Unknown stage";
    const requiredStageMessage = stageMessages[requiredStage] || requiredStage;

    return `This transaction is not at the correct approval stage for your role (${userRole}). It is currently awaiting ${currentStageMessage}, but you can only perform ${requiredStageMessage}. Please refresh the page to see the current status.`;
  }

  /**
   * Generate user-friendly error message for duplicate approval attempts
   */
  static getDuplicateApprovalMessage(
    approverName: string,
    action: string,
    actionDate: string,
    transactionId: string
  ): string {
    return `This transaction (${transactionId}) has already been ${action.toLowerCase()} by ${approverName} on ${actionDate}. Only one user can perform actions at each approval stage. Please refresh the page to see the current status.`;
  }

  /**
   * Generate user-friendly error message for same user duplicate attempts
   */
  static getSameUserDuplicateMessage(
    action: string,
    actionDate: string,
    transactionId: string
  ): string {
    return `You have already ${action.toLowerCase()} this transaction (${transactionId}) on ${actionDate}. You can only act again if the transaction is sent back and resubmitted by the account officer. Please refresh the page to see the current status.`;
  }

  /**
   * Generate debugging information for conflict errors
   */
  static getConflictDebugInfo(context: {
    transactionId: string;
    transactionStatus: TransactionStatus;
    currentStage: ApprovalStage | null;
    userRole: UserRole;
    userId: string;
    action?: string;
    existingApproval?: {
      approverId: string;
      approverName: string;
      action: string;
      createdAt: Date;
    };
  }): Record<string, any> {
    return {
      timestamp: new Date().toISOString(),
      conflictType: "APPROVAL_CONFLICT",
      transaction: {
        id: context.transactionId,
        status: context.transactionStatus,
        currentStage: context.currentStage,
      },
      user: {
        id: context.userId,
        role: context.userRole,
        attemptedAction: context.action,
      },
      existingApproval: context.existingApproval
        ? {
            approverId: context.existingApproval.approverId,
            approverName: context.existingApproval.approverName,
            action: context.existingApproval.action,
            createdAt: context.existingApproval.createdAt.toISOString(),
          }
        : null,
    };
  }

  /**
   * Check if an error is a conflict error (409)
   */
  static isConflictError(error: any): boolean {
    return error?.statusCode === 409 || error?.status === 409;
  }

  /**
   * Extract conflict type from error message
   */
  static getConflictType(errorMessage: string): string {
    if (errorMessage.includes("already been")) {
      return "DUPLICATE_APPROVAL";
    }
    if (errorMessage.includes("not at the correct approval stage")) {
      return "WRONG_STAGE";
    }
    if (errorMessage.includes("Cannot process this transaction")) {
      return "INVALID_STATUS";
    }
    if (errorMessage.includes("already associated")) {
      return "DUPLICATE_BVN";
    }
    return "UNKNOWN_CONFLICT";
  }

  /**
   * Generate user-friendly suggestions for resolving conflicts
   */
  static getResolutionSuggestions(conflictType: string): string[] {
    const suggestions: Record<string, string[]> = {
      DUPLICATE_APPROVAL: [
        "Refresh the page to see the current transaction status",
        "Check with your team to see who has already processed this transaction",
        "If you believe this is an error, contact your supervisor",
      ],
      WRONG_STAGE: [
        "Refresh the page to see the current transaction status",
        "Wait for the transaction to reach your approval stage",
        "Check the transaction workflow to understand the approval process",
      ],
      INVALID_STATUS: [
        "Refresh the page to see the current transaction status",
        "Check if the transaction has been processed by someone else",
        "Contact the account officer if the transaction needs to be resubmitted",
      ],
      DUPLICATE_BVN: [
        "Check if you have an existing loan application",
        "Contact support if you believe this is an error",
        "Use a different BVN if you have multiple accounts",
      ],
      UNKNOWN_CONFLICT: [
        "Refresh the page and try again",
        "Contact support if the problem persists",
        "Check your internet connection and try again",
      ],
    };

    return suggestions[conflictType] || suggestions.UNKNOWN_CONFLICT;
  }
}
