import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
import { Express } from "express";

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "AMS Loan Management System API",
      version: "1.0.0",
      description:
        "A comprehensive loan management system with multi-step transaction creation and role-based approval workflows.",
      contact: {
        name: "AMS Loan System",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url:
          process.env.NODE_ENV === "production"
            ? "https://resplendent-spontaneity-staging.up.railway.app"
            : "http://localhost:8000",
        description:
          process.env.NODE_ENV === "production"
            ? "Production server"
            : "Development server",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "Enter your JWT token",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            id: { type: "string", description: "User ID" },
            email: { type: "string", format: "email" },
            firstName: { type: "string" },
            lastName: { type: "string" },
            role: {
              type: "string",
              enum: [
                "SUPER_ADMIN",
                "ACCOUNT_OFFICER",
                "SUPERVISOR",
                "HEAD_CONSUMER_LENDING",
                "HEAD_RISK_MANAGEMENT",
                "MANAGING_DIRECTOR",
                "ACCOUNTANT",
              ],
            },
            isActive: { type: "boolean" },
            monthlyTarget: { type: "number", nullable: true },
            createdAt: { type: "string", format: "date-time" },
          },
        },
        LoginRequest: {
          type: "object",
          required: ["email", "password"],
          properties: {
            email: { type: "string", format: "email" },
            password: { type: "string", minLength: 8 },
          },
        },
        Transaction: {
          type: "object",
          properties: {
            id: { type: "string" },
            transactionId: {
              type: "string",
              description: "Generated transaction ID",
            },
            status: {
              type: "string",
              enum: [
                "DRAFT",
                "SUBMITTED",
                "UNDER_REVIEW",
                "APPROVED",
                "REJECTED",
                "SENT_BACK",
              ],
            },
            firstName: { type: "string" },
            lastName: { type: "string" },
            email: { type: "string", format: "email" },
            requestedAmount: { type: "number" },
            loanTenor: { type: "integer", description: "Loan tenor in months" },
            createdAt: { type: "string", format: "date-time" },
          },
        },
        Error: {
          type: "object",
          properties: {
            success: { type: "boolean", example: false },
            message: { type: "string" },
            error: { type: "string" },
          },
        },
        Success: {
          type: "object",
          properties: {
            success: { type: "boolean", example: true },
            message: { type: "string" },
            data: { type: "object" },
          },
        },
      },
    },
    paths: {
      "/api/auth/login": {
        post: {
          tags: ["Authentication"],
          summary: "User login",
          description: "Login with email and password to receive OTP",
          requestBody: {
            required: true,
            content: {
              "application/json": {
                schema: { $ref: "#/components/schemas/LoginRequest" },
              },
            },
          },
          responses: {
            "200": {
              description: "Login successful, OTP sent",
              content: {
                "application/json": {
                  schema: { $ref: "#/components/schemas/Success" },
                },
              },
            },
            "400": {
              description: "Bad request",
              content: {
                "application/json": {
                  schema: { $ref: "#/components/schemas/Error" },
                },
              },
            },
          },
        },
      },
      "/api/auth/verify-otp": {
        post: {
          tags: ["Authentication"],
          summary: "Verify OTP",
          description: "Verify OTP to get access tokens",
          requestBody: {
            required: true,
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  required: ["email", "otp"],
                  properties: {
                    email: { type: "string", format: "email" },
                    otp: { type: "string", minLength: 6, maxLength: 6 },
                  },
                },
              },
            },
          },
          responses: {
            "200": { description: "OTP verified successfully" },
            "400": { description: "Invalid OTP" },
          },
        },
      },
      "/api/transactions": {
        get: {
          tags: ["Transactions"],
          summary: "Get transactions",
          description: "Retrieve user transactions",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Transactions retrieved successfully",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean" },
                      data: {
                        type: "array",
                        items: { $ref: "#/components/schemas/Transaction" },
                      },
                    },
                  },
                },
              },
            },
            "401": { description: "Unauthorized" },
          },
        },
        post: {
          tags: ["Transactions"],
          summary: "Create transaction",
          description: "Create a new loan transaction",
          security: [{ bearerAuth: [] }],
          responses: {
            "201": { description: "Transaction created successfully" },
            "400": { description: "Bad request" },
            "401": { description: "Unauthorized" },
          },
        },
      },
      "/api/users": {
        get: {
          tags: ["Users"],
          summary: "Get users",
          description: "Retrieve all users (Admin only)",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Users retrieved successfully",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean" },
                      data: {
                        type: "array",
                        items: { $ref: "#/components/schemas/User" },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    security: [{ bearerAuth: [] }],
  },
  apis: [], // Empty array - we're defining paths manually above
};

export const setupSwagger = (app: Express): void => {
  try {
    const specs = swaggerJsdoc(options);

    app.use(
      "/api-docs",
      swaggerUi.serve,
      swaggerUi.setup(specs, {
        explorer: true,
        customCss: ".swagger-ui .topbar { display: none }",
        customSiteTitle: "AMS Loan System API Documentation",
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          docExpansion: "none",
          filter: true,
          showRequestHeaders: true,
        },
      })
    );

    app.get("/api-docs.json", (req, res) => {
      res.setHeader("Content-Type", "application/json");
      res.send(specs);
    });

    console.log("📚 Swagger documentation available at /api-docs");
  } catch (error) {
    console.error("❌ Failed to setup Swagger:", error);

    app.get("/api-docs", (req, res) => {
      res.json({
        message: "API Documentation temporarily unavailable",
        note: "Your API endpoints are working fine!",
        baseUrl:
          process.env.NODE_ENV === "production"
            ? "https://resplendent-spontaneity-staging.up.railway.app"
            : "http://localhost:8000",
        endpoints: [
          "POST /api/auth/login",
          "POST /api/auth/verify-otp",
          "GET /api/transactions",
          "POST /api/transactions",
          "GET /api/users",
        ],
      });
    });
  }
};

export default setupSwagger;
