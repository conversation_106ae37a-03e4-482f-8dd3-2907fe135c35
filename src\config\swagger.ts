import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
import { Express } from "express";

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "AMS Loan Management System API",
      version: "1.0.0",
      description: `
        A comprehensive loan management system with multi-step transaction creation and role-based approval workflows.
        
        ## Features
        - 🔐 JWT Authentication with OTP verification
        - 👥 Role-based access control (7 user roles)
        - 📋 5-step transaction creation workflow
        - ✅ Multi-level approval system
        - 📁 Document upload management
        - 📊 Dashboard and analytics
        - 🔔 Real-time notifications
        - 📈 Progress tracking and workflow visualization
        - 👤 User management with role-based access control
        - 📄 CSV export for data analysis and reporting
        - 💰 Bulk disbursement processing for accountants
        - 🔐 Secure password reset with email notifications

        Password Reset System Features:
        - Secure token generation with 64-character hex tokens
        - Professional HTML email templates with mobile responsiveness
        - Security controls: 1-hour expiration, one-time use, rate limiting
        - User experience: Token validation, clear error messages, confirmation emails
        - Enterprise ready: SMTP configuration, audit logging, high availability
        
        ## Authentication
        1. Login with email/password to get OTP
        2. Verify OTP to get access tokens
        3. Use Bearer token in Authorization header
        
        ## User Roles
        - **SUPER_ADMIN**: System administration
        - **ACCOUNT_OFFICER**: Create and manage transactions
        - **SUPERVISOR**: First-level approval
        - **HEAD_CONSUMER_LENDING**: Second-level approval
        - **HEAD_RISK_MANAGEMENT**: Risk assessment
        - **MANAGING_DIRECTOR**: High-value approvals (>1M)
        - **ACCOUNTANT**: Final verification
        
        ## Workflow
        1. Account Officer creates transaction (5 steps)
        2. Submits for approval
        3. Goes through approval chain based on amount
        4. Final disbursement after all approvals
      `,
      contact: {
        name: "AMS Loan System",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: "http://localhost:8000",
        description: "Development server",
      },
      {
        url: "https://resplendent-spontaneity-staging.up.railway.app",
        description: "Staging server",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "Enter your JWT token",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            id: { type: "string", description: "User ID" },
            email: { type: "string", format: "email" },
            firstName: { type: "string" },
            lastName: { type: "string" },
            role: {
              type: "string",
              enum: [
                "SUPER_ADMIN",
                "ACCOUNT_OFFICER",
                "SUPERVISOR",
                "HEAD_CONSUMER_LENDING",
                "HEAD_RISK_MANAGEMENT",
                "MANAGING_DIRECTOR",
                "ACCOUNTANT",
              ],
            },
            isActive: { type: "boolean" },
            monthlyTarget: { type: "number", nullable: true },
            createdAt: { type: "string", format: "date-time" },
          },
        },
        Transaction: {
          type: "object",
          properties: {
            id: { type: "string" },
            transactionId: {
              type: "string",
              description: "Generated transaction ID",
            },
            status: {
              type: "string",
              enum: [
                "DRAFT",
                "SUBMITTED",
                "UNDER_REVIEW",
                "APPROVED",
                "REJECTED",
                "SENT_BACK",
              ],
            },
            currentStage: {
              type: "string",
              enum: [
                "ACCOUNT_OFFICER",
                "SUPERVISOR",
                "HEAD_CONSUMER_LENDING",
                "HEAD_RISK_MANAGEMENT",
                "MANAGING_DIRECTOR",
                "ACCOUNTANT",
              ],
            },
            // Personal Information
            firstName: { type: "string" },
            lastName: { type: "string" },
            email: { type: "string", format: "email" },
            phoneNumber: { type: "string" },
            // Loan Information
            requestedAmount: { type: "number" },
            loanTenor: { type: "integer", description: "Loan tenor in months" },
            repaymentMode: {
              type: "string",
              enum: ["MONTHLY", "QUARTERLY", "SEMI_ANNUALLY", "ANNUALLY"],
            },
            grossPay: { type: "number" },
            netPay: { type: "number" },
            purposeOfLoan: { type: "string" },
            // Disbursement
            accountName: { type: "string" },
            accountNumber: { type: "string" },
            bankName: { type: "string" },
            createdAt: { type: "string", format: "date-time" },
            updatedAt: { type: "string", format: "date-time" },
          },
        },
        LoginRequest: {
          type: "object",
          required: ["email", "password"],
          properties: {
            email: { type: "string", format: "email" },
            password: { type: "string", minLength: 8 },
          },
        },
        OTPVerificationRequest: {
          type: "object",
          required: ["email", "otp"],
          properties: {
            email: { type: "string", format: "email" },
            otp: { type: "string", minLength: 6, maxLength: 6 },
          },
        },
        PersonalInfoRequest: {
          type: "object",
          required: [
            "firstName",
            "lastName",
            "email",
            "phoneNumber",
            "dateOfBirth",
          ],
          properties: {
            firstName: { type: "string" },
            middleName: { type: "string" },
            lastName: { type: "string" },
            gender: { type: "string", enum: ["MALE", "FEMALE"] },
            maritalStatus: {
              type: "string",
              enum: ["SINGLE", "MARRIED", "DIVORCED", "WIDOWED"],
            },
            dateOfBirth: { type: "string", format: "date" },
            email: { type: "string", format: "email" },
            phoneNumber: { type: "string" },
            street: { type: "string" },
            city: { type: "string" },
            state: { type: "string" },
            postalCode: { type: "string" },
            organizationName: { type: "string" },
            ippisNumber: { type: "string" },
            employmentDate: { type: "string", format: "date" },
          },
        },
        LoanInfoRequest: {
          type: "object",
          required: [
            "requestedAmount",
            "loanTenor",
            "repaymentMode",
            "grossPay",
            "netPay",
            "purposeOfLoan",
          ],
          properties: {
            requestedAmount: { type: "number", minimum: 1 },
            loanTenor: { type: "integer", minimum: 1 },
            repaymentMode: {
              type: "string",
              enum: ["MONTHLY", "QUARTERLY", "SEMI_ANNUALLY", "ANNUALLY"],
            },
            grossPay: { type: "number", minimum: 1 },
            netPay: { type: "number", minimum: 1 },
            purposeOfLoan: { type: "string" },
          },
        },
        ApprovalRequest: {
          type: "object",
          required: ["action"],
          properties: {
            action: {
              type: "string",
              enum: ["APPROVE", "REJECT", "SEND_BACK"],
            },
            comments: { type: "string" },
          },
        },
        PerformanceOverview: {
          type: "object",
          properties: {
            wellPerformedPercentage: {
              type: "number",
              description:
                "Percentage of account officers performing above 80% of their target",
              example: 95,
            },
            performers: {
              type: "array",
              description:
                "List of account officers with their performance metrics",
              items: {
                type: "object",
                properties: {
                  id: {
                    type: "string",
                    description: "Account officer ID",
                    example: "sample-1",
                  },
                  name: {
                    type: "string",
                    description: "Full name of the account officer",
                    example: "Gift Okoli",
                  },
                  initials: {
                    type: "string",
                    description: "Initials of the account officer",
                    example: "GO",
                  },
                  approvedAmount: {
                    type: "number",
                    description: "Total approved amount for current month",
                    example: 9600000,
                  },
                  monthlyTarget: {
                    type: "number",
                    description: "Monthly target amount",
                    example: ********,
                  },
                  achievementPercentage: {
                    type: "number",
                    description:
                      "Achievement percentage (approved amount / monthly target * 100)",
                    example: 80,
                  },
                  transactionCount: {
                    type: "number",
                    description: "Number of approved transactions",
                    example: 12,
                  },
                },
              },
            },
          },
        },
        Document: {
          type: "object",
          properties: {
            id: {
              type: "string",
              description: "Document ID",
              example: "64f8a1b2c3d4e5f6a7b8c9d0",
            },
            fileName: {
              type: "string",
              description: "Stored file name",
              example: "1704067200000_abc123_passport.pdf",
            },
            originalName: {
              type: "string",
              description: "Original file name",
              example: "passport.pdf",
            },
            fileSize: {
              type: "number",
              description: "File size in bytes",
              example: 1024000,
            },
            fileType: {
              type: "string",
              enum: ["PDF", "IMAGE", "DOCUMENT"],
              description: "File type category",
              example: "PDF",
            },
            mimeType: {
              type: "string",
              description: "MIME type of the file",
              example: "application/pdf",
            },
            uploadedAt: {
              type: "string",
              format: "date-time",
              description: "Upload timestamp",
              example: "2024-01-01T12:00:00.000Z",
            },
            fileUrl: {
              type: "string",
              description: "Public URL to access the file",
              example:
                "https://r2-bucket.example.com/ams-loan-docs/transaction-id/file.pdf",
            },
            formattedSize: {
              type: "string",
              description: "Human-readable file size",
              example: "1.00 MB",
            },
            metadata: {
              type: "string",
              nullable: true,
              description:
                "Custom metadata for document categorization (JSON string or plain text)",
              example:
                '{"category": "identity", "documentType": "passport", "description": "Customer passport copy"}',
            },
          },
        },
        DocumentUploadResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Document uploaded successfully",
            },
            data: {
              type: "object",
              properties: {
                id: {
                  type: "string",
                  example: "64f8a1b2c3d4e5f6a7b8c9d0",
                },
                fileName: {
                  type: "string",
                  example: "1704067200000_abc123_passport.pdf",
                },
                originalName: {
                  type: "string",
                  example: "passport.pdf",
                },
                fileUrl: {
                  type: "string",
                  example:
                    "https://r2-bucket.example.com/ams-loan-docs/transaction-id/file.pdf",
                },
                metadata: {
                  type: "string",
                  nullable: true,
                  example:
                    '{"category": "identity", "documentType": "passport"}',
                },
              },
            },
          },
        },
        MultipleDocumentUploadResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "2 documents uploaded successfully",
            },
            data: {
              type: "object",
              properties: {
                successful: {
                  type: "array",
                  items: {
                    $ref: "#/components/schemas/DocumentUploadResponse/properties/data",
                  },
                },
                failed: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      originalName: {
                        type: "string",
                        example: "invalid_file.exe",
                      },
                      error: {
                        type: "string",
                        example: "File type .exe is not allowed",
                      },
                    },
                  },
                },
                totalUploaded: {
                  type: "number",
                  example: 2,
                },
                totalFailed: {
                  type: "number",
                  example: 0,
                },
              },
            },
          },
        },
        DocumentListResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Documents retrieved successfully",
            },
            data: {
              type: "array",
              items: {
                $ref: "#/components/schemas/Document",
              },
            },
          },
        },
        DocumentMetadata: {
          type: "object",
          description:
            "Example metadata structures for document categorization",
          examples: [
            {
              summary: "Identity Document",
              value: {
                category: "identity",
                documentType: "passport",
                description: "Customer passport copy",
                expiryDate: "2030-12-31",
              },
            },
            {
              summary: "Financial Document",
              value: {
                category: "financial",
                subcategory: "income_proof",
                documentType: "salary_slip",
                month: "2024-01",
                verified: false,
              },
            },
            {
              summary: "Plain Text Metadata",
              value: "Identity document - National ID card",
            },
          ],
        },
        ProgressStage: {
          type: "object",
          description: "Individual stage in the transaction workflow",
          properties: {
            id: {
              type: "string",
              description: "Stage identifier",
              example: "under_review",
            },
            name: {
              type: "string",
              description: "Human-readable stage name",
              example: "Under Review",
            },
            status: {
              type: "string",
              enum: ["completed", "active", "pending"],
              description: "Current status of this stage",
              example: "active",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              nullable: true,
              description: "When this stage was reached (null if not reached)",
              example: "2024-01-16T09:00:00Z",
            },
            order: {
              type: "number",
              description: "Stage order in workflow (1-5)",
              example: 3,
            },
          },
        },
        TransactionProgress: {
          type: "object",
          description:
            "Complete progress tracking information for a transaction",
          properties: {
            transactionId: {
              type: "string",
              description: "Internal transaction ID",
              example: "687a4a82b2114af448562d15",
            },
            transactionNumber: {
              type: "string",
              description: "Human-readable transaction number",
              example: "TXN-2024-001",
            },
            currentStatus: {
              type: "string",
              enum: [
                "DRAFT",
                "SUBMITTED",
                "IN_PROGRESS",
                "APPROVED",
                "REJECTED",
                "SENT_BACK",
                "DISBURSED",
                "COMPLETED",
                "LOAN_REPAID",
              ],
              description: "Current transaction status",
              example: "IN_PROGRESS",
            },
            currentStage: {
              type: "string",
              enum: [
                "ACCOUNT_OFFICER",
                "SUPERVISOR",
                "HEAD_CONSUMER_LENDING",
                "HEAD_RISK_MANAGEMENT",
                "MANAGING_DIRECTOR",
                "ACCOUNTANT",
              ],
              description: "Current approval stage",
              example: "HEAD_CONSUMER_LENDING",
            },
            progressPercentage: {
              type: "number",
              minimum: 0,
              maximum: 100,
              description: "Overall progress percentage",
              example: 60,
            },
            stages: {
              type: "array",
              description: "List of workflow stages with their status",
              items: {
                $ref: "#/components/schemas/ProgressStage",
              },
            },
            statusLabel: {
              type: "string",
              description: "Human-readable status label",
              example: "Under Review",
            },
            nextAction: {
              type: "string",
              nullable: true,
              description: "Description of next expected action",
              example: "Waiting for Head Consumer Lending approval",
            },
            lastUpdated: {
              type: "string",
              format: "date-time",
              description: "When transaction was last updated",
              example: "2024-01-16T09:00:00Z",
            },
          },
        },
        BulkProgressResponse: {
          type: "object",
          description: "Response for bulk progress tracking requests",
          properties: {
            successful: {
              type: "array",
              description: "Successfully retrieved transaction progress",
              items: {
                $ref: "#/components/schemas/TransactionProgress",
              },
            },
            failed: {
              type: "array",
              description: "Transactions that failed to retrieve progress",
              items: {
                type: "object",
                properties: {
                  transactionId: {
                    type: "string",
                    example: "687a4a82b2114af448562d17",
                  },
                  error: {
                    type: "string",
                    example: "Transaction not found",
                  },
                },
              },
            },
            summary: {
              type: "object",
              description: "Summary of bulk operation results",
              properties: {
                total: {
                  type: "number",
                  description: "Total number of transactions requested",
                  example: 3,
                },
                successful: {
                  type: "number",
                  description: "Number of successfully retrieved transactions",
                  example: 2,
                },
                failed: {
                  type: "number",
                  description: "Number of failed transactions",
                  example: 1,
                },
              },
            },
          },
        },
        UpdateUserRequest: {
          type: "object",
          description: "Request schema for updating user details",
          properties: {
            firstName: {
              type: "string",
              minLength: 1,
              description: "User's first name",
              example: "John",
            },
            lastName: {
              type: "string",
              minLength: 1,
              description: "User's last name",
              example: "Doe",
            },
            email: {
              type: "string",
              format: "email",
              description: "User's email address (must be unique)",
              example: "<EMAIL>",
            },
            role: {
              type: "string",
              enum: [
                "SUPER_ADMIN",
                "ACCOUNT_OFFICER",
                "SUPERVISOR",
                "HEAD_CONSUMER_LENDING",
                "HEAD_RISK_MANAGEMENT",
                "MANAGING_DIRECTOR",
                "ACCOUNTANT",
              ],
              description: "User's role (Super Admin only)",
              example: "SUPERVISOR",
            },
            monthlyTarget: {
              type: "number",
              minimum: 1,
              description: "Monthly target amount for the user",
              example: 50000,
            },
            isActive: {
              type: "boolean",
              description:
                "Whether the user account is active (Super Admin only)",
              example: true,
            },
          },
        },
        CreateUserRequest: {
          type: "object",
          description: "Request schema for creating a new user",
          required: ["email", "password", "firstName", "lastName", "role"],
          properties: {
            email: {
              type: "string",
              format: "email",
              description: "User's email address (must be unique)",
              example: "<EMAIL>",
            },
            password: {
              type: "string",
              minLength: 8,
              description: "User's password (minimum 8 characters)",
              example: "SecurePassword123",
            },
            firstName: {
              type: "string",
              minLength: 1,
              description: "User's first name",
              example: "Jane",
            },
            lastName: {
              type: "string",
              minLength: 1,
              description: "User's last name",
              example: "Smith",
            },
            role: {
              type: "string",
              enum: [
                "SUPER_ADMIN",
                "ACCOUNT_OFFICER",
                "SUPERVISOR",
                "HEAD_CONSUMER_LENDING",
                "HEAD_RISK_MANAGEMENT",
                "MANAGING_DIRECTOR",
                "ACCOUNTANT",
              ],
              description: "User's role in the system",
              example: "ACCOUNT_OFFICER",
            },
            monthlyTarget: {
              type: "number",
              minimum: 1,
              description: "Monthly target amount for the user (optional)",
              example: 75000,
            },
          },
        },
        EligibleTransaction: {
          type: "object",
          description: "Transaction eligible for disbursement",
          properties: {
            id: {
              type: "string",
              description: "Internal transaction ID",
              example: "687a4a82b2114af448562d15",
            },
            transactionId: {
              type: "string",
              description: "Human-readable transaction number",
              example: "TXN-2024-001",
            },
            firstName: {
              type: "string",
              description: "Borrower's first name",
              example: "John",
            },
            lastName: {
              type: "string",
              description: "Borrower's last name",
              example: "Doe",
            },
            requestedAmount: {
              type: "number",
              description: "Loan amount requested",
              example: 50000,
            },
            accountName: {
              type: "string",
              nullable: true,
              description: "Disbursement account name",
              example: "John Doe",
            },
            accountNumber: {
              type: "string",
              nullable: true,
              description: "Disbursement account number",
              example: "**********",
            },
            bankName: {
              type: "string",
              nullable: true,
              description: "Disbursement bank name",
              example: "First Bank of Nigeria",
            },
            createdAt: {
              type: "string",
              format: "date-time",
              description: "Transaction creation date",
              example: "2024-01-15T10:00:00Z",
            },
            updatedAt: {
              type: "string",
              format: "date-time",
              description: "Last update date",
              example: "2024-01-16T14:30:00Z",
            },
          },
        },
        BulkDisbursementRequest: {
          type: "object",
          description: "Request for bulk disbursement processing",
          required: ["transactionIds"],
          properties: {
            transactionIds: {
              type: "array",
              items: {
                type: "string",
              },
              minItems: 1,
              maxItems: 100,
              description: "Array of transaction IDs to disburse",
              example: [
                "687a4a82b2114af448562d15",
                "687a4a82b2114af448562d16",
                "687a4a82b2114af448562d17",
              ],
            },
          },
        },
        DisbursementResult: {
          type: "object",
          description: "Result of individual transaction disbursement",
          properties: {
            transactionId: {
              type: "string",
              description: "Transaction number",
              example: "TXN-2024-001",
            },
            success: {
              type: "boolean",
              description: "Whether disbursement was successful",
              example: true,
            },
            error: {
              type: "string",
              nullable: true,
              description: "Error message if disbursement failed",
              example: null,
            },
            amount: {
              type: "number",
              nullable: true,
              description: "Disbursed amount",
              example: 50000,
            },
          },
        },
        BulkDisbursementSummary: {
          type: "object",
          description: "Summary of bulk disbursement operation",
          properties: {
            totalTransactions: {
              type: "integer",
              description: "Total number of transactions processed",
              example: 3,
            },
            successfulDisbursements: {
              type: "integer",
              description: "Number of successful disbursements",
              example: 3,
            },
            failedDisbursements: {
              type: "integer",
              description: "Number of failed disbursements",
              example: 0,
            },
            totalAmount: {
              type: "number",
              description: "Total amount disbursed",
              example: 150000,
            },
            successful: {
              type: "array",
              items: {
                $ref: "#/components/schemas/DisbursementResult",
              },
              description: "Details of successful disbursements",
            },
            failed: {
              type: "array",
              items: {
                $ref: "#/components/schemas/DisbursementResult",
              },
              description: "Details of failed disbursements",
            },
            processedAt: {
              type: "string",
              format: "date-time",
              description: "When the bulk operation was processed",
              example: "2024-01-18T15:30:00Z",
            },
            processedBy: {
              type: "string",
              description:
                "ID of the accountant who processed the disbursements",
              example: "687a4a82b2114af448562d20",
            },
          },
        },
        DisbursementStats: {
          type: "object",
          description: "Disbursement statistics for dashboard",
          properties: {
            eligibleCount: {
              type: "integer",
              description: "Number of transactions eligible for disbursement",
              example: 15,
            },
            totalEligibleAmount: {
              type: "number",
              description: "Total amount of eligible transactions",
              example: 750000,
            },
            recentDisbursements: {
              type: "integer",
              description: "Number of disbursements in the last 24 hours",
              example: 8,
            },
          },
        },
        BulkDisbursementValidation: {
          type: "object",
          description: "Validation result for bulk disbursement",
          properties: {
            totalRequested: {
              type: "integer",
              description: "Total number of transactions requested",
              example: 5,
            },
            validTransactions: {
              type: "integer",
              description: "Number of valid transactions",
              example: 4,
            },
            invalidTransactions: {
              type: "integer",
              description: "Number of invalid transactions",
              example: 1,
            },
            totalAmount: {
              type: "number",
              description: "Total amount for valid transactions",
              example: 200000,
            },
            canProceed: {
              type: "boolean",
              description: "Whether bulk disbursement can proceed",
              example: false,
            },
            issues: {
              type: "array",
              items: {
                type: "string",
              },
              description: "List of validation issues",
              example: ["1 transaction(s) are not eligible for disbursement"],
            },
          },
        },
        PasswordResetRequest: {
          type: "object",
          description:
            "Request for password reset - initiates the password reset process by sending a secure token via email",
          required: ["email"],
          properties: {
            email: {
              type: "string",
              format: "email",
              description:
                "Email address of the account to reset. Must be a valid email format and associated with an active account in the system.",
              example: "<EMAIL>",
              pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
            },
          },
          example: {
            email: "<EMAIL>",
          },
          additionalProperties: false,
        },
        PasswordResetConfirm: {
          type: "object",
          description:
            "Confirm password reset with secure token and new password. Completes the password reset process by updating the user's password.",
          required: ["token", "newPassword"],
          properties: {
            token: {
              type: "string",
              description:
                "Password reset token received via email. This is a cryptographically secure 64-character hexadecimal string that expires in 1 hour and can only be used once.",
              example:
                "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a1b2c3d4e5f6",
              minLength: 64,
              maxLength: 64,
              pattern: "^[a-f0-9]{64}$",
            },
            newPassword: {
              type: "string",
              minLength: 8,
              description:
                "New password that meets security requirements: minimum 8 characters, at least one uppercase letter, one lowercase letter, and one number. No maximum length restriction for better security.",
              example: "NewSecurePass123",
              pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$",
            },
          },
          example: {
            token:
              "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a1b2c3d4e5f6",
            newPassword: "MyNewSecurePassword123",
          },
          additionalProperties: false,
        },
        PasswordResetTokenValidation: {
          type: "object",
          description: "Response for password reset token validation endpoint",
          properties: {
            success: {
              type: "boolean",
              description:
                "Indicates if the token validation request was successful",
              example: true,
            },
            message: {
              type: "string",
              description: "Human-readable message about the validation result",
              example: "Token is valid",
            },
            data: {
              type: "object",
              description: "Token validation result data",
              properties: {
                valid: {
                  type: "boolean",
                  description:
                    "Whether the token is valid and can be used for password reset",
                  example: true,
                },
                email: {
                  type: "string",
                  format: "email",
                  description:
                    "Email address associated with the token (for display purposes only, no sensitive data exposed)",
                  example: "<EMAIL>",
                },
                expiresAt: {
                  type: "string",
                  format: "date-time",
                  description:
                    "Token expiration timestamp in ISO 8601 format (UTC timezone)",
                  example: "2024-01-15T14:30:00.000Z",
                },
              },
              required: ["valid"],
            },
          },
          required: ["success", "message"],
          example: {
            success: true,
            message: "Token is valid",
            data: {
              valid: true,
              email: "<EMAIL>",
              expiresAt: "2024-01-15T14:30:00.000Z",
            },
          },
        },
        Error: {
          type: "object",
          properties: {
            success: { type: "boolean", example: false },
            message: { type: "string" },
            error: { type: "string" },
          },
        },
        Success: {
          type: "object",
          properties: {
            success: { type: "boolean", example: true },
            message: { type: "string" },
            data: { type: "object" },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./src/routes/*.ts", "./src/controllers/*.ts"], // Path to the API docs
};

const specs = swaggerJsdoc(options);

export const setupSwagger = (app: Express): void => {
  app.use(
    "/api-docs",
    swaggerUi.serve,
    swaggerUi.setup(specs, {
      explorer: true,
      customCss: ".swagger-ui .topbar { display: none }",
      customSiteTitle: "AMS Loan System API Documentation",
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: "none",
        filter: true,
        showRequestHeaders: true,
      },
    })
  );

  // Serve the raw OpenAPI spec
  app.get("/api-docs.json", (req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(specs);
  });
};

export default specs;
