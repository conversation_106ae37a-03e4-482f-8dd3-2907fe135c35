import archiver from "archiver";
import { Readable } from "stream";
import axios from "axios";
import { DocumentService } from "./documentService";
import { PDFService } from "./pdfService";
import { TransactionService } from "./transactionService";
import { R2Service } from "./r2Service";
import { UserRole } from "@prisma/client";
import prisma from "../db/db";

export class ZipService {
  /**
   * Creates a ZIP archive containing all transaction documents and PDF
   */
  static async createTransactionZip(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<{ zipBuffer: Buffer; fileName: string }> {
    try {
      // Get transaction data for PDF generation and access control
      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      if (!transaction) {
        throw new Error("Transaction not found or access denied");
      }

      // Get all documents for the transaction
      const documents = await DocumentService.getTransactionDocuments(
        transactionId,
        userId,
        userRole
      );

      // Generate transaction PDF
      const pdfBuffer = await PDFService.generateTransactionPDF(transaction);

      // Create ZIP archive
      const archive = archiver("zip", {
        zlib: { level: 9 }, // Maximum compression
      });

      const buffers: Buffer[] = [];

      // Collect ZIP data
      archive.on("data", (chunk) => {
        buffers.push(chunk);
      });

      // Handle archive completion
      const zipPromise = new Promise<Buffer>((resolve, reject) => {
        archive.on("end", () => {
          const zipBuffer = Buffer.concat(buffers);
          resolve(zipBuffer);
        });

        archive.on("error", (err) => {
          reject(err);
        });
      });

      // Add transaction PDF to ZIP
      archive.append(pdfBuffer, {
        name: "transaction-summary.pdf",
      });

      // Add all documents to ZIP
      if (documents && documents.length > 0) {
        for (const document of documents) {
          try {
            // Try multiple approaches to download the file
            let fileBuffer: Buffer | null = null;

            // First, try using the document service (handles permissions)
            try {
              fileBuffer = await this.downloadDocumentById(
                document.id,
                userId,
                userRole
              );
            } catch (error) {
              console.log(
                `Document service failed for ${document.originalName}, trying R2 direct download...`
              );

              // If that fails, try to download directly from R2 using the key
              try {
                const r2Key = document.fileName; // fileName contains the R2 key
                console.log(`Attempting R2 direct download for key: ${r2Key}`);
                fileBuffer = await R2Service.downloadFile(r2Key);
                console.log(
                  `✅ Successfully downloaded ${document.originalName} from R2 directly`
                );
              } catch (r2Error) {
                console.log(
                  `R2 direct download failed for ${document.originalName}, trying public URL...`
                );

                // If that fails, try the direct URL
                try {
                  fileBuffer = await this.downloadFileFromUrl(document.fileUrl);
                } catch (urlError) {
                  console.log(
                    `Direct URL failed for ${document.originalName}, trying signed URL...`
                  );

                  // Final attempt: try generating a signed URL
                  try {
                    const signedUrl = await R2Service.generateSignedUrl(
                      document.fileName
                    );
                    fileBuffer = await this.downloadFileFromUrl(signedUrl);
                  } catch (signedUrlError) {
                    console.log(
                      `All download attempts failed for ${document.originalName}`
                    );
                  }
                }
              }
            }

            if (fileBuffer) {
              // Add to ZIP with original filename
              archive.append(fileBuffer, {
                name: document.originalName,
              });
              console.log(
                `✅ Successfully added ${document.originalName} to ZIP`
              );
            } else {
              console.error(
                `❌ Failed to download ${document.originalName} - skipping`
              );
            }
          } catch (error) {
            console.error(
              `Error adding document ${document.originalName} to ZIP:`,
              error
            );
            // Continue with other files even if one fails
          }
        }
      }

      // Finalize the archive
      archive.finalize();

      // Wait for ZIP creation to complete
      const zipBuffer = await zipPromise;

      // Generate filename based on transaction ID
      const fileName = `${transaction.transactionId}.zip`;

      return {
        zipBuffer,
        fileName,
      };
    } catch (error) {
      console.error("Error creating transaction ZIP:", error);
      throw new Error(
        `Failed to create ZIP archive: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Downloads a document using R2 service with multiple fallback strategies
   */
  private static async downloadDocumentById(
    documentId: string,
    userId: string,
    userRole: UserRole
  ): Promise<Buffer> {
    try {
      // Get document file info using DocumentService (for permission checking)
      const documentFile = await DocumentService.getDocumentFile(
        documentId,
        userId,
        userRole.toString()
      );

      // Get the document from database to access R2 metadata
      const document = await prisma.document.findFirst({
        where: { id: documentId },
      });

      if (!document) {
        throw new Error("Document not found in database");
      }

      // Use the R2 key to download directly from R2
      const r2Key = document.r2Key || document.fileName;

      // Try multiple download strategies
      const strategies = [
        {
          name: "R2 Direct Download",
          action: async () => await R2Service.downloadFile(r2Key),
        },
        {
          name: "Public URL Download",
          action: async () =>
            await this.downloadFileFromUrl(documentFile.fileUrl),
        },
        {
          name: "Signed URL Download",
          action: async () => {
            const signedUrl = await R2Service.generateSignedUrl(r2Key);
            return await this.downloadFileFromUrl(signedUrl);
          },
        },
      ];

      let lastError: Error | null = null;

      // Try each strategy until one works
      for (const strategy of strategies) {
        try {
          console.log(`Trying ${strategy.name} for document ${documentId}`);
          const result = await strategy.action();
          console.log(`✅ Successfully downloaded using ${strategy.name}`);
          return result;
        } catch (error) {
          console.log(
            `❌ ${strategy.name} failed: ${
              error instanceof Error ? error.message : "Unknown error"
            }`
          );
          lastError =
            error instanceof Error ? error : new Error("Unknown error");
          continue; // Try next strategy
        }
      }

      // If all strategies failed, throw the last error
      throw lastError || new Error("All download strategies failed");
    } catch (error) {
      console.error(`Error downloading document ${documentId}:`, error);
      throw new Error(
        `Failed to download document: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Downloads a file from a URL and returns it as a Buffer
   */
  private static async downloadFileFromUrl(url: string): Promise<Buffer> {
    try {
      const response = await axios({
        method: "GET",
        url: url,
        responseType: "arraybuffer",
        timeout: 30000, // 30 second timeout
        maxContentLength: 50 * 1024 * 1024, // 50MB max file size
      });

      return Buffer.from(response.data);
    } catch (error) {
      console.error(`Error downloading file from ${url}:`, error);
      throw new Error(
        `Failed to download file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Validates if a transaction has any downloadable content
   */
  static async validateTransactionForZip(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<{
    hasContent: boolean;
    documentCount: number;
    canAccess: boolean;
  }> {
    try {
      // Check if transaction exists and user has access
      const transaction = await TransactionService.getTransactionPreview(
        transactionId,
        userId,
        userRole
      );

      if (!transaction) {
        return {
          hasContent: false,
          documentCount: 0,
          canAccess: false,
        };
      }

      // Get document count with error handling
      let documents: any[] = [];
      try {
        documents = await DocumentService.getTransactionDocuments(
          transactionId,
          userId,
          userRole
        );
      } catch (error: any) {
        // If DocumentService throws "Transaction not found", it means user doesn't have access
        if (error.statusCode === 404) {
          return {
            hasContent: false,
            documentCount: 0,
            canAccess: false,
          };
        }
        // Re-throw other errors
        throw error;
      }

      const documentCount = documents ? documents.length : 0;

      // Transaction always has content (at least the PDF), but we check for documents too
      return {
        hasContent: true, // Always true because we can generate PDF
        documentCount,
        canAccess: true,
      };
    } catch (error) {
      console.error("Error validating transaction for ZIP:", error);
      return {
        hasContent: false,
        documentCount: 0,
        canAccess: false,
      };
    }
  }

  /**
   * Gets estimated ZIP file size (rough calculation)
   */
  static async getEstimatedZipSize(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<number> {
    try {
      const documents = await DocumentService.getTransactionDocuments(
        transactionId,
        userId,
        userRole
      );

      // Calculate total document size
      let totalSize = 0;
      if (documents && documents.length > 0) {
        totalSize = documents.reduce(
          (sum, doc) => sum + (doc.fileSize || 0),
          0
        );
      }

      // Add estimated PDF size (typically 50-200KB)
      const estimatedPdfSize = 150 * 1024; // 150KB estimate
      totalSize += estimatedPdfSize;

      // ZIP compression typically reduces size by 10-30%, so we estimate 80% of original size
      const estimatedZipSize = Math.floor(totalSize * 0.8);

      return estimatedZipSize;
    } catch (error) {
      console.error("Error estimating ZIP size:", error);
      return 0;
    }
  }
}
