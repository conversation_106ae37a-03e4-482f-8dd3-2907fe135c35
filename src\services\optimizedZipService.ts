import archiver from "archiver";
import { TransactionService } from "./transactionService";
import { DocumentService } from "./documentService";
import { OptimizedPDFService } from "./optimizedPdfService";
import { R2Service } from "./r2Service";
import { CacheService } from "./cacheService";
import { UserRole } from "@prisma/client";
import axios from "axios";

/**
 * Optimized ZIP Service with parallel processing and caching
 */
export class OptimizedZipService {
  /**
   * Creates an optimized ZIP archive with parallel document downloads
   */
  static async createTransactionZip(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<{ zipBuffer: Buffer; fileName: string }> {
    const startTime = Date.now();
    console.log(
      `🗜️ Starting optimized ZIP creation for transaction ${transactionId}`
    );

    try {
      // Check cache first
      const cacheKey = `zip_${transactionId}_${userId}`;
      const cachedZip = CacheService.get<{
        zipBuffer: Buffer;
        fileName: string;
      }>(cacheKey);

      if (cachedZip) {
        console.log(`📦 Using cached ZIP for transaction ${transactionId}`);
        return cachedZip;
      }

      // Get transaction data and documents in parallel
      const [transaction, documents] = await Promise.all([
        TransactionService.getTransactionPreview(
          transactionId,
          userId,
          userRole
        ),
        DocumentService.getTransactionDocuments(
          transactionId,
          userId,
          userRole
        ),
      ]);

      if (!transaction) {
        throw new Error("Transaction not found or access denied");
      }

      // Generate PDF and download documents in parallel
      const [pdfBuffer, documentBuffers] = await Promise.all([
        OptimizedPDFService.generateTransactionPDF(transaction),
        this.downloadDocumentsInParallel(documents, userId, userRole),
      ]);

      // Create ZIP archive with streaming
      const { zipBuffer, fileName } = await this.createZipArchive(
        transactionId,
        pdfBuffer,
        documentBuffers
      );

      // Cache the result for 30 minutes
      const result = { zipBuffer, fileName };
      CacheService.set(cacheKey, result, 1800);

      const duration = Date.now() - startTime;
      console.log(
        `✅ ZIP creation completed in ${duration}ms for transaction ${transactionId}`
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ ZIP creation failed after ${duration}ms:`, error);
      throw error;
    }
  }

  /**
   * Download documents in parallel with optimized error handling
   */
  private static async downloadDocumentsInParallel(
    documents: any[],
    userId: string,
    userRole: UserRole
  ): Promise<
    Array<{ buffer: Buffer; originalName: string; success: boolean }>
  > {
    if (!documents || documents.length === 0) {
      return [];
    }

    console.log(`📥 Downloading ${documents.length} documents in parallel...`);

    // Process documents in batches to avoid overwhelming the system
    const batchSize = 5;
    const results: Array<{
      buffer: Buffer;
      originalName: string;
      success: boolean;
    }> = [];

    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize);

      const batchPromises = batch.map(async (document) => {
        return this.downloadSingleDocument(document, userId, userRole);
      });

      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        const document = batch[index];
        if (result.status === "fulfilled" && result.value) {
          results.push(result.value);
        } else {
          console.warn(
            `⚠️ Failed to download document: ${document.originalName}`
          );
          // Add placeholder for failed downloads
          results.push({
            buffer: Buffer.from(`Failed to download: ${document.originalName}`),
            originalName: `ERROR_${document.originalName}.txt`,
            success: false,
          });
        }
      });
    }

    const successCount = results.filter((r) => r.success).length;
    console.log(
      `📥 Downloaded ${successCount}/${documents.length} documents successfully`
    );

    return results;
  }

  /**
   * Download a single document with optimized strategies
   */
  private static async downloadSingleDocument(
    document: any,
    userId: string,
    userRole: UserRole
  ): Promise<{
    buffer: Buffer;
    originalName: string;
    success: boolean;
  } | null> {
    const strategies = [
      {
        name: "Document Service",
        action: () =>
          this.downloadViaDocumentService(document.id, userId, userRole),
      },
      {
        name: "R2 Direct",
        action: () => this.downloadViaR2Direct(document),
      },
      {
        name: "Public URL",
        action: () => this.downloadViaPublicUrl(document.filePath),
      },
      {
        name: "Signed URL",
        action: () => this.downloadViaSignedUrl(document),
      },
    ];

    for (const strategy of strategies) {
      try {
        console.log(
          `📥 Trying ${strategy.name} for ${document.originalName}...`
        );
        const buffer = await strategy.action();

        if (buffer && buffer.length > 0) {
          console.log(
            `✅ Successfully downloaded ${document.originalName} via ${strategy.name}`
          );
          return {
            buffer,
            originalName: document.originalName,
            success: true,
          };
        }
      } catch (error) {
        console.log(
          `❌ ${strategy.name} failed for ${document.originalName}:`,
          error instanceof Error ? error.message : String(error)
        );
        continue;
      }
    }

    console.error(
      `❌ All download strategies failed for ${document.originalName}`
    );
    return null;
  }

  /**
   * Download via document service
   */
  private static async downloadViaDocumentService(
    documentId: string,
    userId: string,
    userRole: UserRole
  ): Promise<Buffer> {
    const documentFile = await DocumentService.getDocumentFile(
      documentId,
      userId,
      userRole.toString()
    );

    // DocumentService returns fileUrl, so we need to download from that URL
    return await this.downloadViaPublicUrl(documentFile.fileUrl);
  }

  /**
   * Download via R2 direct access
   */
  private static async downloadViaR2Direct(document: any): Promise<Buffer> {
    const r2Key = document.r2Key || document.fileName;

    if (!r2Key) {
      throw new Error("No R2 key available");
    }

    return await R2Service.downloadFile(r2Key);
  }

  /**
   * Download via public URL
   */
  private static async downloadViaPublicUrl(url: string): Promise<Buffer> {
    if (!url) {
      throw new Error("No public URL available");
    }

    const response = await axios({
      method: "GET",
      url,
      responseType: "arraybuffer",
      timeout: 30000, // 30 second timeout
      maxContentLength: 50 * 1024 * 1024, // 50MB max
    });

    return Buffer.from(response.data);
  }

  /**
   * Download via signed URL
   */
  private static async downloadViaSignedUrl(document: any): Promise<Buffer> {
    const r2Key = document.r2Key || document.fileName;

    if (!r2Key) {
      throw new Error("No R2 key for signed URL");
    }

    const signedUrl = await R2Service.generateSignedUrl(r2Key, 3600);
    return await this.downloadViaPublicUrl(signedUrl);
  }

  /**
   * Create ZIP archive with optimized compression
   */
  private static async createZipArchive(
    transactionId: string,
    pdfBuffer: Buffer,
    documentBuffers: Array<{
      buffer: Buffer;
      originalName: string;
      success: boolean;
    }>
  ): Promise<{ zipBuffer: Buffer; fileName: string }> {
    console.log(`🗜️ Creating ZIP archive for transaction ${transactionId}...`);

    return new Promise((resolve, reject) => {
      const archive = archiver("zip", {
        zlib: { level: 6 }, // Balanced compression (faster than level 9)
        forceLocalTime: true,
      });

      const buffers: Buffer[] = [];

      // Collect ZIP data
      archive.on("data", (chunk) => {
        buffers.push(chunk);
      });

      // Handle archive completion
      archive.on("end", () => {
        const zipBuffer = Buffer.concat(buffers);
        const fileName = `transaction-${transactionId}-${Date.now()}.zip`;

        console.log(
          `✅ ZIP archive created: ${fileName} (${zipBuffer.length} bytes)`
        );
        resolve({ zipBuffer, fileName });
      });

      // Handle errors
      archive.on("error", (err) => {
        console.error("❌ ZIP archive creation failed:", err);
        reject(err);
      });

      // Add transaction PDF
      archive.append(pdfBuffer, {
        name: "transaction-summary.pdf",
        date: new Date(),
      });

      // Add documents
      const successfulDocuments = documentBuffers.filter((doc) => doc.success);
      const failedDocuments = documentBuffers.filter((doc) => !doc.success);

      successfulDocuments.forEach((doc) => {
        archive.append(doc.buffer, {
          name: `documents/${doc.originalName}`,
          date: new Date(),
        });
      });

      // Add error log if there were failures
      if (failedDocuments.length > 0) {
        const errorLog = failedDocuments
          .map((doc) => `Failed to download: ${doc.originalName}`)
          .join("\n");

        archive.append(Buffer.from(errorLog), {
          name: "download-errors.txt",
          date: new Date(),
        });
      }

      // Finalize the archive
      archive.finalize();
    });
  }

  /**
   * Validate transaction for ZIP creation with caching
   */
  static async validateTransactionForZip(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<{ canAccess: boolean; hasContent: boolean }> {
    try {
      // Check cache first
      const cacheKey = `zip_validation_${transactionId}_${userId}`;
      const cachedValidation = CacheService.get<{
        canAccess: boolean;
        hasContent: boolean;
      }>(cacheKey);

      if (cachedValidation) {
        return cachedValidation;
      }

      const [transaction, documents] = await Promise.all([
        TransactionService.getTransactionPreview(
          transactionId,
          userId,
          userRole
        ),
        DocumentService.getTransactionDocuments(
          transactionId,
          userId,
          userRole
        ),
      ]);

      const validation = {
        canAccess: !!transaction,
        hasContent: !!transaction && (documents.length > 0 || true), // Always has PDF
      };

      // Cache validation for 5 minutes
      CacheService.set(cacheKey, validation, 300);

      return validation;
    } catch (error) {
      console.error("❌ ZIP validation failed:", error);
      return { canAccess: false, hasContent: false };
    }
  }

  /**
   * Get estimated ZIP size for logging/monitoring
   */
  static async getEstimatedZipSize(
    transactionId: string,
    userId: string,
    userRole: UserRole
  ): Promise<number> {
    try {
      const documents = await DocumentService.getTransactionDocuments(
        transactionId,
        userId,
        userRole
      );

      // Estimate: PDF (~100KB) + documents (sum of file sizes) * compression ratio (0.7)
      const documentsSize = documents.reduce(
        (sum, doc) => sum + (doc.fileSize || 0),
        0
      );
      const estimatedSize = Math.round((100000 + documentsSize) * 0.7);

      return estimatedSize;
    } catch (error) {
      console.error("❌ Size estimation failed:", error);
      return 1000000; // 1MB default estimate
    }
  }

  /**
   * Clear ZIP cache for a specific transaction
   */
  static clearTransactionZipCache(transactionId: string): void {
    // Get all cache keys and filter for this transaction
    const allKeys = CacheService.getStats().keys;
    // Note: This is a simplified approach - in a real implementation,
    // you'd want a more efficient way to clear related cache entries
    CacheService.flushAll(); // For now, clear all cache
  }

  /**
   * Get ZIP service statistics
   */
  static getZipStats(): {
    cacheHitRate: number;
    averageZipSize: number;
    totalZipsCreated: number;
  } {
    return {
      cacheHitRate: CacheService.getCacheHitRate(),
      averageZipSize: 0, // Would need to track this
      totalZipsCreated: 0, // Would need to track this
    };
  }
}
