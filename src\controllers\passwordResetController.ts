import { Request, Response } from "express";
import { AuthService } from "../services/authService";
import { ResponseHandler } from "../utils/response";
import { asyncHand<PERSON> } from "../middleware/errorHandler";

export class PasswordResetController {
  /**
   * Request password reset - sends reset token via email
   * POST /api/v1/auth/request-password-reset
   */
  static requestPasswordReset = asyncHandler(
    async (req: Request, res: Response) => {
      const { email } = req.body;

      console.log("Password reset request received for:", email);

      const result = await AuthService.requestPasswordReset(email);

      ResponseHandler.success(res, result.message);
    }
  );

  /**
   * Reset password using token
   * POST /api/v1/auth/reset-password
   */
  static resetPassword = asyncHandler(
    async (req: Request, res: Response) => {
      const { token, newPassword } = req.body;

      console.log("Password reset attempt with token:", token.substring(0, 8) + "...");

      const result = await AuthService.resetPassword(token, newPassword);

      ResponseHandler.success(res, result.message);
    }
  );

  /**
   * Validate reset token
   * GET /api/v1/auth/validate-reset-token/:token
   */
  static validateResetToken = asyncHandler(
    async (req: Request, res: Response) => {
      const { token } = req.params;

      console.log("Token validation request for:", token.substring(0, 8) + "...");

      const result = await AuthService.validateResetToken(token);

      if (!result.valid) {
        return ResponseHandler.error(
          res,
          "Invalid or expired reset token",
          undefined,
          400
        );
      }

      ResponseHandler.success(res, "Token is valid", {
        valid: result.valid,
        email: result.email,
      });
    }
  );
}
