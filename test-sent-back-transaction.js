#!/usr/bin/env node

/**
 * Test script specifically for the sent-back transaction 409 error scenario
 * This script helps diagnose issues when account officers try to update/resubmit sent-back transactions
 */

const http = require("http");

const BASE_URL = process.env.API_URL || "http://localhost:8000";
const API_BASE = `${BASE_URL}/api/v1`;

// Test transaction ID from the user's example
const TEST_TRANSACTION_ID = "TXN17540372344587849";
const TEST_MONGO_ID = "688c7bf2c84f9b415f221973";

console.log("🔍 Testing Sent-Back Transaction Scenario...\n");
console.log(`Base URL: ${BASE_URL}`);
console.log(`API Base: ${API_BASE}`);
console.log(`Test Transaction ID: ${TEST_TRANSACTION_ID}`);
console.log(`Test Mongo ID: ${TEST_MONGO_ID}\n`);

// Test scenarios for sent-back transactions
const testScenarios = [
  {
    name: "Get Transaction Details",
    method: "GET",
    path: `/transactions/${TEST_MONGO_ID}`,
    description: "Check current transaction status and stage",
    requiresAuth: true,
  },
  {
    name: "Update Personal Info (Sent-Back)",
    method: "PATCH",
    path: `/transactions/${TEST_MONGO_ID}/personal-info`,
    description: "Test updating personal info on sent-back transaction",
    requiresAuth: true,
    body: {
      firstName: "MUSTAPHA",
      lastName: "AMINU",
      email: "<EMAIL>",
    },
  },
  {
    name: "Submit Transaction (Correct Endpoint)",
    method: "POST",
    path: `/transactions/${TEST_MONGO_ID}/submit`,
    description:
      "Test resubmitting sent-back transaction using correct endpoint",
    requiresAuth: true,
  },
  {
    name: "Wrong: Approval Endpoint (Should Fail)",
    method: "POST",
    path: `/approvals/${TEST_MONGO_ID}`,
    description: "Test using approval endpoint (should give clear error)",
    requiresAuth: true,
    body: {
      action: "APPROVE",
    },
    expectError: true,
  },
];

async function makeRequest(scenario, authToken = null) {
  return new Promise((resolve) => {
    const url = new URL(scenario.path, API_BASE);

    const headers = {
      "Content-Type": "application/json",
      "User-Agent": "AMS-SentBack-Test/1.0",
    };

    if (authToken && scenario.requiresAuth) {
      headers["Authorization"] = `Bearer ${authToken}`;
    }

    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === "https:" ? 443 : 8000),
      path: url.pathname + url.search,
      method: scenario.method,
      headers,
      timeout: 10000,
    };

    const protocol =
      url.protocol === "https:" ? require("https") : require("http");

    const req = protocol.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            scenario: scenario.name,
            status: res.statusCode,
            success: scenario.expectError
              ? res.statusCode >= 400
              : res.statusCode >= 200 && res.statusCode < 400,
            data: jsonData,
            headers: res.headers,
            rawData: data,
          });
        } catch (error) {
          resolve({
            scenario: scenario.name,
            status: res.statusCode,
            success: scenario.expectError
              ? res.statusCode >= 400
              : res.statusCode >= 200 && res.statusCode < 400,
            data: null,
            error: "Invalid JSON response",
            rawData: data.substring(0, 500) + (data.length > 500 ? "..." : ""),
          });
        }
      });
    });

    req.on("error", (error) => {
      resolve({
        scenario: scenario.name,
        status: "ERROR",
        success: false,
        error: error.message,
      });
    });

    req.on("timeout", () => {
      req.destroy();
      resolve({
        scenario: scenario.name,
        status: "TIMEOUT",
        success: false,
        error: "Request timed out",
      });
    });

    if (scenario.body) {
      req.write(JSON.stringify(scenario.body));
    }

    req.end();
  });
}

async function runTests() {
  console.log("🧪 Running Sent-Back Transaction Tests...\n");

  console.log("Scenario".padEnd(35) + " | Status | Result | Description");
  console.log("-".repeat(90));

  const results = [];

  // Note: These tests will fail without proper authentication
  // In a real scenario, you would need to provide a valid JWT token

  for (const scenario of testScenarios) {
    const result = await makeRequest(scenario);
    results.push(result);

    const statusColor =
      result.status === 409
        ? "🔴"
        : result.status === 401 || result.status === 403
        ? "🔐"
        : result.success
        ? "🟢"
        : "🟡";
    const statusText = result.status.toString().padEnd(6);
    const resultText = result.success
      ? "PASS"
      : result.status === 409
      ? "409!"
      : result.status === 401
      ? "AUTH"
      : result.status === 403
      ? "PERM"
      : "FAIL";

    console.log(
      `${scenario.name.padEnd(
        35
      )} | ${statusText} | ${statusColor} ${resultText.padEnd(4)} | ${
        scenario.description
      }`
    );

    // Show detailed information for important responses
    if (result.status === 409) {
      console.log(`   🔍 409 Conflict Details:`);
      console.log(`      Message: ${result.data?.message || "No message"}`);
      console.log(`      Error: ${result.data?.error || "No error details"}`);
      if (result.data?.data) {
        console.log(`      Data: ${JSON.stringify(result.data.data, null, 8)}`);
      }
    } else if (result.status === 401 || result.status === 403) {
      console.log(
        `   🔐 Auth Issue: ${result.data?.message || "Authentication required"}`
      );
    } else if (result.data && result.data.success === false) {
      console.log(`   ❌ Error: ${result.data.message || "Unknown error"}`);
    } else if (
      result.data &&
      result.data.success === true &&
      scenario.name === "Get Transaction Details"
    ) {
      console.log(
        `   ✅ Transaction Status: ${result.data.data?.status || "Unknown"}`
      );
      console.log(
        `   ✅ Current Stage: ${result.data.data?.currentStage || "Unknown"}`
      );
    }

    // Small delay between requests
    await new Promise((resolve) => setTimeout(resolve, 200));
  }

  console.log("\n📊 Test Summary:");
  console.log("-".repeat(50));

  const successful = results.filter((r) => r.success).length;
  const conflicts = results.filter((r) => r.status === 409).length;
  const authIssues = results.filter(
    (r) => r.status === 401 || r.status === 403
  ).length;
  const failed = results.filter(
    (r) =>
      !r.success && r.status !== 409 && r.status !== 401 && r.status !== 403
  ).length;

  console.log(`✅ Successful: ${successful}`);
  console.log(`🔴 409 Conflicts: ${conflicts}`);
  console.log(`🔐 Auth Issues: ${authIssues}`);
  console.log(`❌ Other Failures: ${failed}`);

  if (authIssues > 0) {
    console.log("\n🔐 Authentication Required:");
    console.log("-".repeat(40));
    console.log("Most tests require authentication. To run with auth:");
    console.log("1. Login to get a JWT token");
    console.log(
      "2. Set the token in the script or pass as environment variable"
    );
    console.log(
      "3. Make sure you have ACCOUNT_OFFICER role for transaction operations"
    );
  }

  if (conflicts > 0) {
    console.log("\n🔴 409 Conflict Analysis:");
    console.log("-".repeat(40));
    results
      .filter((r) => r.status === 409)
      .forEach((r) => {
        console.log(`❌ ${r.scenario}:`);
        console.log(`   Message: ${r.data?.message || "No message"}`);
        console.log(
          `   Analysis: This is the expected behavior for the approval endpoint`
        );
        console.log("");
      });
  }

  console.log("\n💡 Sent-Back Transaction Workflow:");
  console.log("-".repeat(50));
  console.log(
    "1. Transaction is sent back by approver (status: SENT_BACK, stage: ACCOUNT_OFFICER)"
  );
  console.log(
    "2. Account officer can UPDATE transaction details using PATCH endpoints"
  );
  console.log(
    "3. Account officer RESUBMITS using PUT /transactions/{id}/submit"
  );
  console.log(
    "4. Account officer should NOT use POST /approvals/{id} (will get 409 error)"
  );

  console.log("\n🛠️  Debugging the Original Issue:");
  console.log("-".repeat(50));
  console.log("Based on your transaction data:");
  console.log(`- Transaction ID: ${TEST_TRANSACTION_ID}`);
  console.log("- Status: SENT_BACK ✅ (correct)");
  console.log("- Current Stage: ACCOUNT_OFFICER ✅ (correct)");
  console.log("- BVN: ***********");
  console.log("");
  console.log("Possible causes of 409 error:");
  console.log(
    "1. Frontend calling approval endpoint instead of submission endpoint"
  );
  console.log(
    "2. BVN uniqueness conflict (check if BVN exists in other transactions)"
  );
  console.log("3. Transaction ownership issue (wrong user trying to edit)");
  console.log("4. Concurrent modification by multiple users");

  console.log("\n🔗 Correct Endpoints for Account Officers:");
  console.log("-".repeat(50));
  console.log(
    `✅ Update Personal Info: PATCH ${API_BASE}/transactions/{id}/personal-info`
  );
  console.log(
    `✅ Update Next of Kin: PATCH ${API_BASE}/transactions/{id}/next-of-kin`
  );
  console.log(
    `✅ Update Loan Info: PATCH ${API_BASE}/transactions/{id}/loan-info`
  );
  console.log(
    `✅ Resubmit Transaction: PUT ${API_BASE}/transactions/{id}/submit`
  );
  console.log(`❌ Approval (Wrong): POST ${API_BASE}/approvals/{id}`);

  console.log("\n🔍 Next Steps:");
  console.log("-".repeat(30));
  console.log(
    "1. Check frontend code to ensure correct endpoints are being called"
  );
  console.log("2. Verify BVN uniqueness in database");
  console.log("3. Check server logs for detailed error information");
  console.log("4. Test with proper authentication token");
  console.log("5. Monitor network requests in browser dev tools");
}

// Check if server is running first
console.log(`🔍 Checking server connectivity at ${BASE_URL}...`);

const serverCheck = http.request(
  {
    hostname: new URL(BASE_URL).hostname,
    port: new URL(BASE_URL).port || 8000,
    path: "/api/v1/health",
    method: "GET",
    timeout: 5000,
  },
  (res) => {
    console.log(`✅ Server is responding (Status: ${res.statusCode})\n`);
    runTests();
  }
);

serverCheck.on("error", (error) => {
  console.error(`❌ Cannot connect to server at ${BASE_URL}`);
  console.error(`   Error: ${error.message}`);
  console.error(`   Make sure the server is running with: npm run dev`);
  process.exit(1);
});

serverCheck.on("timeout", () => {
  console.error(`❌ Server connection timed out at ${BASE_URL}`);
  console.error(`   Make sure the server is running with: npm run dev`);
  process.exit(1);
});

serverCheck.end();
