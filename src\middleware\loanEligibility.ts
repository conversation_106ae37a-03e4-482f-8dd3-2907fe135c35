import { Request, Response, NextFunction } from "express";
import { LoanEligibilityService } from "../services/loanEligibilityService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";

/**
 * Middleware to check loan eligibility based on BVN before allowing loan application creation
 */
export const checkLoanEligibility = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { bvn } = req.body;

    // BVN is required for eligibility check
    if (!bvn) {
      ResponseHandler.error(
        res,
        "BVN is required for loan eligibility validation",
        undefined,
        400
      );
      return;
    }

    // Validate BVN format (should be 11 digits)
    if (!/^\d{11}$/.test(bvn)) {
      ResponseHandler.error(
        res,
        "BVN must be exactly 11 digits",
        undefined,
        400
      );
      return;
    }

    // Check loan eligibility
    const eligibilityResult =
      await LoanEligibilityService.checkLoanEligibilityByBVN(bvn);

    if (!eligibilityResult.isEligible) {
      ResponseHandler.error(
        res,
        eligibilityResult.reason ||
          "You are not eligible for a new loan at this time",
        JSON.stringify({
          activeLoan: eligibilityResult.activeLoan,
          eligibilityDate: eligibilityResult.eligibilityDate,
        }),
        400
      );
      return;
    }

    // User is eligible, proceed to next middleware/controller
    next();
  } catch (error) {
    console.error("Loan eligibility check error:", error);
    ResponseHandler.serverError(res, "Error checking loan eligibility");
  }
};

/**
 * Middleware to validate BVN format in request body
 */
export const validateBVN = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const { bvn } = req.body;

  if (!bvn) {
    ResponseHandler.error(res, "BVN is required", undefined, 400);
    return;
  }

  if (typeof bvn !== "string" || !/^\d{11}$/.test(bvn)) {
    ResponseHandler.error(res, "BVN must be exactly 11 digits", undefined, 400);
    return;
  }

  next();
};

/**
 * Middleware to check loan eligibility during personal info update (when BVN is provided)
 */
export const checkLoanEligibilityOnPersonalInfoUpdate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { bvn } = req.body;

    // Only check eligibility if BVN is being updated
    if (bvn) {
      // Validate BVN format
      if (!/^\d{11}$/.test(bvn)) {
        ResponseHandler.error(
          res,
          "BVN must be exactly 11 digits",
          undefined,
          400
        );
        return;
      }

      // Check if this is a new transaction or if BVN is being changed
      const { transactionId } = req.params;

      // For existing transactions, we'll validate during submission
      // For now, just validate format and proceed
    }

    next();
  } catch (error) {
    console.error("BVN validation error:", error);
    ResponseHandler.serverError(res, "Error validating BVN");
  }
};
