import { Router } from "express";
import { CleanupController } from "../controllers/cleanupController";
import { authenticate, authorize } from "../middleware/auth";
import { UserRole } from "@prisma/client";

const router = Router();

// Apply authentication to all cleanup routes
router.use(authenticate);

// Apply authorization - only SUPER_ADMIN can access cleanup endpoints
router.use(authorize(UserRole.SUPER_ADMIN));

/**
 * @swagger
 * /api/v1/cleanup/stats:
 *   get:
 *     summary: Get database cleanup statistics
 *     description: |
 *       Retrieve statistics about data that would be cleaned up, including:
 *       - Number of OTP codes eligible for cleanup (used or expired)
 *       - Number of notifications older than retention period
 *       - Oldest records in each table
 *       - Summary recommendations
 *
 *       **Access:** SUPER_ADMIN only
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Cleanup statistics retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     otpCodesToCleanup:
 *                       type: integer
 *                       example: 150
 *                     notificationsToCleanup:
 *                       type: integer
 *                       example: 2500
 *                     oldestOtpCode:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T10:30:00.000Z"
 *                     oldestNotification:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-12-01T08:15:00.000Z"
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalRecordsToCleanup:
 *                           type: integer
 *                           example: 2650
 *                         hasDataToCleanup:
 *                           type: boolean
 *                           example: true
 *       403:
 *         description: Access denied - SUPER_ADMIN role required
 *       401:
 *         description: Authentication required
 */
router.get("/stats", CleanupController.getCleanupStats);

/**
 * @swagger
 * /api/v1/cleanup/status:
 *   get:
 *     summary: Get cleanup job status
 *     description: |
 *       Get the current status of scheduled cleanup jobs, including:
 *       - Whether jobs are initialized and running
 *       - Number of active cron jobs
 *       - Configuration settings (retention periods, batch sizes)
 *       - Timezone settings
 *
 *       **Access:** SUPER_ADMIN only
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Job status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Cleanup job status retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     isInitialized:
 *                       type: boolean
 *                       example: true
 *                     activeJobs:
 *                       type: integer
 *                       example: 3
 *                     configuration:
 *                       type: object
 *                       properties:
 *                         otpRetentionHours:
 *                           type: integer
 *                           example: 1
 *                         notificationRetentionDays:
 *                           type: integer
 *                           example: 30
 *                         batchSize:
 *                           type: integer
 *                           example: 1000
 *                         timezone:
 *                           type: string
 *                           example: "UTC"
 */
router.get("/status", CleanupController.getJobStatus);

/**
 * @swagger
 * /api/v1/cleanup/dashboard:
 *   get:
 *     summary: Get comprehensive cleanup dashboard
 *     description: |
 *       Get comprehensive dashboard data combining statistics, job status, and recommendations.
 *       This endpoint provides all the information needed for a cleanup management interface.
 *
 *       **Access:** SUPER_ADMIN only
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     statistics:
 *                       type: object
 *                       description: Cleanup statistics
 *                     jobStatus:
 *                       type: object
 *                       description: Current job status
 *                     recommendations:
 *                       type: object
 *                       properties:
 *                         shouldRunOtpCleanup:
 *                           type: boolean
 *                           example: true
 *                         shouldRunNotificationCleanup:
 *                           type: boolean
 *                           example: false
 *                         oldestDataAge:
 *                           type: object
 *                           properties:
 *                             otpDays:
 *                               type: integer
 *                               example: 5
 *                             notificationDays:
 *                               type: integer
 *                               example: 45
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 */
router.get("/dashboard", CleanupController.getCleanupDashboard);

/**
 * @swagger
 * /api/v1/cleanup/manual/otp:
 *   post:
 *     summary: Trigger manual OTP cleanup
 *     description: |
 *       Manually trigger cleanup of OTP codes. This will:
 *       - Delete all used OTP codes
 *       - Delete all unused OTP codes older than retention period (default: 1 hour)
 *       - Process in batches to avoid database locks
 *       - Return detailed execution statistics
 *
 *       **Access:** SUPER_ADMIN only
 *       **Use Case:** Emergency cleanup or testing
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: OTP cleanup completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "OTP cleanup completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     recordsDeleted:
 *                       type: integer
 *                       example: 150
 *                     executionTime:
 *                       type: integer
 *                       description: Execution time in milliseconds
 *                       example: 2500
 *       500:
 *         description: OTP cleanup failed
 */
router.post("/manual/otp", CleanupController.manualOtpCleanup);

/**
 * @swagger
 * /api/v1/cleanup/manual/notifications:
 *   post:
 *     summary: Trigger manual notification cleanup
 *     description: |
 *       Manually trigger cleanup of old notifications. This will:
 *       - Delete all notifications older than retention period (default: 30 days)
 *       - Process in batches to avoid database locks
 *       - Return detailed execution statistics
 *
 *       **Access:** SUPER_ADMIN only
 *       **Use Case:** Emergency cleanup or testing
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Notification cleanup completed successfully
 *       500:
 *         description: Notification cleanup failed
 */
router.post(
  "/manual/notifications",
  CleanupController.manualNotificationCleanup
);

/**
 * @swagger
 * /api/v1/cleanup/manual/password-reset:
 *   post:
 *     summary: Trigger manual password reset token cleanup
 *     description: |
 *       Manually trigger cleanup of password reset tokens. This will:
 *       - Delete all used password reset tokens
 *       - Delete all unused password reset tokens older than retention period (default: 1 hour)
 *       - Process in batches to avoid database locks
 *       - Return detailed execution statistics
 *
 *       **Access:** SUPER_ADMIN only
 *       **Use Case:** Emergency cleanup or testing
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Password reset token cleanup completed successfully
 *       500:
 *         description: Password reset token cleanup failed
 */
router.post(
  "/manual/password-reset",
  CleanupController.manualPasswordResetCleanup
);

/**
 * @swagger
 * /api/v1/cleanup/manual/full:
 *   post:
 *     summary: Trigger manual full cleanup
 *     description: |
 *       Manually trigger full database cleanup (both OTP codes and notifications).
 *       This runs both cleanup operations sequentially and returns combined statistics.
 *
 *       **Access:** SUPER_ADMIN only
 *       **Use Case:** Complete system maintenance
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Full cleanup completed successfully
 *       500:
 *         description: Some cleanup operations failed
 */
router.post("/manual/full", CleanupController.manualFullCleanup);

/**
 * @swagger
 * /api/v1/cleanup/manual/selective:
 *   post:
 *     summary: Trigger selective manual cleanup
 *     description: |
 *       Manually trigger selective cleanup with options to include/exclude specific operations.
 *       Allows fine-grained control over which cleanup operations to run.
 *
 *       **Access:** SUPER_ADMIN only
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               includeOtp:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include OTP cleanup
 *               includeNotifications:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include notification cleanup
 *               includePasswordReset:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include password reset token cleanup
 *     responses:
 *       200:
 *         description: Selective cleanup completed successfully
 *       500:
 *         description: Some cleanup operations failed
 */
router.post("/manual/selective", CleanupController.manualSelectiveCleanup);

/**
 * @swagger
 * /api/v1/cleanup/jobs/start:
 *   post:
 *     summary: Initialize/restart cleanup jobs
 *     description: |
 *       Initialize or restart the scheduled cleanup jobs. This will:
 *       - Start OTP cleanup job (runs hourly)
 *       - Start notification cleanup job (runs daily at 2 AM)
 *       - Start full cleanup job (runs weekly on Sunday at 3 AM)
 *
 *       **Access:** SUPER_ADMIN only
 *       **Use Case:** System maintenance or recovery
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup jobs initialized successfully
 *       500:
 *         description: Failed to initialize cleanup jobs
 */
router.post("/jobs/start", CleanupController.initializeJobs);

/**
 * @swagger
 * /api/v1/cleanup/jobs/stop:
 *   post:
 *     summary: Stop cleanup jobs
 *     description: |
 *       Stop all scheduled cleanup jobs. Use this for maintenance or troubleshooting.
 *       Jobs can be restarted using the /jobs/start endpoint.
 *
 *       **Access:** SUPER_ADMIN only
 *       **Use Case:** System maintenance or troubleshooting
 *     tags: [Database Cleanup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup jobs stopped successfully
 *       500:
 *         description: Failed to stop cleanup jobs
 */
router.post("/jobs/stop", CleanupController.stopJobs);

export default router;
