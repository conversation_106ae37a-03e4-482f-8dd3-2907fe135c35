# AMS Loan Management System - Backend API

A comprehensive loan management system built with Node.js, Express, TypeScript, Prisma, and PostgreSQL. This system handles the complete loan application workflow from creation to approval and disbursement.

## 🚀 Features

### Core Features

- **Loan Type Selection System**

  - 4 Loan Types: Consumer Loan Public/Private, SME Individual/Corporate
  - Dynamic form fields based on selected loan type
  - Backward compatibility with existing transactions

- **Multi-step Transaction Creation** (5 steps)

  - Loan Type Selection
  - Personal Information (dynamic fields)
  - Next of Kin Details
  - Loan Information
  - Disbursement Details
  - Document Upload

- **Transaction Preview & PDF Generation**

  - Preview transactions in draft and submitted states
  - Role-based access control for viewing
  - PDF download functionality with loan type-specific formatting

- **Role-based Approval Workflow**

  - Account Officer → Supervisor → Head Consumer Lending → Head Risk Management → Managing Director → Accountant
  - Approval thresholds (amounts below 1M can skip Managing Director)
  - Send back functionality with notifications

- **User Management**

  - Role-based access control
  - OTP-based authentication
  - Session management with timeout

- **Document Management**

  - Multiple file upload support (PDF, images, documents)
  - Custom metadata support for document categorization
  - File validation and security
  - Download and preview capabilities

- **Dashboard & Analytics**

  - Individual performance metrics for Account Officers
  - System-wide statistics for administrators
  - Transaction trends and approval metrics

- **Enhanced Notification System**
  - Real-time notifications for workflow changes
  - **Bulk email notifications for approval workflow**
  - Professional email templates with transaction details
  - Role-based email notifications to all relevant approvers
  - In-app notification management

## 🏗️ Architecture

### Tech Stack

- **Backend**: Node.js, Express.js, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with OTP verification
- **File Upload**: Multer
- **Email**: Nodemailer
- **Validation**: Zod
- **Security**: Helmet, Rate Limiting, CORS

### Project Structure

```
src/
├── controllers/          # Request handlers
├── services/            # Business logic
├── middleware/          # Custom middleware
├── routes/             # API routes
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── db/                 # Database configuration
└── scripts/            # Database seeds and utilities
```

## 🛠️ Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL database
- npm or yarn package manager

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd ams-loan-system
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**

   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your configuration:

   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/ams_loan_db"
   JWT_SECRET="your_super_secret_jwt_key_here"
   SMTP_HOST="smtp.gmail.com"
   SMTP_USER="<EMAIL>"
   SMTP_PASS="your_app_password"

   # R2 (Cloudflare R2) Configuration - Required for file uploads
   R2_ACCESS_KEY_ID="your_r2_access_key_id"
   R2_SECRET_ACCESS_KEY="your_r2_secret_access_key"
   R2_ENDPOINT="https://your-account-id.r2.cloudflarestorage.com"
   R2_BUCKET_NAME="your-bucket-name"
   R2_REGION="auto"
   R2_PUBLIC_URL="https://your-custom-domain.com"

   # ... other configurations
   ```

4. **Database Setup**

   ```bash
   # Generate Prisma client
   npm run db:generate

   # Push database schema
   npm run db:push

   # Seed initial data
   npm run db:seed
   ```

5. **R2 Storage Setup (Required for File Uploads)**

   The system uses Cloudflare R2 for file storage. To set up R2:

   a. **Create a Cloudflare R2 bucket:**

   - Go to Cloudflare Dashboard → R2 Object Storage
   - Create a new bucket
   - Note the bucket name

   b. **Create R2 API tokens:**

   - Go to Cloudflare Dashboard → My Profile → API Tokens
   - Create a custom token with R2:Object:Edit permissions
   - Note the Access Key ID and Secret Access Key

   c. **Configure R2 in your `.env` file:**

   ```env
   R2_ACCESS_KEY_ID="your_access_key_id"
   R2_SECRET_ACCESS_KEY="your_secret_access_key"
   R2_ENDPOINT="https://your-account-id.r2.cloudflarestorage.com"
   R2_BUCKET_NAME="your-bucket-name"
   R2_REGION="auto"
   R2_PUBLIC_URL="https://your-custom-domain.com"  # Optional: custom domain
   ```

   **⚠️ Important:** Without proper R2 configuration, file uploads and document access will fail with authorization errors.

6. **Email Configuration (Required for Authentication Features)**

   The system sends email notifications for authentication purposes (password reset, OTP verification):

   ```env
   # Email Configuration (for authentication emails only)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your_app_password
   FROM_EMAIL=<EMAIL>
   FROM_NAME="AMS Loan System"

   # Frontend URL (for password reset links)
   FRONTEND_URL="http://localhost:3000"
   ```

   **Email Features:**

   - Password reset request emails with secure tokens
   - Password reset confirmation emails
   - OTP verification emails for two-factor authentication
   - Professional HTML email templates
   - Error handling for failed email deliveries

   **Note:** Transaction workflow emails (approval notifications, submission alerts, etc.) have been disabled. The system only sends authentication-related emails. All transaction notifications are handled through in-app notifications.

7. **Start the development server**
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:8000`

## 📚 API Documentation

### Base URL

```
http://localhost:8000/api/v1
```

### Authentication

All protected endpoints require a Bearer token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Key Endpoints

#### Authentication

- `POST /auth/login` - Login with email/password
- `POST /auth/verify-otp` - Verify OTP and get tokens
- `POST /auth/refresh-token` - Refresh access token
- `GET /auth/profile` - Get user profile

#### Transactions

- `GET /transactions/loan-types` - Get available loan types
- `POST /transactions` - Create new transaction
- `GET /transactions` - Get transactions (with filters)
- `GET /transactions/:id` - Get specific transaction
- `PATCH /transactions/:id/loan-type` - Set loan type for transaction
- `PATCH /transactions/:id/personal-info` - Update personal info (dynamic validation)
- `PATCH /transactions/:id/next-of-kin` - Update next of kin
- `PATCH /transactions/:id/loan-info` - Update loan information
- `PATCH /transactions/:id/disbursement` - Update disbursement info
- `POST /transactions/:id/submit` - Submit transaction for approval
- `GET /transactions/:id/preview` - Get transaction preview
- `GET /transactions/:id/pdf` - Download transaction PDF

#### Approvals

- `GET /approvals/pending` - Get transactions pending approval
- `POST /approvals/:id/process` - Approve/reject/send back transaction
- `GET /approvals/:id/history` - Get approval history

#### Documents

- `POST /documents/:transactionId/upload` - Upload document (with optional metadata)
- `POST /documents/:transactionId/upload-multiple` - Upload multiple documents (with optional metadata)
- `GET /documents/:transactionId` - Get transaction documents (includes metadata)
- `GET /documents/download/:documentId` - Download document
- `DELETE /documents/:documentId` - Delete document

#### Dashboard

- `GET /dashboard/stats` - Get user dashboard statistics
- `GET /dashboard/my-transactions` - Get my transactions with filtering
- `GET /dashboard/my-transactions/stats` - Get my transaction statistics
- `GET /dashboard/account-officers` - Get account officers list (for filtering)
- `GET /dashboard/system-overview` - Get system overview (admin)
- `GET /dashboard/transaction-trends` - Get transaction trends (admin)

#### Notifications

- `GET /notifications` - Get user notifications
- `PATCH /notifications/:id/read` - Mark notification as read
- `PATCH /notifications/mark-all-read` - Mark all as read

## 👥 User Roles & Permissions

### Account Officer

- Create and edit transactions
- View own transactions and performance metrics
- Upload documents
- Submit transactions for approval

### Supervisor

- Review and approve/reject transactions from Account Officers
- View transactions in their approval queue
- Send transactions back for corrections

### Head Consumer Lending

- Approve transactions from Supervisors
- View lending-related analytics

### Head Risk Management

- Final approval for transactions under 1M
- Risk assessment and approval
- Can approve transactions up to threshold

### Managing Director

- Required approval for transactions over 1M
- System-wide oversight
- Access to all analytics

### Accountant

- Final verification before disbursement
- Financial validation
- Disbursement processing

### Super Admin

- User management
- System configuration
- Full system access

## 🔄 Transaction Workflow

1. **Creation** (Account Officer)

   - Create transaction draft
   - Fill 5-step form (Personal Info → Next of Kin → Loan Info → Disbursement → Documents)
   - Submit for approval

2. **Approval Process**

   - Supervisor review
   - Head Consumer Lending approval
   - Head Risk Management approval
   - Managing Director approval (if amount > 1M)
   - Accountant final verification

3. **Actions Available**

   - **Approve**: Move to next stage
   - **Reject**: End process with reason
   - **Send Back**: Return to Account Officer for corrections

4. **Notifications**
   - Email and in-app notifications at each stage
   - All previous approvers notified when sent back

## 🗄️ Database Schema

### Key Models

- **User**: System users with roles
- **Transaction**: Loan applications with 5-step data
- **TransactionApproval**: Approval history and actions
- **Document**: Uploaded files linked to transactions
- **Notification**: System notifications
- **OtpCode**: OTP verification codes

### Relationships

- Users create multiple Transactions
- Transactions have multiple Approvals and Documents
- Users receive multiple Notifications

## 🔒 Security Features

- **JWT Authentication** with refresh tokens
- **OTP Verification** for login
- **Rate Limiting** on sensitive endpoints
- **File Upload Validation** with size and type restrictions
- **Role-based Access Control**
- **Session Timeout** (1 hour default)
- **Password Hashing** with bcrypt
- **CORS Protection**
- **Helmet Security Headers**

## 📊 Performance Features

- **Database Indexing** on frequently queried fields
- **Pagination** for large data sets
- **File Size Limits** to prevent abuse
- **Connection Pooling** with Prisma
- **Error Handling** with proper HTTP status codes

## 🧪 Testing

```bash
# Run type checking
npm run lint

# Run tests (when implemented)
npm test
```

## 📦 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed initial data
npm run db:studio    # Open Prisma Studio
npm run lint         # Type checking
```

## 🔧 Default Users (After Seeding)

| Role              | Email                    | Password           |
| ----------------- | ------------------------ | ------------------ |
| Super Admin       | <EMAIL>        | admin123456        |
| Account Officer   | <EMAIL>      | officer123456      |
| Supervisor        | <EMAIL>   | supervisor123456   |
| Head Consumer     | <EMAIL> | headconsumer123456 |
| Head Risk         | <EMAIL>     | headrisk123456     |
| Managing Director | <EMAIL>     | director123456     |
| Accountant        | <EMAIL>   | accountant123456   |

⚠️ **Important**: Change these default passwords in production!

## 🚀 Deployment

### Environment Variables for Production

```env
NODE_ENV=production
DATABASE_URL=your_production_database_url
JWT_SECRET=your_production_jwt_secret
SMTP_HOST=your_production_smtp_host
# ... other production configs
```

### Build and Deploy

```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Built with ❤️ for efficient loan management**
