import { Router } from "express";
import { DocumentController } from "../controllers/documentController";
import { authenticate, requireAccountOfficer } from "../middleware/auth";
import { upload } from "../utils/fileUpload";
import { uploadLimiter, documentLimiter } from "../middleware/rateLimiter";
import { validateBody } from "../middleware/validation";
import { documentUploadMetadataSchema } from "../utils/validation";

const router = Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/documents/download/{documentId}:
 *   get:
 *     summary: Download a document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document file
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Document not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/download/:documentId",
  documentLimiter,
  DocumentController.downloadDocument
);

/**
 * @swagger
 * /api/v1/documents/{documentId}/signed-url:
 *   get:
 *     summary: Generate a signed URL for document access
 *     description: |
 *       Generate a time-limited signed URL for secure document access.
 *       The URL expires after the specified time (default 1 hour, max 24 hours).
 *
 *       **Security Features:**
 *       - Role-based access control (account officers can only access their own documents)
 *       - Configurable expiration time with maximum limit
 *       - Audit logging for access attempts
 *       - Rate limiting to prevent abuse
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *         description: Document ID (MongoDB ObjectID format)
 *         example: '507f1f77bcf86cd799439011'
 *       - in: query
 *         name: expiresIn
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 300
 *           maximum: 86400
 *           default: 3600
 *         description: URL expiration time in seconds (5 minutes to 24 hours)
 *         example: 3600
 *     responses:
 *       200:
 *         description: Signed URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Signed URL generated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     signedUrl:
 *                       type: string
 *                       format: uri
 *                       description: Time-limited signed URL for document access
 *                       example: "https://r2.example.com/documents/file.pdf?signature=..."
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                       description: ISO timestamp when the URL expires
 *                       example: "2024-01-15T10:30:00.000Z"
 *                     documentId:
 *                       type: string
 *                       description: Document ID
 *                       example: "507f1f77bcf86cd799439011"
 *                     fileName:
 *                       type: string
 *                       description: Original filename
 *                       example: "passport_copy.pdf"
 *       400:
 *         description: Bad request (invalid document ID or expiration time)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden (insufficient permissions)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Document not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       429:
 *         description: Too many requests
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/:documentId/signed-url",
  uploadLimiter,
  DocumentController.getDocumentSignedUrl
);

/**
 * @swagger
 * /api/v1/documents/{transactionId}/upload:
 *   post:
 *     summary: Upload a single document with optional metadata
 *     description: |
 *       Upload a single document file with optional metadata for categorization.
 *
 *       **Metadata Format:**
 *       - Can be JSON string or plain text
 *       - Maximum 1000 characters
 *       - Optional field
 *
 *       **Metadata Examples:**
 *       - JSON: `{"category": "identity", "documentType": "passport"}`
 *       - Plain text: `"Identity document - Passport"`
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               document:
 *                 type: string
 *                 format: binary
 *                 description: Document file to upload
 *               metadata:
 *                 type: string
 *                 description: Optional metadata for document categorization (JSON string or plain text, max 1000 characters)
 *                 example: '{"category": "identity", "documentType": "passport", "description": "Customer passport copy"}'
 *             required:
 *               - document
 *     responses:
 *       201:
 *         description: Document uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DocumentUploadResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/:transactionId/upload",
  requireAccountOfficer,
  uploadLimiter,
  upload.single("document"), // Single file upload
  validateBody(documentUploadMetadataSchema),
  DocumentController.uploadDocument
);

/**
 * @swagger
 * /api/v1/documents/{transactionId}/upload-multiple:
 *   post:
 *     summary: Upload multiple documents with optional metadata
 *     description: |
 *       Upload multiple document files (max 10) with optional metadata array for categorization.
 *
 *       **Metadata Format:**
 *       - JSON string array where each element corresponds to a file
 *       - Each metadata element can be JSON string or plain text
 *       - Maximum 1000 characters per metadata element
 *       - Optional field
 *
 *       **Metadata Array Example:**
 *       ```json
 *       [
 *         "{\"category\": \"identity\", \"documentType\": \"passport\"}",
 *         "{\"category\": \"financial\", \"documentType\": \"bank_statement\"}"
 *       ]
 *       ```
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Document files to upload (max 10)
 *               metadata:
 *                 type: string
 *                 description: Optional metadata array for document categorization (JSON string array)
 *                 example: '["{\\"category\\": \\"identity\\", \\"documentType\\": \\"passport\\"}", "{\\"category\\": \\"financial\\", \\"documentType\\": \\"bank_statement\\"}"]'
 *             required:
 *               - documents
 *     responses:
 *       201:
 *         description: Documents uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MultipleDocumentUploadResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/:transactionId/upload-multiple",
  requireAccountOfficer,
  uploadLimiter,
  upload.array("documents", 10), // Multiple files upload
  validateBody(documentUploadMetadataSchema),
  DocumentController.uploadDocumentFlexible
);

/**
 * @swagger
 * /api/v1/documents/{transactionId}/stats:
 *   get:
 *     summary: Get document statistics for a transaction
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Document statistics
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/:transactionId/stats",
  documentLimiter,
  DocumentController.getDocumentStats
);

/**
 * @swagger
 * /api/v1/documents/{transactionId}:
 *   get:
 *     summary: Get all documents for a transaction
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: List of transaction documents with metadata
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DocumentListResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Transaction not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/:transactionId",
  documentLimiter,
  DocumentController.getTransactionDocuments
);

/**
 * @swagger
 * /api/v1/documents/{documentId}:
 *   delete:
 *     summary: Delete a document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Document not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete(
  "/:documentId",
  requireAccountOfficer,
  DocumentController.deleteDocument
);

export default router;
