import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { StreamingExportService } from '../services/streamingExportService';
import { UserRole } from '@prisma/client';

if (isMainThread) {
  // This file is being imported, not run as a worker
  throw new Error('This file should only be run as a worker thread');
}

interface WorkerData {
  userRole: UserRole;
  userId: string;
  filters: {
    accountOfficerId?: string;
    accountOfficerIds?: string[];
    loanType?: string;
    dateFrom?: string;
    dateTo?: string;
    search?: string;
  };
  taskType: 'export' | 'cleanup';
  maxAgeHours?: number;
}

async function runExportTask() {
  try {
    console.log('🔄 Worker thread started for XLSX export');
    
    const data = workerData as WorkerData;
    
    if (data.taskType === 'export') {
      const result = await StreamingExportService.exportPendingApprovalsStreaming(
        data.userRole,
        data.userId,
        data.filters
      );
      
      parentPort?.postMessage({
        success: true,
        result,
        message: `Export completed: ${result.totalRows} rows exported to ${result.filename}`
      });
    } else if (data.taskType === 'cleanup') {
      await StreamingExportService.cleanupOldExports(data.maxAgeHours || 24);
      
      parentPort?.postMessage({
        success: true,
        message: 'Cleanup completed successfully'
      });
    }
    
  } catch (error) {
    console.error('❌ Worker thread error:', error);
    
    parentPort?.postMessage({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

// Run the task
runExportTask();
