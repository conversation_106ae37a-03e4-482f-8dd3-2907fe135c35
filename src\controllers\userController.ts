import { Response } from "express";
import { UserService } from "../services/userService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";
import { UserRole } from "@prisma/client";

export class UserController {
  /**
   * Delete a user (Super Admin only)
   * DELETE /api/v1/users/:userId
   */
  static deleteUser = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { userId } = req.params;
      const adminUser = req.user!;

      console.log("Delete user request:", { 
        userId, 
        adminId: adminUser.id, 
        adminRole: adminUser.role 
      });

      await UserService.deleteUser(userId, adminUser.id);

      // Log the deletion for audit trail
      console.log("User deleted successfully:", { 
        deletedUserId: userId, 
        deletedBy: adminUser.id,
        timestamp: new Date().toISOString()
      });

      ResponseHandler.success(res, "User deleted successfully");
    }
  );

  /**
   * Update user details
   * PATCH /api/v1/users/:userId
   */
  static updateUser = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { userId } = req.params;
      const updateData = req.body;
      const currentUser = req.user!;

      console.log("Update user request:", { 
        userId, 
        updateData: Object.keys(updateData),
        requestedBy: currentUser.id,
        requestedByRole: currentUser.role
      });

      // Check permissions for the update
      const isSuperAdmin = currentUser.role === UserRole.SUPER_ADMIN;
      const isUpdatingSelf = currentUser.id === userId;

      // Validate permissions for specific fields
      if (!isSuperAdmin) {
        // Non-super admins can only update their own basic profile
        if (!isUpdatingSelf) {
          return ResponseHandler.error(
            res,
            "You can only update your own profile",
            undefined,
            403
          );
        }

        // Check if trying to update restricted fields
        const restrictedFields = ['role', 'isActive', 'email'];
        const attemptingRestrictedUpdate = restrictedFields.some(
          field => field in updateData
        );

        if (attemptingRestrictedUpdate) {
          return ResponseHandler.error(
            res,
            "Only Super Admin can update role, isActive status, or email",
            undefined,
            403
          );
        }

        // Only allow basic profile updates for non-super admins
        const allowedFields = ['firstName', 'lastName', 'monthlyTarget'];
        const invalidFields = Object.keys(updateData).filter(
          field => !allowedFields.includes(field)
        );

        if (invalidFields.length > 0) {
          return ResponseHandler.error(
            res,
            `You can only update: ${allowedFields.join(', ')}`,
            undefined,
            403
          );
        }
      }

      const updatedUser = await UserService.updateUser(
        userId, 
        updateData, 
        currentUser.id
      );

      // Log the update for audit trail
      console.log("User updated successfully:", { 
        updatedUserId: userId, 
        updatedFields: Object.keys(updateData),
        updatedBy: currentUser.id,
        timestamp: new Date().toISOString()
      });

      ResponseHandler.success(res, "User updated successfully", updatedUser);
    }
  );
}
