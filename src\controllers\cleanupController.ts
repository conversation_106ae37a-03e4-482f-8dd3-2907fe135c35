import { Request, Response } from "express";
import { DatabaseCleanupService } from "../services/databaseCleanupService";
import { ResponseHandler } from "../utils/response";
import { asyncHandler } from "../middleware/errorHandler";
import { AuthenticatedRequest } from "../types";

export class CleanupController {
  /**
   * Get cleanup statistics (what would be cleaned up)
   */
  static getCleanupStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const stats = await DatabaseCleanupService.getCleanupStats();

      ResponseHandler.success(
        res,
        "Cleanup statistics retrieved successfully",
        {
          ...stats,
          summary: {
            totalRecordsToCleanup:
              stats.otpCodesToCleanup +
              stats.notificationsToCleanup +
              stats.passwordResetTokensToCleanup,
            hasDataToCleanup:
              stats.otpCodesToCleanup > 0 ||
              stats.notificationsToCleanup > 0 ||
              stats.passwordResetTokensToCleanup > 0,
          },
        }
      );
    }
  );

  /**
   * Get cleanup job status
   */
  static getJobStatus = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const status = DatabaseCleanupService.getJobStatus();

      ResponseHandler.success(
        res,
        "Cleanup job status retrieved successfully",
        status
      );
    }
  );

  /**
   * Trigger manual OTP cleanup
   */
  static manualOtpCleanup = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      console.log(
        `🔧 Manual OTP cleanup triggered by user ${req.user!.id} (${
          req.user!.email
        })`
      );

      const result = await DatabaseCleanupService.cleanupOtpCodes();

      if (result.success) {
        ResponseHandler.success(
          res,
          "OTP cleanup completed successfully",
          result
        );
      } else {
        ResponseHandler.error(res, "OTP cleanup failed", result.error, 500);
      }
    }
  );

  /**
   * Trigger manual notification cleanup
   */
  static manualNotificationCleanup = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      console.log(
        `🔧 Manual notification cleanup triggered by user ${req.user!.id} (${
          req.user!.email
        })`
      );

      const result = await DatabaseCleanupService.cleanupNotifications();

      if (result.success) {
        ResponseHandler.success(
          res,
          "Notification cleanup completed successfully",
          result
        );
      } else {
        ResponseHandler.error(
          res,
          "Notification cleanup failed",
          result.error,
          500
        );
      }
    }
  );

  /**
   * Trigger manual password reset token cleanup
   */
  static manualPasswordResetCleanup = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      console.log(
        `🔧 Manual password reset token cleanup triggered by user ${
          req.user!.id
        } (${req.user!.email})`
      );

      const result = await DatabaseCleanupService.cleanupPasswordResetTokens();

      if (result.success) {
        ResponseHandler.success(
          res,
          "Password reset token cleanup completed successfully",
          result
        );
      } else {
        ResponseHandler.error(
          res,
          "Password reset token cleanup failed",
          result.error,
          500
        );
      }
    }
  );

  /**
   * Trigger manual full cleanup
   */
  static manualFullCleanup = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      console.log(
        `🔧 Manual full cleanup triggered by user ${req.user!.id} (${
          req.user!.email
        })`
      );

      const stats = await DatabaseCleanupService.runFullCleanup();

      const allSuccessful =
        stats.otpCleanup.success && stats.notificationCleanup.success;

      if (allSuccessful) {
        ResponseHandler.success(
          res,
          "Full database cleanup completed successfully",
          stats
        );
      } else {
        ResponseHandler.error(
          res,
          "Some cleanup operations failed",
          JSON.stringify(stats),
          500
        );
      }
    }
  );

  /**
   * Trigger selective manual cleanup
   */
  static manualSelectiveCleanup = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const {
        includeOtp = true,
        includeNotifications = true,
        includePasswordReset = true,
      } = req.body;

      console.log(
        `🔧 Manual selective cleanup triggered by user ${req.user!.id} (${
          req.user!.email
        }):`,
        {
          includeOtp,
          includeNotifications,
          includePasswordReset,
        }
      );

      const stats = await DatabaseCleanupService.manualCleanup({
        includeOtp,
        includeNotifications,
        includePasswordReset,
      });

      const allSuccessful =
        stats.otpCleanup.success &&
        stats.notificationCleanup.success &&
        stats.passwordResetCleanup.success;

      if (allSuccessful) {
        ResponseHandler.success(
          res,
          "Selective cleanup completed successfully",
          stats
        );
      } else {
        ResponseHandler.error(
          res,
          "Some cleanup operations failed",
          JSON.stringify(stats),
          500
        );
      }
    }
  );

  /**
   * Initialize cleanup jobs (for manual restart)
   */
  static initializeJobs = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      console.log(
        `🔧 Cleanup jobs initialization triggered by user ${req.user!.id} (${
          req.user!.email
        })`
      );

      try {
        DatabaseCleanupService.initializeScheduledJobs();
        const status = DatabaseCleanupService.getJobStatus();

        ResponseHandler.success(
          res,
          "Cleanup jobs initialized successfully",
          status
        );
      } catch (error) {
        ResponseHandler.error(
          res,
          "Failed to initialize cleanup jobs",
          error instanceof Error ? error.message : String(error),
          500
        );
      }
    }
  );

  /**
   * Stop cleanup jobs
   */
  static stopJobs = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      console.log(
        `🔧 Cleanup jobs stop triggered by user ${req.user!.id} (${
          req.user!.email
        })`
      );

      try {
        DatabaseCleanupService.stopScheduledJobs();
        const status = DatabaseCleanupService.getJobStatus();

        ResponseHandler.success(
          res,
          "Cleanup jobs stopped successfully",
          status
        );
      } catch (error) {
        ResponseHandler.error(
          res,
          "Failed to stop cleanup jobs",
          error instanceof Error ? error.message : String(error),
          500
        );
      }
    }
  );

  /**
   * Get comprehensive cleanup dashboard data
   */
  static getCleanupDashboard = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const [stats, jobStatus] = await Promise.all([
        DatabaseCleanupService.getCleanupStats(),
        DatabaseCleanupService.getJobStatus(),
      ]);

      const dashboard = {
        statistics: {
          ...stats,
          summary: {
            totalRecordsToCleanup:
              stats.otpCodesToCleanup +
              stats.notificationsToCleanup +
              stats.passwordResetTokensToCleanup,
            hasDataToCleanup:
              stats.otpCodesToCleanup > 0 ||
              stats.notificationsToCleanup > 0 ||
              stats.passwordResetTokensToCleanup > 0,
          },
        },
        jobStatus,
        recommendations: {
          shouldRunOtpCleanup: stats.otpCodesToCleanup > 100,
          shouldRunNotificationCleanup: stats.notificationsToCleanup > 1000,
          shouldRunPasswordResetCleanup:
            stats.passwordResetTokensToCleanup > 50,
          oldestDataAge: {
            otpDays: stats.oldestOtpCode
              ? Math.floor(
                  (Date.now() - stats.oldestOtpCode.getTime()) /
                    (1000 * 60 * 60 * 24)
                )
              : 0,
            notificationDays: stats.oldestNotification
              ? Math.floor(
                  (Date.now() - stats.oldestNotification.getTime()) /
                    (1000 * 60 * 60 * 24)
                )
              : 0,
            passwordResetDays: stats.oldestPasswordResetToken
              ? Math.floor(
                  (Date.now() - stats.oldestPasswordResetToken.getTime()) /
                    (1000 * 60 * 60 * 24)
                )
              : 0,
          },
        },
        lastUpdated: new Date().toISOString(),
      };

      ResponseHandler.success(
        res,
        "Cleanup dashboard data retrieved successfully",
        dashboard
      );
    }
  );
}
