import { Router } from "express";
import authRoutes from "./authRoutes";
import userRoutes from "./userRoutes";
import transactionRoutes from "./transactionRoutes";
import bulkDisbursementRoutes from "./bulkDisbursementRoutes";
import approvalRoutes from "./approvalRoutes";
import documentRoutes from "./documentRoutes";
import dashboardRoutes from "./dashboardRoutes";
import notificationRoutes from "./notificationRoutes";
import cleanupRoutes from "./cleanupRoutes";
import { R2Service } from "../services/r2Service";

const router = Router();

// API routes
router.use("/auth", authRoutes);
router.use("/users", userRoutes);
router.use("/transactions", transactionRoutes);
router.use("/transactions", bulkDisbursementRoutes);
router.use("/approvals", approvalRoutes);
router.use("/documents", documentRoutes);
router.use("/dashboard", dashboardRoutes);
router.use("/notifications", notificationRoutes);
router.use("/cleanup", cleanupRoutes);

// Health check endpoint
router.get("/health", (req, res) => {
  const r2Status = R2Service.isR2Configured();

  res.json({
    success: true,
    message: "AMS Loan System API is running",
    timestamp: new Date().toISOString(),
    services: {
      r2Storage: {
        configured: r2Status,
        status: r2Status ? "operational" : "not configured",
        message: r2Status
          ? "File storage service is ready"
          : "File storage service not configured - uploads will fail",
      },
    },
  });
});

export default router;
