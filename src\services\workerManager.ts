import { Worker } from 'worker_threads';
import path from 'path';
import { UserRole } from '@prisma/client';

export class WorkerManager {
  private static activeWorkers = new Set<Worker>();

  /**
   * Run XLSX export in worker thread (Non-blocking)
   */
  static async runExportInWorker(
    userRole: UserRole,
    userId: string,
    filters: {
      accountOfficerId?: string;
      accountOfficerIds?: string[];
      loanType?: string;
      dateFrom?: string;
      dateTo?: string;
      search?: string;
    }
  ): Promise<{ success: boolean; result?: any; error?: string }> {
    return new Promise((resolve, reject) => {
      console.log('🔄 Starting XLSX export in worker thread...');
      
      const workerPath = path.join(__dirname, '../workers/exportWorker.js');
      const worker = new Worker(workerPath, {
        workerData: {
          userRole,
          userId,
          filters,
          taskType: 'export'
        }
      });

      this.activeWorkers.add(worker);

      const timeout = setTimeout(() => {
        console.log('⏰ Worker timeout - terminating...');
        worker.terminate();
        this.activeWorkers.delete(worker);
        reject(new Error('Export worker timed out after 10 minutes'));
      }, 10 * 60 * 1000); // 10 minute timeout

      worker.on('message', (result) => {
        clearTimeout(timeout);
        this.activeWorkers.delete(worker);
        
        console.log('✅ Worker completed:', result);
        resolve(result);
      });

      worker.on('error', (error) => {
        clearTimeout(timeout);
        this.activeWorkers.delete(worker);
        
        console.error('❌ Worker error:', error);
        reject(error);
      });

      worker.on('exit', (code) => {
        clearTimeout(timeout);
        this.activeWorkers.delete(worker);
        
        if (code !== 0) {
          console.error(`❌ Worker stopped with exit code ${code}`);
          reject(new Error(`Worker stopped with exit code ${code}`));
        }
      });
    });
  }

  /**
   * Run cleanup in worker thread (Non-blocking)
   */
  static async runCleanupInWorker(maxAgeHours: number = 24): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve, reject) => {
      console.log('🗑️ Starting cleanup in worker thread...');
      
      const workerPath = path.join(__dirname, '../workers/exportWorker.js');
      const worker = new Worker(workerPath, {
        workerData: {
          taskType: 'cleanup',
          maxAgeHours
        }
      });

      this.activeWorkers.add(worker);

      const timeout = setTimeout(() => {
        console.log('⏰ Cleanup worker timeout - terminating...');
        worker.terminate();
        this.activeWorkers.delete(worker);
        reject(new Error('Cleanup worker timed out'));
      }, 5 * 60 * 1000); // 5 minute timeout

      worker.on('message', (result) => {
        clearTimeout(timeout);
        this.activeWorkers.delete(worker);
        
        console.log('✅ Cleanup worker completed:', result);
        resolve(result);
      });

      worker.on('error', (error) => {
        clearTimeout(timeout);
        this.activeWorkers.delete(worker);
        
        console.error('❌ Cleanup worker error:', error);
        reject(error);
      });

      worker.on('exit', (code) => {
        clearTimeout(timeout);
        this.activeWorkers.delete(worker);
        
        if (code !== 0) {
          console.error(`❌ Cleanup worker stopped with exit code ${code}`);
          reject(new Error(`Cleanup worker stopped with exit code ${code}`));
        }
      });
    });
  }

  /**
   * Get active worker count
   */
  static getActiveWorkerCount(): number {
    return this.activeWorkers.size;
  }

  /**
   * Terminate all active workers (for graceful shutdown)
   */
  static async terminateAllWorkers(): Promise<void> {
    console.log(`🛑 Terminating ${this.activeWorkers.size} active workers...`);
    
    const terminationPromises = Array.from(this.activeWorkers).map(worker => {
      return new Promise<void>((resolve) => {
        worker.terminate().then(() => {
          this.activeWorkers.delete(worker);
          resolve();
        }).catch(() => {
          // Force remove even if termination fails
          this.activeWorkers.delete(worker);
          resolve();
        });
      });
    });

    await Promise.all(terminationPromises);
    console.log('✅ All workers terminated');
  }
}
