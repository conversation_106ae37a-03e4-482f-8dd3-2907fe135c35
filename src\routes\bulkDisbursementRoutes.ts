import { Router } from "express";
import { BulkDisbursementController } from "../controllers/bulkDisbursementController";
import { validateBody } from "../middleware/validation";
import { authenticate, requireAccountant } from "../middleware/auth";
import { bulkDisbursementSchema } from "../utils/validation";

const router = Router();

// All routes require authentication and accountant role
router.use(authenticate);
router.use(requireAccountant);

/**
 * @swagger
 * tags:
 *   name: Bulk Disbursement
 *   description: Bulk disbursement operations for accountants
 */

/**
 * @swagger
 * /api/v1/transactions/eligible-for-disbursement:
 *   get:
 *     summary: Get transactions eligible for disbursement
 *     description: |
 *       Retrieve all transactions that are eligible for disbursement (APPROVED status, ACCOUNTANT stage).
 *       
 *       **Eligibility Criteria:**
 *       - Transaction status must be APPROVED
 *       - Current stage must be ACCOUNTANT
 *       - Must have valid disbursement account details
 *       
 *       **Access Control:**
 *       - Only users with ACCOUNTANT role can access this endpoint
 *       - Returns transactions from all account officers
 *       
 *       **Pagination:**
 *       - Supports limit and offset parameters
 *       - Maximum limit is 100 transactions per request
 *       - Results are ordered by approval date (oldest first)
 *     tags: [Bulk Disbursement]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Maximum number of transactions to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of transactions to skip
 *     responses:
 *       200:
 *         description: Eligible transactions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Eligible transactions retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/EligibleTransaction'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: Total number of eligible transactions
 *                         limit:
 *                           type: integer
 *                           description: Requested limit
 *                         offset:
 *                           type: integer
 *                           description: Requested offset
 *                         hasMore:
 *                           type: boolean
 *                           description: Whether more transactions are available
 *                         returned:
 *                           type: integer
 *                           description: Number of transactions returned in this response
 *       400:
 *         description: Invalid pagination parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Accountant role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/eligible-for-disbursement",
  BulkDisbursementController.getEligibleTransactions
);

/**
 * @swagger
 * /api/v1/transactions/bulk-disburse:
 *   post:
 *     summary: Process bulk disbursement
 *     description: |
 *       Process multiple approved transactions for disbursement in a single atomic operation.
 *       
 *       **Operation Details:**
 *       - All transactions must be in APPROVED status with ACCOUNTANT stage
 *       - All transactions are processed atomically (all succeed or all fail)
 *       - Transaction status is updated from APPROVED to DISBURSED
 *       - disbursedAt timestamp is set for all processed transactions
 *       - Maximum 100 transactions can be processed at once
 *       
 *       **Validation:**
 *       - Verifies all transactions exist and are eligible
 *       - Checks disbursement account details are complete
 *       - Ensures no duplicate transaction IDs
 *       - Validates user has ACCOUNTANT role
 *       
 *       **Atomicity:**
 *       - Uses database transactions to ensure all-or-nothing processing
 *       - If any transaction fails validation, entire operation is rolled back
 *       - Provides detailed error information for failed transactions
 *       
 *       **Audit Trail:**
 *       - Logs all disbursement activities
 *       - Records processing timestamp and accountant details
 *       - Maintains complete audit trail for compliance
 *     tags: [Bulk Disbursement]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BulkDisbursementRequest'
 *           examples:
 *             single_transaction:
 *               summary: Single transaction disbursement
 *               value:
 *                 transactionIds: ["687a4a82b2114af448562d15"]
 *             multiple_transactions:
 *               summary: Multiple transactions disbursement
 *               value:
 *                 transactionIds: [
 *                   "687a4a82b2114af448562d15",
 *                   "687a4a82b2114af448562d16",
 *                   "687a4a82b2114af448562d17"
 *                 ]
 *     responses:
 *       200:
 *         description: Bulk disbursement processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Successfully disbursed 3 of 3 transactions"
 *                 data:
 *                   $ref: '#/components/schemas/BulkDisbursementSummary'
 *       400:
 *         description: Validation error or processing failure
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalid_request:
 *                 summary: Invalid request format
 *                 value:
 *                   success: false
 *                   message: "transactionIds must be an array"
 *               too_many_transactions:
 *                 summary: Too many transactions
 *                 value:
 *                   success: false
 *                   message: "Maximum 100 transactions can be processed at once"
 *               processing_failure:
 *                 summary: Processing failure with rollback
 *                 value:
 *                   success: false
 *                   message: "Bulk disbursement failed. 1 transaction(s) could not be processed. All changes have been rolled back."
 *                   data:
 *                     failed:
 *                       - transactionId: "TXN-2024-001"
 *                         success: false
 *                         error: "Transaction status is IN_PROGRESS, expected APPROVED"
 *                         amount: 50000
 *                     successful: []
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Accountant role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: One or more transactions not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/bulk-disburse",
  validateBody(bulkDisbursementSchema),
  BulkDisbursementController.processBulkDisbursement
);

/**
 * @swagger
 * /api/v1/transactions/disbursement-stats:
 *   get:
 *     summary: Get disbursement statistics
 *     description: |
 *       Get current disbursement statistics including eligible transactions count,
 *       total eligible amount, and recent disbursement activity.
 *       
 *       **Statistics Included:**
 *       - Number of transactions eligible for disbursement
 *       - Total amount of eligible transactions
 *       - Number of recent disbursements (last 24 hours)
 *       
 *       **Use Cases:**
 *       - Dashboard widgets and summaries
 *       - Planning and workload management
 *       - Performance monitoring
 *     tags: [Bulk Disbursement]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Disbursement statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Disbursement statistics retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/DisbursementStats'
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Accountant role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/disbursement-stats",
  BulkDisbursementController.getDisbursementStats
);

/**
 * @swagger
 * /api/v1/transactions/validate-bulk-disbursement:
 *   post:
 *     summary: Validate transactions for bulk disbursement (dry run)
 *     description: |
 *       Validate a list of transactions for bulk disbursement without actually processing them.
 *       This is useful for pre-validation and user interface feedback.
 *       
 *       **Validation Checks:**
 *       - Verifies all transaction IDs exist
 *       - Checks eligibility status (APPROVED, ACCOUNTANT stage)
 *       - Validates disbursement account details
 *       - Calculates total disbursement amount
 *       
 *       **Response Information:**
 *       - Number of valid vs invalid transactions
 *       - Total disbursement amount for valid transactions
 *       - Whether the bulk operation can proceed
 *       - Detailed issues for invalid transactions
 *     tags: [Bulk Disbursement]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BulkDisbursementRequest'
 *     responses:
 *       200:
 *         description: Validation completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Bulk disbursement validation completed"
 *                 data:
 *                   $ref: '#/components/schemas/BulkDisbursementValidation'
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Accountant role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/validate-bulk-disbursement",
  validateBody(bulkDisbursementSchema),
  BulkDisbursementController.validateBulkDisbursement
);

export default router;
