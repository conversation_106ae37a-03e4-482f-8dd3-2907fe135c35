/**
 * CSV Utility Functions
 * Provides robust CSV generation and formatting utilities
 */

export interface CSVOptions {
  delimiter?: string;
  quote?: string;
  escape?: string;
  lineBreak?: string;
  includeHeaders?: boolean;
}

export class CSVUtils {
  private static readonly DEFAULT_OPTIONS: Required<CSVOptions> = {
    delimiter: ",",
    quote: '"',
    escape: '"',
    lineBreak: "\n",
    includeHeaders: true,
  };

  /**
   * Convert array of objects to CSV string with robust handling
   */
  static arrayToCSV(data: any[], options: CSVOptions = {}): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    if (!Array.isArray(data) || data.length === 0) {
      return "";
    }

    // Get headers from first object
    const headers = Object.keys(data[0]);
    const csvLines: string[] = [];

    // Add headers if requested
    if (opts.includeHeaders) {
      csvLines.push(this.formatCSVRow(headers, opts));
    }

    // Process each data row
    data.forEach((row, index) => {
      try {
        const values = headers.map(header => this.formatCSVValue(row[header], opts));
        csvLines.push(values.join(opts.delimiter));
      } catch (error) {
        console.error(`Error processing CSV row ${index}:`, error);
        // Skip problematic rows but continue processing
      }
    });

    return csvLines.join(opts.lineBreak);
  }

  /**
   * Format a single CSV row
   */
  private static formatCSVRow(values: string[], options: Required<CSVOptions>): string {
    return values
      .map(value => this.formatCSVValue(value, options))
      .join(options.delimiter);
  }

  /**
   * Format a single CSV value with proper escaping
   */
  private static formatCSVValue(value: any, options: Required<CSVOptions>): string {
    // Handle null/undefined
    if (value === null || value === undefined) {
      return "";
    }

    // Handle different data types
    let stringValue: string;

    if (typeof value === "object") {
      if (Array.isArray(value)) {
        // Join array elements with semicolon
        stringValue = value
          .map(item => this.sanitizeValue(item))
          .filter(Boolean)
          .join("; ");
      } else if (value instanceof Date) {
        // Format dates consistently
        stringValue = value.toLocaleDateString("en-US", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
        });
      } else {
        // For other objects, try to extract meaningful information
        stringValue = this.extractObjectValue(value);
      }
    } else {
      stringValue = String(value);
    }

    // Sanitize the string value
    stringValue = this.sanitizeValue(stringValue);

    // Check if quoting is needed
    const needsQuoting = 
      stringValue.includes(options.delimiter) ||
      stringValue.includes(options.quote) ||
      stringValue.includes(options.lineBreak) ||
      stringValue.includes(";") ||
      stringValue.startsWith(" ") ||
      stringValue.endsWith(" ");

    if (needsQuoting) {
      // Escape existing quotes
      const escapedValue = stringValue.replace(
        new RegExp(options.quote, "g"),
        options.escape + options.quote
      );
      return options.quote + escapedValue + options.quote;
    }

    return stringValue;
  }

  /**
   * Sanitize a value to ensure it's safe for CSV
   */
  private static sanitizeValue(value: any): string {
    if (value === null || value === undefined) {
      return "";
    }

    let stringValue = String(value);

    // Remove or replace problematic characters
    stringValue = stringValue
      .replace(/[\r\n\t]/g, " ") // Replace line breaks and tabs with spaces
      .replace(/\s+/g, " ") // Collapse multiple spaces
      .trim(); // Remove leading/trailing spaces

    return stringValue;
  }

  /**
   * Extract meaningful value from complex objects
   */
  private static extractObjectValue(obj: any): string {
    if (!obj || typeof obj !== "object") {
      return String(obj || "");
    }

    // Try common meaningful properties first
    const meaningfulProps = ["name", "title", "label", "value", "text", "description"];
    
    for (const prop of meaningfulProps) {
      if (obj[prop] !== undefined && obj[prop] !== null) {
        return String(obj[prop]);
      }
    }

    // If it's a simple object with few properties, stringify it
    const keys = Object.keys(obj);
    if (keys.length <= 3) {
      try {
        return JSON.stringify(obj);
      } catch {
        return "[Object]";
      }
    }

    // For complex objects, return a summary
    return `[Object with ${keys.length} properties]`;
  }

  /**
   * Generate a safe filename for CSV export
   */
  static generateCSVFilename(prefix: string, suffix?: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-").slice(0, 19);
    const safeSuffix = suffix ? `-${suffix.replace(/[^a-zA-Z0-9-_]/g, "")}` : "";
    return `${prefix}${safeSuffix}-${timestamp}.csv`;
  }

  /**
   * Validate CSV content
   */
  static validateCSVContent(content: string): {
    isValid: boolean;
    error?: string;
    lineCount?: number;
    columnCount?: number;
  } {
    if (!content || typeof content !== "string") {
      return {
        isValid: false,
        error: "Content is not a string",
      };
    }

    // Check if it looks like JSON instead of CSV
    const trimmed = content.trim();
    if (trimmed.startsWith("{") || trimmed.startsWith("[")) {
      return {
        isValid: false,
        error: "Content appears to be JSON instead of CSV",
      };
    }

    const lines = content.split(/\r?\n/).filter(line => line.trim());
    
    if (lines.length === 0) {
      return {
        isValid: false,
        error: "No content lines found",
      };
    }

    // Check if first line looks like headers
    const firstLine = lines[0];
    const columnCount = firstLine.split(",").length;

    return {
      isValid: true,
      lineCount: lines.length,
      columnCount,
    };
  }

  /**
   * Create CSV from transaction data with specific formatting
   */
  static createTransactionCSV(transactions: any[]): string {
    if (!Array.isArray(transactions) || transactions.length === 0) {
      return "Transaction ID,Customer Name,Email,Phone Number,BVN,Loan Type,Requested Amount,Status,Current Stage,Created Date\n";
    }

    const csvData = transactions.map(transaction => ({
      "Transaction ID": transaction.transactionId || "",
      "Customer Name": [
        transaction.firstName,
        transaction.middleName,
        transaction.lastName
      ].filter(Boolean).join(" "),
      "Email": transaction.email || "",
      "Phone Number": transaction.phoneNumber || "",
      "BVN": transaction.bvn || "",
      "Loan Type": transaction.loanTypeConfig?.name || transaction.loanType || "",
      "Requested Amount": transaction.requestedAmount || 0,
      "Status": transaction.status || "",
      "Current Stage": transaction.currentStage || "",
      "Created Date": transaction.createdAt 
        ? new Date(transaction.createdAt).toLocaleDateString("en-US", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          })
        : "",
      "Submitted Date": transaction.submittedAt
        ? new Date(transaction.submittedAt).toLocaleDateString("en-US", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          })
        : "",
      "Account Officer": transaction.createdBy
        ? `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`
        : "",
      "Account Officer Email": transaction.createdBy?.email || "",
      "Organization": transaction.organizationName || "",
      "IPPIS Number": transaction.ippisNumber || "",
      "Employment Date": transaction.employmentDate
        ? new Date(transaction.employmentDate).toLocaleDateString("en-US")
        : "",
      "Address": [
        transaction.street,
        transaction.city,
        transaction.state,
        transaction.postalCode,
      ].filter(Boolean).join(", "),
      "Loan Purpose": transaction.loanPurpose || "",
      "Repayment Mode": transaction.repaymentMode || "",
      "Tenor (Months)": transaction.tenor || "",
      "Monthly Repayment": transaction.monthlyRepayment || 0,
      "Total Repayment": transaction.totalRepayment || 0,
      "Last Updated": transaction.updatedAt
        ? new Date(transaction.updatedAt).toLocaleDateString("en-US", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          })
        : "",
    }));

    return this.arrayToCSV(csvData);
  }
}
