import prisma from "../db/db";
import { AuthUtils } from "../utils/auth";
import { EmailService } from "../utils/email";
import { UserRole } from "@prisma/client";
import { OperationalError } from "../middleware/errorHandler";
import crypto from "crypto";

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  monthlyTarget?: number;
}

export interface AuthResult {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
  };
  accessToken: string;
  refreshToken: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

export class AuthService {
  static async login(
    credentials: LoginCredentials
  ): Promise<{ requiresOTP: boolean; message: string }> {
    const { email, password } = credentials;

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new OperationalError(
        "No account found with this email address. Please check your email and try again, or contact support if you need assistance.",
        401
      );
    }

    if (!user.isActive) {
      throw new OperationalError(
        "Your account has been deactivated. Please contact support to reactivate your account before attempting to log in.",
        401
      );
    }

    // Verify password
    const isValidPassword = await AuthUtils.comparePassword(
      password,
      user.password
    );
    if (!isValidPassword) {
      throw new OperationalError(
        "The password you entered is incorrect. Please check your password and try again. If you've forgotten your password, use the 'Forgot Password' option.",
        401
      );
    }

    // Generate and send OTP
    const otp = AuthUtils.generateOTP();
    const expiresAt = AuthUtils.getOTPExpiryTime();

    // Store OTP in database
    await prisma.otpCode.create({
      data: {
        userId: user.id,
        code: otp,
        expiresAt,
      },
    });

    // Send OTP via email
    await EmailService.sendOTP(email, otp);

    return {
      requiresOTP: true,
      message: "OTP sent to your email address",
    };
  }

  static async verifyOTP(email: string, otpCode: string): Promise<AuthResult> {
    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        otpCodes: {
          where: {
            code: otpCode,
            isUsed: false,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
    });

    if (!user) {
      throw new OperationalError(
        "No account found with this email address. Please verify your email and try again.",
        401
      );
    }

    if (!user.isActive) {
      throw new OperationalError(
        "Your account has been deactivated. Please contact support to reactivate your account.",
        401
      );
    }

    const otpRecord = user.otpCodes[0];
    if (!otpRecord) {
      throw new OperationalError(
        "The OTP code you entered is invalid or has already been used. Please request a new OTP code and try again.",
        400
      );
    }

    // Check if OTP is expired
    if (AuthUtils.isOTPExpired(otpRecord.expiresAt)) {
      throw new OperationalError(
        "Your OTP code has expired. OTP codes are valid for 10 minutes. Please request a new OTP code to continue.",
        400
      );
    }

    // Mark OTP as used
    await prisma.otpCode.update({
      where: { id: otpRecord.id },
      data: { isUsed: true },
    });

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    });

    // Generate tokens
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
    };

    const accessToken = AuthUtils.generateAccessToken(payload);
    const refreshToken = AuthUtils.generateRefreshToken(payload);

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
      accessToken,
      refreshToken,
    };
  }

  static async refreshToken(
    refreshToken: string
  ): Promise<{ accessToken: string }> {
    try {
      const payload = AuthUtils.verifyRefreshToken(refreshToken);

      // Verify user still exists and is active
      const user = await prisma.user.findUnique({
        where: { id: payload.id },
      });

      if (!user || !user.isActive) {
        throw new OperationalError("Invalid user", 401);
      }

      // Generate new access token
      const newPayload = {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName,
      };

      const accessToken = AuthUtils.generateAccessToken(newPayload);

      return { accessToken };
    } catch (error) {
      throw new OperationalError("Invalid refresh token", 401);
    }
  }

  static async createUser(
    userData: CreateUserData
  ): Promise<{ id: string; email: string }> {
    const { email, password, firstName, lastName, role, monthlyTarget } =
      userData;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new OperationalError("User with this email already exists", 409);
    }

    // Hash password
    const hashedPassword = await AuthUtils.hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role,
        monthlyTarget: monthlyTarget ? monthlyTarget : undefined,
      },
    });

    return {
      id: user.id,
      email: user.email,
    };
  }

  static async createSuperAdmin(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }): Promise<{ id: string; email: string; message: string }> {
    const { email, password, firstName, lastName } = userData;

    // Check if any super admin already exists
    // const existingSuperAdmin = await prisma.user.findFirst({
    //   where: { role: UserRole.SUPER_ADMIN },
    // });

    // if (existingSuperAdmin) {
    //   throw new OperationalError(
    //     "Super Admin already exists. Only one Super Admin is allowed.",
    //     400
    //   );
    // }

    // Check if user with this email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new OperationalError(
        `An account with the email address '${email}' already exists. Please use a different email address or contact support if you believe this is an error.`,
        409
      );
    }

    // Hash password
    const hashedPassword = await AuthUtils.hashPassword(password);

    // Create super admin user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role: UserRole.SUPER_ADMIN,
        isActive: true,
      },
    });

    return {
      id: user.id,
      email: user.email,
      message: "Super Admin created successfully",
    };
  }

  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new OperationalError("User not found", 404);
    }

    // Verify current password
    const isValidPassword = await AuthUtils.comparePassword(
      currentPassword,
      user.password
    );
    if (!isValidPassword) {
      throw new OperationalError("Current password is incorrect", 400);
    }

    // Hash new password
    const hashedNewPassword = await AuthUtils.hashPassword(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword },
    });
  }

  static async deactivateUser(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: { isActive: false },
    });
  }

  static async activateUser(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: { isActive: true },
    });
  }

  /**
   * Request password reset - sends reset token via email
   */
  static async requestPasswordReset(
    email: string
  ): Promise<{ message: string }> {
    console.log("Password reset requested for:", email);

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // Don't reveal if email exists or not for security
      return {
        message:
          "If an account with that email exists, a password reset link has been sent.",
      };
    }

    if (!user.isActive) {
      throw new OperationalError(
        "Your account has been deactivated and cannot be used for password reset. Please contact support for assistance with reactivating your account.",
        400
      );
    }

    // Generate secure reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    // Invalidate any existing reset tokens for this user
    await prisma.passwordResetToken.updateMany({
      where: {
        userId: user.id,
        isUsed: false,
        expiresAt: { gt: new Date() },
      },
      data: { isUsed: true },
    });

    // Create new reset token
    await prisma.passwordResetToken.create({
      data: {
        userId: user.id,
        token: resetToken,
        expiresAt,
      },
    });

    // Send reset email
    try {
      const resetUrl = `${
        process.env.FRONTEND_URL || "http://localhost:3000"
      }/reset-password?token=${resetToken}`;

      await EmailService.sendPasswordResetEmail({
        to: user.email,
        firstName: user.firstName,
        resetUrl,
        expiresAt,
      });

      console.log("Password reset email sent successfully to:", email);
    } catch (error) {
      console.error("Failed to send password reset email:", error);
      throw new OperationalError(
        "Failed to send password reset email. Please try again.",
        500
      );
    }

    return {
      message:
        "If an account with that email exists, a password reset link has been sent.",
    };
  }

  /**
   * Reset password using token
   */
  static async resetPassword(
    token: string,
    newPassword: string
  ): Promise<{ message: string }> {
    console.log(
      "Password reset attempt with token:",
      token.substring(0, 8) + "..."
    );

    // Find valid reset token
    const resetToken = await prisma.passwordResetToken.findFirst({
      where: {
        token,
        isUsed: false,
        expiresAt: { gt: new Date() },
      },
      include: {
        user: true,
      },
    });

    if (!resetToken) {
      throw new OperationalError("Invalid or expired reset token.", 400);
    }

    if (!resetToken.user.isActive) {
      throw new OperationalError(
        "Account is deactivated. Please contact support.",
        400
      );
    }

    // Validate new password
    if (!newPassword || newPassword.length < 8) {
      throw new OperationalError(
        "Password must be at least 8 characters long.",
        400
      );
    }

    // Hash new password
    const hashedPassword = await AuthUtils.hashPassword(newPassword);

    // Update user password and mark token as used
    await prisma.$transaction([
      prisma.user.update({
        where: { id: resetToken.userId },
        data: {
          password: hashedPassword,
          updatedAt: new Date(),
        },
      }),
      prisma.passwordResetToken.update({
        where: { id: resetToken.id },
        data: { isUsed: true },
      }),
    ]);

    console.log("Password reset successful for user:", resetToken.user.email);

    // Send confirmation email
    try {
      await EmailService.sendPasswordResetConfirmationEmail({
        to: resetToken.user.email,
        firstName: resetToken.user.firstName,
      });
    } catch (error) {
      console.error("Failed to send password reset confirmation email:", error);
      // Don't throw error here as password was already reset successfully
    }

    return {
      message:
        "Password has been reset successfully. You can now login with your new password.",
    };
  }

  /**
   * Validate reset token (for frontend to check if token is valid before showing reset form)
   */
  static async validateResetToken(
    token: string
  ): Promise<{ valid: boolean; email?: string }> {
    const resetToken = await prisma.passwordResetToken.findFirst({
      where: {
        token,
        isUsed: false,
        expiresAt: { gt: new Date() },
      },
      include: {
        user: {
          select: {
            email: true,
            isActive: true,
          },
        },
      },
    });

    if (!resetToken || !resetToken.user.isActive) {
      return { valid: false };
    }

    return {
      valid: true,
      email: resetToken.user.email,
    };
  }
}
