import { Response } from 'express';
import { DashboardService } from '../services/dashboardService';
import { ResponseHandler } from '../utils/response';
import { AuthenticatedRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';

export class DashboardController {
  static getUserDashboard = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const stats = await DashboardService.getUserDashboardStats(userId, userRole);

    ResponseHandler.success(res, 'Dashboard statistics retrieved successfully', stats);
  });

  static getSystemOverview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const overview = await DashboardService.getSystemOverview();

    ResponseHandler.success(res, 'System overview retrieved successfully', overview);
  });

  static getTransactionTrends = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const days = parseInt(req.query.days as string) || 30;

    const trends = await DashboardService.getTransactionTrends(days);

    ResponseHandler.success(res, 'Transaction trends retrieved successfully', trends);
  });

  static getApprovalMetrics = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const metrics = await DashboardService.getApprovalMetrics();

    ResponseHandler.success(res, 'Approval metrics retrieved successfully', metrics);
  });

  static getMyTransactions = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const filters = {
      status: req.query.status as string,
      dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
      search: req.query.search as string,
      accountOfficerName: req.query.accountOfficerName as string,
    };

    const pagination = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 10,
      sortBy: req.query.sortBy as string || 'createdAt',
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
    };

    const result = await DashboardService.getMyTransactions(filters, pagination, userId, userRole);

    ResponseHandler.success(res, 'My transactions retrieved successfully', result);
  });

  static getMyTransactionStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const dateFrom = req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined;
    const dateTo = req.query.dateTo ? new Date(req.query.dateTo as string) : undefined;

    const stats = await DashboardService.getMyTransactionStats(userId, userRole, dateFrom, dateTo);

    ResponseHandler.success(res, 'My transaction statistics retrieved successfully', stats);
  });

  static getAccountOfficers = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userRole = req.user!.role;

    // Only non-account officers can see this list for filtering
    if (userRole === 'ACCOUNT_OFFICER') {
      return ResponseHandler.forbidden(res, 'Access denied');
    }

    const accountOfficers = await DashboardService.getAccountOfficersList();

    ResponseHandler.success(res, 'Account officers list retrieved successfully', accountOfficers);
  });

  static getActivities = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const filters = {
      type: req.query.type as string,
      dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
      userId: req.query.userId as string,
    };

    const pagination = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
    };

    const activities = await DashboardService.getActivities(filters, pagination, userId, userRole);

    ResponseHandler.success(res, 'Activities retrieved successfully', activities);
  });

  static getUserManagement = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userRole = req.user!.role;

    // Only Super Admin can access user management
    if (userRole !== 'SUPER_ADMIN') {
      return ResponseHandler.forbidden(res, 'Access denied. Super Admin access required.');
    }

    const filters = {
      role: req.query.role as string,
      status: req.query.status as string,
      search: req.query.search as string,
      dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
    };

    const pagination = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 50,
    };

    const users = await DashboardService.getUserManagement(filters, pagination);

    ResponseHandler.success(res, 'User management data retrieved successfully', users);
  });

  static getPerformanceOverview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const performanceOverview = await DashboardService.getPerformanceOverview();
    ResponseHandler.success(res, 'Performance overview retrieved successfully', performanceOverview);
  });
}
