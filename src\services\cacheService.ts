import NodeCache from "node-cache";
import { UserRole } from "@prisma/client";

/**
 * In-memory caching service for frequently accessed data
 * Uses node-cache for simple in-memory caching
 * For production, consider Redis for distributed caching
 */
export class CacheService {
  private static cache = new NodeCache({
    stdTTL: 300, // 5 minutes default TTL
    checkperiod: 60, // Check for expired keys every minute
    useClones: false, // Don't clone objects for better performance
  });

  // Cache keys
  private static readonly KEYS = {
    USER_PERMISSIONS: (role: UserRole) => `user_permissions_${role}`,
    DASHBOARD_STATS: (userId: string, role: UserRole) => `dashboard_stats_${userId}_${role}`,
    SYSTEM_OVERVIEW: "system_overview",
    PERFORMANCE_OVERVIEW: "performance_overview",
    ACCOUNT_OFFICERS: "account_officers",
    TRANSACTION_COUNTS: (userId: string, role: UserRole) => `transaction_counts_${userId}_${role}`,
    USER_PROFILE: (userId: string) => `user_profile_${userId}`,
    APPROVAL_METRICS: "approval_metrics",
    TRANSACTION_TRENDS: (days: number) => `transaction_trends_${days}`,
  };

  /**
   * Get cached data
   */
  static get<T>(key: string): T | undefined {
    return this.cache.get<T>(key);
  }

  /**
   * Set cached data with optional TTL
   */
  static set<T>(key: string, value: T, ttl?: number): boolean {
    return this.cache.set(key, value, ttl || 300);
  }

  /**
   * Delete cached data
   */
  static del(key: string | string[]): number {
    return this.cache.del(key);
  }

  /**
   * Clear all cache
   */
  static flushAll(): void {
    this.cache.flushAll();
  }

  /**
   * Get cache statistics
   */
  static getStats() {
    return this.cache.getStats();
  }

  // Specific cache methods for common operations

  /**
   * Cache user permissions (long TTL since roles don't change often)
   */
  static getUserPermissions(role: UserRole) {
    return this.get(this.KEYS.USER_PERMISSIONS(role));
  }

  static setUserPermissions(role: UserRole, permissions: any) {
    return this.set(this.KEYS.USER_PERMISSIONS(role), permissions, 3600); // 1 hour TTL
  }

  /**
   * Cache dashboard stats (short TTL for real-time data)
   */
  static getDashboardStats(userId: string, role: UserRole) {
    return this.get(this.KEYS.DASHBOARD_STATS(userId, role));
  }

  static setDashboardStats(userId: string, role: UserRole, stats: any) {
    return this.set(this.KEYS.DASHBOARD_STATS(userId, role), stats, 120); // 2 minutes TTL
  }

  /**
   * Cache system overview (medium TTL)
   */
  static getSystemOverview() {
    return this.get(this.KEYS.SYSTEM_OVERVIEW);
  }

  static setSystemOverview(overview: any) {
    return this.set(this.KEYS.SYSTEM_OVERVIEW, overview, 300); // 5 minutes TTL
  }

  /**
   * Cache performance overview (medium TTL)
   */
  static getPerformanceOverview() {
    return this.get(this.KEYS.PERFORMANCE_OVERVIEW);
  }

  static setPerformanceOverview(overview: any) {
    return this.set(this.KEYS.PERFORMANCE_OVERVIEW, overview, 300); // 5 minutes TTL
  }

  /**
   * Cache account officers list (long TTL)
   */
  static getAccountOfficers() {
    return this.get(this.KEYS.ACCOUNT_OFFICERS);
  }

  static setAccountOfficers(officers: any) {
    return this.set(this.KEYS.ACCOUNT_OFFICERS, officers, 1800); // 30 minutes TTL
  }

  /**
   * Cache user profile (medium TTL)
   */
  static getUserProfile(userId: string) {
    return this.get(this.KEYS.USER_PROFILE(userId));
  }

  static setUserProfile(userId: string, profile: any) {
    return this.set(this.KEYS.USER_PROFILE(userId), profile, 600); // 10 minutes TTL
  }

  /**
   * Cache transaction trends (medium TTL)
   */
  static getTransactionTrends(days: number) {
    return this.get(this.KEYS.TRANSACTION_TRENDS(days));
  }

  static setTransactionTrends(days: number, trends: any) {
    return this.set(this.KEYS.TRANSACTION_TRENDS(days), trends, 600); // 10 minutes TTL
  }

  /**
   * Invalidate cache for specific user when their data changes
   */
  static invalidateUserCache(userId: string, role?: UserRole) {
    const keysToDelete = [
      this.KEYS.USER_PROFILE(userId),
    ];

    if (role) {
      keysToDelete.push(
        this.KEYS.DASHBOARD_STATS(userId, role),
        this.KEYS.TRANSACTION_COUNTS(userId, role)
      );
    }

    return this.del(keysToDelete);
  }

  /**
   * Invalidate system-wide cache when global data changes
   */
  static invalidateSystemCache() {
    const keysToDelete = [
      this.KEYS.SYSTEM_OVERVIEW,
      this.KEYS.PERFORMANCE_OVERVIEW,
      this.KEYS.ACCOUNT_OFFICERS,
      this.KEYS.APPROVAL_METRICS,
    ];

    return this.del(keysToDelete);
  }

  /**
   * Invalidate transaction-related cache
   */
  static invalidateTransactionCache() {
    // Get all keys and filter transaction-related ones
    const allKeys = this.cache.keys();
    const transactionKeys = allKeys.filter(key => 
      key.includes('dashboard_stats_') || 
      key.includes('transaction_counts_') ||
      key.includes('transaction_trends_') ||
      key === this.KEYS.SYSTEM_OVERVIEW ||
      key === this.KEYS.PERFORMANCE_OVERVIEW ||
      key === this.KEYS.APPROVAL_METRICS
    );

    return this.del(transactionKeys);
  }

  /**
   * Get cache hit rate for monitoring
   */
  static getCacheHitRate(): number {
    const stats = this.getStats();
    const totalRequests = stats.hits + stats.misses;
    return totalRequests > 0 ? (stats.hits / totalRequests) * 100 : 0;
  }

  /**
   * Log cache statistics for monitoring
   */
  static logCacheStats() {
    const stats = this.getStats();
    const hitRate = this.getCacheHitRate();
    
    console.log("📊 Cache Statistics:", {
      keys: stats.keys,
      hits: stats.hits,
      misses: stats.misses,
      hitRate: `${hitRate.toFixed(2)}%`,
      ksize: stats.ksize,
      vsize: stats.vsize,
    });
  }
}
