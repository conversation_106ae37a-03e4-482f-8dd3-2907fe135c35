import { Router } from "express";
import { AuthController } from "../controllers/authController";
import { PasswordResetController } from "../controllers/passwordResetController";
import { validateBody } from "../middleware/validation";
import { authenticate, requireSuperAdmin } from "../middleware/auth";
import { authLimiter, otpLimiter } from "../middleware/rateLimiter";
import {
  loginSchema,
  verifyOtpSchema,
  createUserSchema,
  createSuperAdminSchema,
  passwordResetRequestSchema,
  passwordResetConfirmSchema,
} from "../utils/validation";

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Authentication
 *   description: User authentication and authorization
 */

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: Login with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *           example:
 *             email: "<EMAIL>"
 *             password: "officer123456"
 *     responses:
 *       200:
 *         description: Login successful, OTP sent
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *             example:
 *               success: true
 *               message: "OTP sent to your email"
 *               data:
 *                 requiresOTP: true
 *       400:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       429:
 *         description: Too many login attempts
 */
router.post(
  "/login",
  authLimiter,
  validateBody(loginSchema),
  AuthController.login
);

/**
 * @swagger
 * /api/v1/auth/verify-otp:
 *   post:
 *     summary: Verify OTP and get access tokens
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OTPVerificationRequest'
 *           example:
 *             email: "<EMAIL>"
 *             otp: "123456"
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Login successful"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                     tokens:
 *                       type: object
 *                       properties:
 *                         accessToken:
 *                           type: string
 *                         refreshToken:
 *                           type: string
 *       400:
 *         description: Invalid or expired OTP
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/verify-otp",
  otpLimiter,
  validateBody(verifyOtpSchema),
  AuthController.verifyOTP
);

/**
 * @swagger
 * /api/v1/auth/refresh-token:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 example: "your_refresh_token_here"
 *             required:
 *               - refreshToken
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Token refreshed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     accessToken:
 *                       type: string
 *       400:
 *         description: Refresh token is required or invalid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/refresh-token", AuthController.refreshToken);

/**
 * @swagger
 * /api/v1/auth/request-password-reset:
 *   post:
 *     summary: Request password reset
 *     description: |
 *       Send a password reset link to the user's email address.
 *
 *       **Security Features:**
 *       - Doesn't reveal if email exists or not (prevents email enumeration)
 *       - Generates cryptographically secure random token (32 bytes)
 *       - Token expires in 1 hour for security
 *       - Invalidates previous reset tokens automatically
 *       - Rate limited to prevent abuse (100 requests per 15 minutes)
 *
 *       **Process Flow:**
 *       1. User provides email address
 *       2. System validates email format
 *       3. If account exists and is active, generates secure reset token
 *       4. Reset link sent via email with professional template
 *       5. User receives email with secure reset link
 *
 *       **Email Content:**
 *       - Professional HTML template with AMS branding
 *       - Secure reset link with embedded token
 *       - Clear expiration time (1 hour from generation)
 *       - Security warnings and usage instructions
 *       - Mobile-responsive design with fallback text version
 *
 *       **Production Notes:**
 *       - Always returns success message regardless of email existence
 *       - Requires SMTP configuration for email delivery
 *       - Logs all reset requests for security monitoring
 *       - Supports high-traffic production environments
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PasswordResetRequest'
 *           example:
 *             email: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Password reset email sent (or would be sent if email exists)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "If an account with that email exists, a password reset link has been sent."
 *             examples:
 *               success:
 *                 summary: Successful request
 *                 value:
 *                   success: true
 *                   message: "If an account with that email exists, a password reset link has been sent."
 *       400:
 *         description: Invalid email format or account deactivated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalid_email:
 *                 summary: Invalid email format
 *                 value:
 *                   success: false
 *                   message: "Invalid email format"
 *               account_deactivated:
 *                 summary: Account deactivated
 *                 value:
 *                   success: false
 *                   message: "Account is deactivated. Please contact support."
 *       429:
 *         description: Too many requests - rate limited
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Too many requests from this IP, please try again later."
 *       500:
 *         description: Failed to send email or internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Failed to send password reset email. Please try again later."
 */
router.post(
  "/request-password-reset",
  authLimiter,
  validateBody(passwordResetRequestSchema),
  PasswordResetController.requestPasswordReset
);

/**
 * @swagger
 * /api/v1/auth/reset-password:
 *   post:
 *     summary: Reset password using token
 *     description: |
 *       Reset user password using the secure token received via email.
 *
 *       **Security Features:**
 *       - Token validation (not expired, not used, exists in database)
 *       - Strong password requirements enforced
 *       - Atomic database operation (password + token update together)
 *       - Account status verification (must be active)
 *       - Secure password hashing with bcrypt
 *       - Token invalidation after successful use
 *       - Confirmation email sent after successful reset
 *
 *       **Password Requirements:**
 *       - Minimum 8 characters length
 *       - At least one lowercase letter (a-z)
 *       - At least one uppercase letter (A-Z)
 *       - At least one number (0-9)
 *       - No maximum length restriction
 *
 *       **Process Flow:**
 *       1. User provides token from email and new password
 *       2. System validates token (exists, not expired, not used)
 *       3. System validates password strength requirements
 *       4. System verifies associated account is active
 *       5. Password is securely hashed and updated
 *       6. Token is marked as used (prevents reuse)
 *       7. Confirmation email sent to user
 *
 *       **Production Notes:**
 *       - Tokens expire after 1 hour for security
 *       - Each token can only be used once
 *       - All password reset attempts are logged
 *       - Rate limited to prevent brute force attacks
 *       - Supports concurrent reset requests safely
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PasswordResetConfirm'
 *           example:
 *             token: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6"
 *             newPassword: "NewSecurePass123"
 *     responses:
 *       200:
 *         description: Password reset successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Password has been reset successfully. You can now login with your new password."
 *             examples:
 *               success:
 *                 summary: Password reset successful
 *                 value:
 *                   success: true
 *                   message: "Password has been reset successfully. You can now login with your new password."
 *       400:
 *         description: Invalid/expired token, weak password, or account issues
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalid_token:
 *                 summary: Invalid or expired token
 *                 value:
 *                   success: false
 *                   message: "Invalid or expired reset token."
 *               token_used:
 *                 summary: Token already used
 *                 value:
 *                   success: false
 *                   message: "Reset token has already been used."
 *               weak_password:
 *                 summary: Password doesn't meet requirements
 *                 value:
 *                   success: false
 *                   message: "Password must contain at least one lowercase letter, one uppercase letter, and one number"
 *               account_deactivated:
 *                 summary: Account is deactivated
 *                 value:
 *                   success: false
 *                   message: "Account is deactivated. Please contact support."
 *       429:
 *         description: Too many requests - rate limited
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error or email sending failure
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/reset-password",
  authLimiter,
  validateBody(passwordResetConfirmSchema),
  PasswordResetController.resetPassword
);

/**
 * @swagger
 * /api/v1/auth/validate-reset-token/{token}:
 *   get:
 *     summary: Validate password reset token
 *     description: |
 *       Check if a password reset token is valid and not expired.
 *       This endpoint is useful for frontend applications to validate
 *       the token before showing the password reset form to users.
 *
 *       **Validation Checks:**
 *       - Token exists in database
 *       - Token is not expired (within 1 hour of generation)
 *       - Token has not been used previously
 *       - Associated user account is active and exists
 *       - Token format is valid (64-character hex string)
 *
 *       **Use Cases:**
 *       - Frontend token validation before showing reset form
 *       - User experience improvement (early error detection)
 *       - Security verification before allowing password change
 *       - Mobile app token verification
 *       - Progressive web app offline validation
 *
 *       **Production Benefits:**
 *       - Prevents users from filling out forms with invalid tokens
 *       - Provides clear feedback about token status
 *       - Enables better error handling in frontend applications
 *       - Supports single-page application workflows
 *       - Allows for graceful degradation of user experience
 *
 *       **Security Notes:**
 *       - Does not reveal sensitive user information
 *       - Only returns email for display purposes (no other user data)
 *       - Rate limited to prevent token enumeration attacks
 *       - Logs validation attempts for security monitoring
 *     tags: [Authentication]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 64
 *           maxLength: 64
 *           pattern: '^[a-f0-9]{64}$'
 *         description: Password reset token from email (64-character hex string)
 *         example: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a1b2c3d4e5f6"
 *     responses:
 *       200:
 *         description: Token is valid and can be used for password reset
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Token is valid"
 *                 data:
 *                   type: object
 *                   properties:
 *                     valid:
 *                       type: boolean
 *                       example: true
 *                       description: "Indicates if the token is valid for password reset"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                       description: "Email address associated with the token (for display purposes only)"
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T14:30:00.000Z"
 *                       description: "Token expiration timestamp (ISO 8601 format)"
 *             examples:
 *               valid_token:
 *                 summary: Valid token
 *                 value:
 *                   success: true
 *                   message: "Token is valid"
 *                   data:
 *                     valid: true
 *                     email: "<EMAIL>"
 *                     expiresAt: "2024-01-15T14:30:00.000Z"
 *       400:
 *         description: Invalid, expired, or used token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalid_token:
 *                 summary: Token not found or invalid format
 *                 value:
 *                   success: false
 *                   message: "Invalid or expired reset token"
 *               expired_token:
 *                 summary: Token has expired
 *                 value:
 *                   success: false
 *                   message: "Reset token has expired. Please request a new password reset."
 *               used_token:
 *                 summary: Token already used
 *                 value:
 *                   success: false
 *                   message: "Reset token has already been used. Please request a new password reset."
 *               account_deactivated:
 *                 summary: Associated account is deactivated
 *                 value:
 *                   success: false
 *                   message: "Account is deactivated. Please contact support."
 *       429:
 *         description: Too many requests - rate limited
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Too many requests from this IP, please try again later."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               success: false
 *               message: "Internal server error. Please try again later."
 */
router.get(
  "/validate-reset-token/:token",
  authLimiter,
  PasswordResetController.validateResetToken
);

/**
 * @swagger
 * /api/v1/auth/create-super-admin:
 *   post:
 *     summary: Create Super Admin (Initial Setup Only)
 *     description: |
 *       Creates the initial Super Admin user for the system. This endpoint can only be used once.
 *       After a Super Admin is created, this endpoint will return an error.
 *
 *       **Security Notes:**
 *       - This endpoint should only be used during initial system setup
 *       - Only one Super Admin can exist in the system
 *       - Consider disabling this endpoint in production after initial setup
 *       - No authentication required for initial setup
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Super Admin email address
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 description: Strong password for Super Admin
 *                 example: "SecurePassword123!"
 *               firstName:
 *                 type: string
 *                 minLength: 1
 *                 description: Super Admin first name
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 minLength: 1
 *                 description: Super Admin last name
 *                 example: "Admin"
 *     responses:
 *       201:
 *         description: Super Admin created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Super Admin created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Super Admin user ID
 *                       example: "6877a5f56bc70e597c5ed79f"
 *                     email:
 *                       type: string
 *                       description: Super Admin email
 *                       example: "<EMAIL>"
 *       400:
 *         description: Validation error or Super Admin already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: User with email already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create-super-admin",
  validateBody(createSuperAdminSchema),
  AuthController.createSuperAdmin
);

// Protected routes - everything after this requires authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/auth/profile:
 *   get:
 *     summary: Get user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Profile retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/profile", AuthController.getProfile);

/**
 * @swagger
 * /api/v1/auth/change-password:
 *   post:
 *     summary: Change user password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 example: "currentSecurePassword123"
 *               newPassword:
 *                 type: string
 *                 example: "newSecurePassword456"
 *             required:
 *               - currentPassword
 *               - newPassword
 *     responses:
 *       200:
 *         description: Password changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid current password or validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/change-password", AuthController.changePassword);

// Admin only routes
/**
 * @swagger
 * /api/v1/auth/create-user:
 *   post:
 *     summary: Create a new user (Super Admin only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserRequest'
 *           example:
 *             email: "<EMAIL>"
 *             password: "newuser123456"
 *             firstName: "New"
 *             lastName: "User"
 *             role: "ACCOUNT_OFFICER"
 *             monthlyTarget: 50000
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     email:
 *                       type: string
 *       400:
 *         description: Validation error or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create-user",
  requireSuperAdmin,
  validateBody(createUserSchema),
  AuthController.createUser
);

/**
 * @swagger
 * /api/v1/auth/users/{userId}/deactivate:
 *   patch:
 *     summary: Deactivate a user (Super Admin only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to deactivate
 *     responses:
 *       200:
 *         description: User deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/users/:userId/deactivate",
  requireSuperAdmin,
  AuthController.deactivateUser
);

/**
 * @swagger
 * /api/v1/auth/users/{userId}/activate:
 *   patch:
 *     summary: Activate a user (Super Admin only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user to activate
 *     responses:
 *       200:
 *         description: User activated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.patch(
  "/users/:userId/activate",
  requireSuperAdmin,
  AuthController.activateUser
);

export default router;
