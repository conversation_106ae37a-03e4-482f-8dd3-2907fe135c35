import nodemailer from "nodemailer";

export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

export class EmailService {
  private static getTransporter() {
    // Check if email is configured
    if (
      !process.env.SMTP_HOST ||
      !process.env.SMTP_USER ||
      !process.env.SMTP_PASS
    ) {
      console.warn("⚠️  Email not configured. OTPs will be logged to console.");
      return null;
    }

    // Gmail configuration
    if (process.env.SMTP_HOST === "smtp.gmail.com") {
      return nodemailer.createTransport({
        service: "gmail",
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS, // Use App Password, not regular password
        },
      });
    }

    // Generic SMTP configuration
    return nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || "587"),
      secure: process.env.SMTP_PORT === "465", // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
      tls: {
        rejectUnauthorized: false, // Allow self-signed certificates
      },
    });
  }

  static async sendEmail(options: EmailOptions): Promise<void> {
    const transporter = this.getTransporter();

    // If email is not configured, log to console instead
    if (!transporter) {
      console.log("📧 Email would be sent to:", options.to);
      console.log("📝 Subject:", options.subject);
      if (options.text) console.log("📝 Content:", options.text);
      return;
    }

    try {
      await transporter.sendMail({
        from: `${process.env.FROM_NAME || "AMS Loan System"} <${
          process.env.FROM_EMAIL || process.env.SMTP_USER
        }>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      });
      console.log("✅ Email sent successfully to:", options.to);
    } catch (error) {
      console.error("❌ Email sending failed:", error);
      // Don't throw error in development - just log it
      if (process.env.NODE_ENV === "production") {
        throw new Error("Failed to send email");
      } else {
        console.warn(
          "⚠️  Email failed in development mode - continuing without email"
        );
      }
    }
  }

  static async sendOTP(email: string, otp: string): Promise<void> {
    const subject = "Your OTP Code - AMS Loan System";
    const text = `Your OTP code is: ${otp}. This code will expire in 5 minutes.`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Your OTP Code</h2>
        <p>Your One-Time Password (OTP) for AMS Loan System is:</p>
        <div style="background-color: #f4f4f4; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="color: #007bff; font-size: 32px; margin: 0;">${otp}</h1>
        </div>
        <p>This OTP will expire in 5 minutes.</p>
        <p>If you didn't request this OTP, please ignore this email.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">AMS Loan Management System</p>
      </div>
    `;

    // Log OTP to console for development
    console.log(`🔐 OTP for ${email}: ${otp}`);

    await this.sendEmail({ to: email, subject, text, html });
  }

  // REMOVED: Transaction notification emails
  // Transaction workflow emails have been disabled - only authentication emails are sent
  static async sendTransactionNotification(
    email: string,
    transactionId: string,
    status: string,
    message: string
  ): Promise<void> {
    console.log(
      `📧 [DISABLED] Transaction notification email would be sent to: ${email}`
    );
    console.log(
      `📝 [DISABLED] Subject: Transaction ${status} - ${transactionId}`
    );
    console.log(`📝 [DISABLED] Message: ${message}`);
    // Email sending disabled for transaction notifications
    return;
  }

  // REMOVED: Approval notification emails
  // Transaction workflow emails have been disabled - only authentication emails are sent
  static async sendApprovalNotification(
    email: string,
    transactionId: string,
    approverRole: string,
    requestedAmount: number,
    customerName?: string
  ): Promise<void> {
    console.log(
      `📧 [DISABLED] Approval notification email would be sent to: ${email}`
    );
    console.log(
      `📝 [DISABLED] Subject: Action Required: Transaction ${transactionId} Pending Your Approval`
    );
    console.log(
      `📝 [DISABLED] Role: ${approverRole}, Amount: ${requestedAmount}, Customer: ${
        customerName || "N/A"
      }`
    );
    // Email sending disabled for approval notifications
    return;
  }

  // REMOVED: Bulk approval notification emails
  // Transaction workflow emails have been disabled - only authentication emails are sent
  static async sendBulkApprovalNotifications(
    users: Array<{ email: string; firstName: string; lastName: string }>,
    transactionId: string,
    approverRole: string,
    requestedAmount: number,
    customerName?: string
  ): Promise<void> {
    console.log(
      `📧 [DISABLED] Bulk approval notifications would be sent to ${users.length} users for transaction ${transactionId}`
    );
    console.log(
      `📝 [DISABLED] Role: ${approverRole}, Amount: ${requestedAmount}, Customer: ${
        customerName || "N/A"
      }`
    );
    console.log(
      `📝 [DISABLED] Recipients: ${users
        .map((u) => `${u.firstName} ${u.lastName} (${u.email})`)
        .join(", ")}`
    );
    // Email sending disabled for bulk approval notifications
    return;
  }

  /**
   * Send password reset email
   */
  static async sendPasswordResetEmail(options: {
    to: string;
    firstName: string;
    resetUrl: string;
    expiresAt: Date;
  }): Promise<void> {
    const { to, firstName, resetUrl, expiresAt } = options;

    const subject = "Password Reset Request - AMS Loan System";

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background-color: #f8f9fa; }
          .button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
          }
          .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>

          <div class="content">
            <h2>Hello ${firstName},</h2>

            <p>We received a request to reset your password for your AMS Loan System account.</p>

            <p>Click the button below to reset your password:</p>

            <div style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </div>

            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 3px;">
              ${resetUrl}
            </p>

            <div class="warning">
              <strong>⚠️ Important Security Information:</strong>
              <ul>
                <li>This link will expire on ${expiresAt.toLocaleString()}</li>
                <li>If you didn't request this password reset, please ignore this email</li>
                <li>Never share this link with anyone</li>
                <li>For security, this link can only be used once</li>
              </ul>
            </div>

            <p>If you're having trouble clicking the button, copy and paste the URL above into your web browser.</p>

            <p>If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.</p>
          </div>

          <div class="footer">
            <p>This is an automated message from AMS Loan System. Please do not reply to this email.</p>
            <p>If you need assistance, please contact your system administrator.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Password Reset Request - AMS Loan System

      Hello ${firstName},

      We received a request to reset your password for your AMS Loan System account.

      Please click the following link to reset your password:
      ${resetUrl}

      This link will expire on ${expiresAt.toLocaleString()}.

      If you didn't request this password reset, please ignore this email.

      For security reasons, this link can only be used once and will expire in 1 hour.

      If you need assistance, please contact your system administrator.
    `;

    await this.sendEmail({
      to,
      subject,
      html,
      text,
    });

    console.log(`📧 Password reset email sent to ${to}`);
  }

  /**
   * Send password reset confirmation email
   */
  static async sendPasswordResetConfirmationEmail(options: {
    to: string;
    firstName: string;
  }): Promise<void> {
    const { to, firstName } = options;

    const subject = "Password Reset Successful - AMS Loan System";

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset Successful</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background-color: #f8f9fa; }
          .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
          }
          .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Password Reset Successful</h1>
          </div>

          <div class="content">
            <h2>Hello ${firstName},</h2>

            <div class="success">
              <strong>Your password has been successfully reset!</strong>
            </div>

            <p>Your AMS Loan System account password has been changed successfully. You can now log in with your new password.</p>

            <p><strong>What happens next:</strong></p>
            <ul>
              <li>You can now log in to the system with your new password</li>
              <li>All previous password reset tokens have been invalidated</li>
              <li>Your account security has been updated</li>
            </ul>

            <p><strong>Security Notice:</strong></p>
            <p>If you did not make this change, please contact your system administrator immediately as your account may have been compromised.</p>

            <p>For your security, we recommend:</p>
            <ul>
              <li>Using a strong, unique password</li>
              <li>Not sharing your login credentials with anyone</li>
              <li>Logging out when you're finished using the system</li>
            </ul>
          </div>

          <div class="footer">
            <p>This is an automated message from AMS Loan System. Please do not reply to this email.</p>
            <p>If you need assistance, please contact your system administrator.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Password Reset Successful - AMS Loan System

      Hello ${firstName},

      Your password has been successfully reset!

      Your AMS Loan System account password has been changed successfully. You can now log in with your new password.

      If you did not make this change, please contact your system administrator immediately.

      For your security, we recommend using a strong, unique password and not sharing your login credentials with anyone.

      If you need assistance, please contact your system administrator.
    `;

    await this.sendEmail({
      to,
      subject,
      html,
      text,
    });

    console.log(`📧 Password reset confirmation email sent to ${to}`);
  }
}
