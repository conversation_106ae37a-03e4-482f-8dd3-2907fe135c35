import { Response } from "express";
import { ProgressTrackingService } from "../services/progressTrackingService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";

export class ProgressTrackingController {
  /**
   * Get transaction progress tracking data
   * GET /api/v1/transactions/:transactionId/progress
   */
  static getTransactionProgress = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log("Getting transaction progress:", { transactionId, userId, userRole });

      const progressData = await ProgressTrackingService.getTransactionProgress(
        transactionId,
        userId,
        userRole
      );

      // Add next action to response
      const nextAction = ProgressTrackingService.getNextAction({
        status: progressData.currentStatus,
        currentStage: progressData.currentStage,
      });

      const response = {
        ...progressData,
        statusLabel: ProgressTrackingService.getStatusLabel(progressData.currentStatus),
        nextAction,
      };

      ResponseHandler.success(
        res,
        "Transaction progress retrieved successfully",
        response
      );
    }
  );

  /**
   * Get multiple transactions progress (for dashboard/list views)
   * POST /api/v1/transactions/progress/bulk
   */
  static getBulkTransactionProgress = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionIds } = req.body;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      if (!Array.isArray(transactionIds) || transactionIds.length === 0) {
        return ResponseHandler.error(
          res,
          "Transaction IDs array is required",
          undefined,
          400
        );
      }

      if (transactionIds.length > 50) {
        return ResponseHandler.error(
          res,
          "Maximum 50 transactions can be processed at once",
          undefined,
          400
        );
      }

      console.log("Getting bulk transaction progress:", { 
        count: transactionIds.length, 
        userId, 
        userRole 
      });

      // Process all transactions in parallel
      const progressPromises = transactionIds.map(async (transactionId: string) => {
        try {
          const progressData = await ProgressTrackingService.getTransactionProgress(
            transactionId,
            userId,
            userRole
          );

          const nextAction = ProgressTrackingService.getNextAction({
            status: progressData.currentStatus,
            currentStage: progressData.currentStage,
          });

          return {
            transactionId,
            success: true,
            data: {
              ...progressData,
              statusLabel: ProgressTrackingService.getStatusLabel(progressData.currentStatus),
              nextAction,
            },
          };
        } catch (error: any) {
          console.error(`Error getting progress for ${transactionId}:`, error.message);
          return {
            transactionId,
            success: false,
            error: error.message,
          };
        }
      });

      const results = await Promise.all(progressPromises);
      
      // Separate successful and failed results
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      ResponseHandler.success(
        res,
        `Progress retrieved for ${successful.length}/${transactionIds.length} transactions`,
        {
          successful: successful.map(r => r.data),
          failed: failed.map(r => ({ 
            transactionId: r.transactionId, 
            error: r.error 
          })),
          summary: {
            total: transactionIds.length,
            successful: successful.length,
            failed: failed.length,
          },
        }
      );
    }
  );
}
