import {
  S3<PERSON>lient,
  PutObjectCommand,
  GetO<PERSON>Command,
  DeleteObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { Readable } from "stream";
import crypto from "crypto";
import path from "path";

export interface R2UploadResult {
  key: string;
  url: string;
  bucket: string;
  fileSize: number;
  contentType: string;
  etag: string;
}

export interface R2FileInfo {
  key: string;
  url: string;
  bucket: string;
  size: number;
  contentType: string;
  lastModified: Date;
  etag: string;
}

export class R2Service {
  private static client: S3Client;
  private static bucket: string;
  private static publicUrl: string;
  private static isConfigured: boolean = false;

  /**
   * Initialize R2 client with configuration
   */
  static initialize() {
    const accessKeyId = process.env.R2_ACCESS_KEY_ID;
    const secretAccessKey = process.env.R2_SECRET_ACCESS_KEY;
    const endpoint = process.env.R2_ENDPOINT;
    const bucketName = process.env.R2_BUCKET_NAME;
    const region = process.env.R2_REGION || "auto";

    if (!accessKeyId || !secretAccessKey || !endpoint || !bucketName) {
      throw new Error(
        "R2 configuration missing. Please check R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, R2_ENDPOINT, and R2_BUCKET_NAME environment variables."
      );
    }

    this.client = new S3Client({
      region,
      endpoint,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      forcePathStyle: true, // Required for R2
    });

    this.bucket = bucketName;
    this.publicUrl =
      process.env.R2_PUBLIC_URL ||
      endpoint.replace("https://", `https://${bucketName}.`);
    this.isConfigured = true;
  }

  /**
   * Check if R2 is properly configured
   */
  static isR2Configured(): boolean {
    return this.isConfigured;
  }

  /**
   * Upload a file to R2
   */
  static async uploadFile(
    file: Express.Multer.File,
    transactionId: string,
    customFileName?: string
  ): Promise<R2UploadResult> {
    if (!this.client) {
      this.initialize();
    }

    try {
      // Generate unique filename
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(8).toString("hex");
      const fileExtension = path.extname(file.originalname);
      const sanitizedOriginalName = file.originalname
        .replace(/[^a-zA-Z0-9.-]/g, "_")
        .substring(0, 100);

      const fileName =
        customFileName ||
        `${timestamp}_${randomString}_${sanitizedOriginalName}`;

      // Create the key (path) for the file
      const key = `ams-loan-docs/${transactionId}/${fileName}`;

      // Upload file to R2
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ContentLength: file.size,
        Metadata: {
          originalName: file.originalname,
          transactionId: transactionId,
          uploadedAt: new Date().toISOString(),
        },
      });

      const result = await this.client.send(command);

      // Generate public URL
      const url = `${this.publicUrl}/${key}`;

      return {
        key,
        url,
        bucket: this.bucket,
        fileSize: file.size,
        contentType: file.mimetype,
        etag: result.ETag || "",
      };
    } catch (error) {
      console.error("Error uploading file to R2:", error);
      throw new Error(
        `Failed to upload file to R2: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get file information from R2
   */
  static async getFileInfo(key: string): Promise<R2FileInfo> {
    if (!this.client) {
      this.initialize();
    }

    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const result = await this.client.send(command);
      const url = `${this.publicUrl}/${key}`;

      return {
        key,
        url,
        bucket: this.bucket,
        size: result.ContentLength || 0,
        contentType: result.ContentType || "application/octet-stream",
        lastModified: result.LastModified || new Date(),
        etag: result.ETag || "",
      };
    } catch (error) {
      console.error(`Error getting file info for ${key}:`, error);
      throw new Error(
        `Failed to get file info: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Download file from R2 as Buffer
   */
  static async downloadFile(key: string): Promise<Buffer> {
    if (!this.client) {
      this.initialize();
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const result = await this.client.send(command);

      if (!result.Body) {
        throw new Error("No file content received");
      }

      // Convert stream to buffer
      const chunks: Uint8Array[] = [];
      const stream = result.Body as Readable;

      return new Promise((resolve, reject) => {
        stream.on("data", (chunk) => chunks.push(chunk));
        stream.on("error", reject);
        stream.on("end", () => resolve(Buffer.concat(chunks)));
      });
    } catch (error) {
      console.error(`Error downloading file ${key}:`, error);
      throw new Error(
        `Failed to download file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Generate a signed URL for secure access
   */
  static async generateSignedUrl(
    key: string,
    expiresIn: number = 3600
  ): Promise<string> {
    if (!this.client) {
      this.initialize();
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      return await getSignedUrl(this.client, command, { expiresIn });
    } catch (error) {
      console.error(`Error generating signed URL for ${key}:`, error);
      throw new Error(
        `Failed to generate signed URL: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Delete file from R2
   */
  static async deleteFile(key: string): Promise<void> {
    if (!this.client) {
      this.initialize();
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.client.send(command);
    } catch (error) {
      console.error(`Error deleting file ${key}:`, error);
      throw new Error(
        `Failed to delete file: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * List files in a transaction folder
   */
  static async listTransactionFiles(
    transactionId: string
  ): Promise<R2FileInfo[]> {
    if (!this.client) {
      this.initialize();
    }

    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: `ams-loan-docs/${transactionId}/`,
      });

      const result = await this.client.send(command);
      const files: R2FileInfo[] = [];

      if (result.Contents) {
        for (const object of result.Contents) {
          if (object.Key) {
            files.push({
              key: object.Key,
              url: `${this.publicUrl}/${object.Key}`,
              bucket: this.bucket,
              size: object.Size || 0,
              contentType: "application/octet-stream", // R2 doesn't return content type in list
              lastModified: object.LastModified || new Date(),
              etag: object.ETag || "",
            });
          }
        }
      }

      return files;
    } catch (error) {
      console.error(
        `Error listing files for transaction ${transactionId}:`,
        error
      );
      throw new Error(
        `Failed to list files: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get public URL for a file
   */
  static getPublicUrl(key: string): string {
    return `${this.publicUrl}/${key}`;
  }

  /**
   * Extract key from R2 URL
   */
  static extractKeyFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      // Remove leading slash
      return urlObj.pathname.substring(1);
    } catch (error) {
      console.error("Error extracting key from URL:", error);
      return null;
    }
  }

  /**
   * Check if file exists in R2
   */
  static async fileExists(key: string): Promise<boolean> {
    try {
      await this.getFileInfo(key);
      return true;
    } catch (error) {
      return false;
    }
  }
}
