import rateLimit from "express-rate-limit";
import { ResponseHandler } from "../utils/response";

// General rate limiter - Configurable via environment variables
export const generalLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || "900000"), // 15 minutes default
  max: parseInt(process.env.RATE_LIMIT_MAX || "2500"), // Increased to 2000 requests per 15 minutes
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting for certain conditions
  skip: (req) => {
    // Skip rate limiting in development
    if (process.env.NODE_ENV === "development") {
      return true;
    }
    return false;
  },
});

// User-based auth rate limiter - Increased for better UX
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Increased from 50 to 100 per IP
  keyGenerator: (req) => {
    // Use email/username if provided, otherwise fall back to IP
    const identifier = req.body?.email || req.body?.username || req.ip;
    return `auth_${identifier}`;
  },
  message: {
    success: false,
    message: "Too many authentication attempts, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting in development
  skip: (req) => process.env.NODE_ENV === "development",
});

// OTP rate limiter
export const otpLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // limit each IP to 3 OTP requests per windowMs
  message: {
    success: false,
    message: "Too many OTP requests, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// File upload rate limiter - More generous for document operations
export const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Increased from 20 to 100 upload requests per 15 minutes
  message: {
    success: false,
    message: "Too many file upload requests, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting in development
  skip: () => process.env.NODE_ENV === "development",
});

// Document access rate limiter - For document previews and downloads
export const documentLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 300, // Allow 200 document operations per 5 minutes
  message: {
    success: false,
    message: "Too many document requests, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting in development
  skip: () => process.env.NODE_ENV === "development",
});
