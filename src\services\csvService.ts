// Utility function to format dates
function formatDate(date: Date | string, includeTime: boolean = false): string {
  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");

  if (includeTime) {
    const hours = String(d.getHours()).padStart(2, "0");
    const minutes = String(d.getMinutes()).padStart(2, "0");
    const seconds = String(d.getSeconds()).padStart(2, "0");
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  return `${year}-${month}-${day}`;
}

export interface CSVExportData {
  [key: string]: any;
}

export class CSVService {
  /**
   * Generate CSV content for transaction data
   */
  static generateTransactionCSV(transaction: any): string {
    const csvData: CSVExportData[] = [];

    // Basic Transaction Information
    csvData.push({
      Section: "Transaction Information",
      Field: "Transaction ID",
      Value: transaction.transactionId || "",
      Details: "",
    });

    csvData.push({
      Section: "Transaction Information",
      Field: "Status",
      Value: transaction.status || "",
      Details: "",
    });

    csvData.push({
      Section: "Transaction Information",
      Field: "Current Stage",
      Value: transaction.currentStage || "",
      Details: "",
    });

    csvData.push({
      Section: "Transaction Information",
      Field: "Loan Type",
      Value: transaction.loanTypeConfig?.name || transaction.loanType || "",
      Details: "",
    });

    csvData.push({
      Section: "Transaction Information",
      Field: "Created Date",
      Value: transaction.createdAt
        ? formatDate(transaction.createdAt, true)
        : "",
      Details: "",
    });

    csvData.push({
      Section: "Transaction Information",
      Field: "Updated Date",
      Value: transaction.updatedAt
        ? formatDate(transaction.updatedAt, true)
        : "",
      Details: "",
    });

    // Personal Information
    if (transaction.firstName || transaction.lastName) {
      csvData.push({
        Section: "Personal Information",
        Field: "Full Name",
        Value: `${transaction.firstName || ""} ${
          transaction.middleName || ""
        } ${transaction.lastName || ""}`.trim(),
        Details: "",
      });
    }

    if (transaction.email) {
      csvData.push({
        Section: "Personal Information",
        Field: "Email",
        Value: transaction.email,
        Details: "",
      });
    }

    if (transaction.phoneNumber) {
      csvData.push({
        Section: "Personal Information",
        Field: "Phone Number",
        Value: transaction.phoneNumber,
        Details: "",
      });
    }

    if (transaction.dateOfBirth) {
      csvData.push({
        Section: "Personal Information",
        Field: "Date of Birth",
        Value: formatDate(transaction.dateOfBirth, false),
        Details: "",
      });
    }

    if (transaction.gender) {
      csvData.push({
        Section: "Personal Information",
        Field: "Gender",
        Value: transaction.gender,
        Details: "",
      });
    }

    if (transaction.maritalStatus) {
      csvData.push({
        Section: "Personal Information",
        Field: "Marital Status",
        Value: transaction.maritalStatus,
        Details: "",
      });
    }

    if (transaction.bvn) {
      csvData.push({
        Section: "Personal Information",
        Field: "BVN",
        Value: transaction.bvn,
        Details: "",
      });
    }

    if (transaction.nin) {
      csvData.push({
        Section: "Personal Information",
        Field: "NIN",
        Value: transaction.nin,
        Details: "",
      });
    }

    // Address Information
    const address = [
      transaction.street,
      transaction.city,
      transaction.state,
      transaction.postalCode,
    ]
      .filter(Boolean)
      .join(", ");

    if (address) {
      csvData.push({
        Section: "Personal Information",
        Field: "Address",
        Value: address,
        Details: "",
      });
    }

    // Employment Information
    if (transaction.organizationName) {
      csvData.push({
        Section: "Employment Information",
        Field: "Organization Name",
        Value: transaction.organizationName,
        Details: "",
      });
    }

    if (transaction.ippisNumber) {
      csvData.push({
        Section: "Employment Information",
        Field: "IPPIS Number",
        Value: transaction.ippisNumber,
        Details: "",
      });
    }

    if (transaction.employmentDate) {
      csvData.push({
        Section: "Employment Information",
        Field: "Employment Date",
        Value: formatDate(transaction.employmentDate, false),
        Details: "",
      });
    }

    // Loan Information
    if (transaction.requestedAmount) {
      csvData.push({
        Section: "Loan Information",
        Field: "Requested Amount",
        Value: transaction.requestedAmount.toLocaleString(),
        Details: "NGN",
      });
    }

    if (transaction.loanTenor) {
      csvData.push({
        Section: "Loan Information",
        Field: "Loan Tenor",
        Value: transaction.loanTenor.toString(),
        Details: "months",
      });
    }

    if (transaction.repaymentMode) {
      csvData.push({
        Section: "Loan Information",
        Field: "Repayment Mode",
        Value: transaction.repaymentMode,
        Details: "",
      });
    }

    if (transaction.grossPay) {
      csvData.push({
        Section: "Loan Information",
        Field: "Gross Pay",
        Value: transaction.grossPay.toLocaleString(),
        Details: "NGN",
      });
    }

    if (transaction.netPay) {
      csvData.push({
        Section: "Loan Information",
        Field: "Net Pay",
        Value: transaction.netPay.toLocaleString(),
        Details: "NGN",
      });
    }

    if (transaction.purposeOfLoan) {
      csvData.push({
        Section: "Loan Information",
        Field: "Purpose of Loan",
        Value: transaction.purposeOfLoan,
        Details: "",
      });
    }

    // Disbursement Information
    if (
      transaction.accountName ||
      transaction.accountNumber ||
      transaction.bankName
    ) {
      csvData.push({
        Section: "Disbursement Information",
        Field: "Account Name",
        Value: transaction.accountName || "",
        Details: "",
      });

      csvData.push({
        Section: "Disbursement Information",
        Field: "Account Number",
        Value: transaction.accountNumber || "",
        Details: "",
      });

      csvData.push({
        Section: "Disbursement Information",
        Field: "Bank Name",
        Value: transaction.bankName || "",
        Details: "",
      });
    }

    // Document Information
    if (transaction.documents && transaction.documents.length > 0) {
      transaction.documents.forEach((doc: any, index: number) => {
        csvData.push({
          Section: "Documents",
          Field: `Document ${index + 1} - Name`,
          Value: doc.originalName || doc.fileName || "",
          Details: "",
        });

        csvData.push({
          Section: "Documents",
          Field: `Document ${index + 1} - Type`,
          Value: doc.fileType || "",
          Details: "",
        });

        csvData.push({
          Section: "Documents",
          Field: `Document ${index + 1} - Size`,
          Value: doc.fileSize ? `${(doc.fileSize / 1024).toFixed(2)} KB` : "",
          Details: "",
        });

        csvData.push({
          Section: "Documents",
          Field: `Document ${index + 1} - Upload Date`,
          Value: doc.uploadedAt ? formatDate(doc.uploadedAt, true) : "",
          Details: "",
        });
      });
    }

    // Approval History
    if (transaction.approvals && transaction.approvals.length > 0) {
      transaction.approvals.forEach((approval: any, index: number) => {
        csvData.push({
          Section: "Approval History",
          Field: `Approval ${index + 1} - Stage`,
          Value: approval.stage || "",
          Details: "",
        });

        csvData.push({
          Section: "Approval History",
          Field: `Approval ${index + 1} - Action`,
          Value: approval.action || "",
          Details: "",
        });

        csvData.push({
          Section: "Approval History",
          Field: `Approval ${index + 1} - Approver`,
          Value: approval.approver
            ? `${approval.approver.firstName} ${approval.approver.lastName}`
            : "",
          Details: approval.approver?.role || "",
        });

        csvData.push({
          Section: "Approval History",
          Field: `Approval ${index + 1} - Comments`,
          Value: approval.comments || "",
          Details: "",
        });

        csvData.push({
          Section: "Approval History",
          Field: `Approval ${index + 1} - Date`,
          Value: approval.createdAt ? formatDate(approval.createdAt, true) : "",
          Details: "",
        });
      });
    }

    // Created By Information
    if (transaction.createdBy) {
      csvData.push({
        Section: "System Information",
        Field: "Created By",
        Value: `${transaction.createdBy.firstName} ${transaction.createdBy.lastName}`,
        Details: transaction.createdBy.email || "",
      });
    }

    return this.convertToCSV(csvData);
  }

  /**
   * Convert array of objects to CSV string
   */
  private static convertToCSV(data: CSVExportData[]): string {
    if (data.length === 0) {
      return "";
    }

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(",");

    const csvRows = data.map((row) => {
      return headers
        .map((header) => {
          const value = row[header];
          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes('"') || value.includes("\n"))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || "";
        })
        .join(",");
    });

    return [csvHeaders, ...csvRows].join("\n");
  }

  /**
   * Generate filename for CSV export
   */
  static generateCSVFilename(transactionId: string): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");
    const timestamp = `${year}${month}${day}-${hours}${minutes}${seconds}`;
    return `transaction-${transactionId}-${timestamp}.csv`;
  }
}
