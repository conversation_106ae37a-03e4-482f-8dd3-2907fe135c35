import multer from "multer";
import path from "path";
import { FileType } from "@prisma/client";
import { R2Service } from "../services/r2Service";

// Configure multer to use memory storage for R2
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (
  req: any,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  const allowedTypes = (
    process.env.ALLOWED_FILE_TYPES || "pdf,jpg,jpeg,png,doc,docx"
  ).split(",");
  const fileExtension = path.extname(file.originalname).toLowerCase().slice(1);

  if (allowedTypes.includes(fileExtension)) {
    cb(null, true);
  } else {
    cb(
      new Error(
        `File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(
          ", "
        )}`
      )
    );
  }
};

// Configure multer
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || "10485760"), // 10MB default
  },
});

export class FileUploadUtils {
  static getFileType(mimeType: string): FileType {
    if (mimeType === "application/pdf") {
      return FileType.PDF;
    } else if (mimeType.startsWith("image/")) {
      return FileType.IMAGE;
    } else {
      return FileType.DOCUMENT;
    }
  }

  /**
   * Delete file from R2 using key
   */
  static async deleteFile(r2Key: string): Promise<void> {
    try {
      await R2Service.deleteFile(r2Key);
    } catch (error) {
      console.error("Error deleting file from R2:", error);
    }
  }

  /**
   * Get file URL from R2 public URL (already a full URL)
   */
  static getFileUrl(publicUrl: string): string {
    return publicUrl;
  }

  /**
   * Get signed URL for secure access (R2 equivalent of optimized URL)
   */
  static async getSignedFileUrl(
    r2Key: string,
    expiresIn: number = 3600
  ): Promise<string> {
    try {
      return await R2Service.generateSignedUrl(r2Key, expiresIn);
    } catch (error) {
      console.error("Error generating signed URL:", error);
      // Fallback to public URL
      return R2Service.getPublicUrl(r2Key);
    }
  }

  static validateFileSize(size: number): boolean {
    const maxSize = parseInt(process.env.MAX_FILE_SIZE || "10485760");
    return size <= maxSize;
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Upload file to R2
   */
  static async uploadToR2(
    file: Express.Multer.File,
    transactionId: string
  ): Promise<{
    key: string;
    url: string;
    bucket: string;
    fileSize: number;
    etag: string;
  }> {
    const result = await R2Service.uploadFile(file, transactionId);

    return {
      key: result.key,
      url: result.url,
      bucket: result.bucket,
      fileSize: result.fileSize,
      etag: result.etag,
    };
  }

  /**
   * Legacy method for backward compatibility - now uses R2
   * @deprecated Use uploadToR2 instead
   */
  static async uploadToCloudinary(
    file: Express.Multer.File,
    transactionId: string
  ): Promise<{
    publicId: string;
    secureUrl: string;
    fileSize: number;
  }> {
    const result = await this.uploadToR2(file, transactionId);

    return {
      publicId: result.key, // R2 key maps to publicId
      secureUrl: result.url, // R2 URL maps to secureUrl
      fileSize: result.fileSize,
    };
  }
}
