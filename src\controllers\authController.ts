import { Request, Response } from "express";
import { AuthService } from "../services/authService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";

export class AuthController {
  static login = asyncHandler(async (req: Request, res: Response) => {
    const { email, password } = req.body;

    const result = await AuthService.login({ email, password });

    ResponseHandler.success(res, result.message, {
      requiresOTP: result.requiresOTP,
    });
  });

  static verifyOTP = asyncHandler(async (req: Request, res: Response) => {
    const { email, otp } = req.body;

    const result = await AuthService.verifyOTP(email, otp);

    ResponseHandler.success(res, "Login successful", result);
  });

  static refreshToken = asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return ResponseHandler.error(res, "Refresh token is required");
    }

    const result = await AuthService.refreshToken(refreshToken);

    ResponseHandler.success(res, "Token refreshed successfully", result);
  });

  static createUser = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userData = req.body;

      const result = await AuthService.createUser(userData);

      ResponseHandler.success(res, "User created successfully", result, 201);
    }
  );

  static changePassword = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user!.id;

      await AuthService.changePassword(userId, currentPassword, newPassword);

      ResponseHandler.success(res, "Password changed successfully");
    }
  );

  static getProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const user = req.user!;

      ResponseHandler.success(res, "Profile retrieved successfully", user);
    }
  );

  static deactivateUser = asyncHandler(async (req: Request, res: Response) => {
    const { userId } = req.params;

    await AuthService.deactivateUser(userId);

    ResponseHandler.success(res, "User deactivated successfully");
  });

  static activateUser = asyncHandler(async (req: Request, res: Response) => {
    const { userId } = req.params;

    await AuthService.activateUser(userId);

    ResponseHandler.success(res, "User activated successfully");
  });

  static createSuperAdmin = asyncHandler(
    async (req: Request, res: Response) => {
      const { email, password, firstName, lastName } = req.body;

      const result = await AuthService.createSuperAdmin({
        email,
        password,
        firstName,
        lastName,
      });

      ResponseHandler.success(res, result.message, {
        id: result.id,
        email: result.email,
      });
    }
  );
}
